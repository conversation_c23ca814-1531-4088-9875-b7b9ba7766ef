# Mobile app development

- [Prerequisites][1]
- [Architecture][2]
- [Running the app][3]
- [Environment variables][4]
- [Flows][5]
- [Push notifications][24]

## Prerequisites

To be able to develop for mobile we need to install some extra applications:

- [Xcode][6] 12+
- [Android studio][7]
- [Cocoapods][8]

We need these applications to run app-based containers (locally) on
emulators/devices.

## Architecture

### Capacitor

Mobile apps are setup using [Capacitor][9].

Notice the `capacitor.config.ts` file in the root of containers that are app
based.

### NX Integration

Capacitor is integrated with the Nx monorepository setup using native NX
executors. The `project.json` file in the root of app-based containers contains
targets that use `nx:run-commands` to execute Capacitor CLI commands directly.
This approach provides better control and eliminates dependency on third-party
plugins.

### Next.js

The app is build using the [static HTML export][11] functionality from Next.js.
This static export is picked up by capacitor as its `webDir`, integrating the
export with the mobile platforms (iOS, and Android)

## Creating a native project

### Container generator

The [Nx generator][12] for containers can be used to create a new native
project. This includes a basic setup, but does not yet support running the
project on a platform (i.e. iOS or Android).

### Adding a platform

Run the Capacitor add command in the root folder of the project to copy the
native platform template into your project.

    npx nx run [container-name]:add:[ios|android]

### Syncing

Use the sync command to perform an Ionic build and copy the web assets to the
native platforms. This can be used to run a local production build.

    npx nx run [container-name]:sync:[ios|android]

## Running the app

For mobile development we have different options for running our app. Depending
on the required platform and type of development, you can choose the best
option:

### `nx serve`

To run the app container locally in the browser we can use:

    npx nx serve [app-container-name]
    npx nx serve be-eneco-insights

This will run a [Next.js server][13] as you could run any other Next.js web app.

### Local production build

To make a (local) production build for the mobile app we need a few steps:

Change in `.env` file the line for an empty value:

    CAPACITOR_CONFIG_SERVER_URL=

Make sure in the `.env` file to disable mocks

    FE_AUTH_MOCKS=0
    FE_SITECORE_MOCKS=0
    FE_DC_MOCKS=0

Perform the Next.js [static HTML export][14]:

    npx nx export [app-container-name] --verbose
    npx nx export be-eneco-insights --verbose

Sync the production build to the apps:

    npx nx run [app-container-name]:sync
    npx nx run [app-container-name]:sync:[ios|android]
    npx nx run be-eneco-insights:sync:android --verbose

_or immediately run after sync using:_

    npx nx run [app-container-name]:run:[ios|android]
    npx nx run be-eneco-insights:run:android

## Releasing a native project

### Pipelines

- Add a job in the Prepare stage (in the release-app-manual.yaml and
  release-app-prod.yaml file).

### Azure DevOps

- Create a variable group in Azure DevOps (enecomanagedcloud/BTO Digital
  Solutions - DXP/Pipelines/Library) using the container name postfixed by the
  environment (i.e. be-eneco-insights-acc). Make sure to set the
  `CAPACITOR_CONFIG_SERVER_URL` to an empty string.
- Allow the corresponding app pipeline to access the created variable group at
  the pipeline permissions tab.

### App stores

TODO: document app store release process.

### DevTools

#### Android

In Chrome, the DevTools can be used to inspect the Android emulator. Go to
[chrome://inspect/#devices][16] and find a link "inspect" with the running
emulator.

#### iOS

In Safari, the DevTools can be used to inspect the iOS emulator. Open Safari go
to `Developer -> <YOUR DEVICE NAME> -> Select Inspectable Application`

### Live reloading

If we want to run the app on a mobile device/emulator (not the browser) but we
don't want to make a production build, we can use the server attribute in the
`capacitor-config.json` to accomplish this. We need to carry out three steps:

1\. Run our app locally using the serve command

    nx serve [app-container-name] --port=8888 --hostname=0.0.0.0

2\. Make sure this environment variable in your [.env file][17] points to your
local server:

    CAPACITOR_CONFIG_SERVER_URL=http://***********:8888

3\. Run your desired platform:

    nx run [app-container-name]:run:[ios|android]

💡 \_If you grab your own IP address (from the machine you work on) it will fix
the `ERR_CONNECTION_REFUSED` issues in Android, both for the simulator as the
physical device read more about this [using framework CLI's][18]

## Environment variables

Make sure to have or set the `JAVA_HOME` and `ANDROID_SDK_ROOT` environment
variables. Adjust for your environment:

    export JAVA_HOME=/Applications/Android\ Studio.app/Contents/jre/Contents/Home
    export ANDROID_SDK_ROOT=$HOME/Library/Android/sdk

## Flows

### FlowStateContainer

Mobile flows, for example the EnergyProfile flow, are built with the help of
Xstate. To make sure Xstate is connected with the history api, the
FlowStateContainer can be used.

The following pattern needs to be followed to make full use of this component:

- The `historyStates` parameter needs to list any state you want to connect to
  `historyStates=[OVERVIEW, SETTINGS, ...]`
- A url parameter is added to the url according to the corresponding state
  `&state=OVERVIEW`

The following actions are executed when a history state changes is encountered:

- Push a new entry to the browser history api with the state parameter updated
- Set the state on the given MachineProvider

When the FlowStateContainer encounters a url parameter change (for example the
user clicks on the back or forward button in the browser), the following actions
are executed:

- The state request parameter is converted to an event `&state=OVERVIEW` is
  converted to the event `{ type: TO_OVERVIEW }`
- If the TO_OVERVIEW event is defined in the state machine, the state machine
  will transition to the corresponding state and provide the updated state to
  the given MachineProvider.

## Push Notifications

### Overview

- Eneco BE Insights application uses Airship for push notifications feature and
  heavylifting is handled by [Airship Capacitor SDK][23]
- BE Application Insights Airship instance owner is:
  <EMAIL>

### How it works?

- On first application launch, Airship SDK registers a channelId for a physical
  device (only works via physical devices) and notifications are targeted via
  this unique identifier.
- More info can be found here: [Confluence Push notifications][19]

### Testing it locally:

- Testing locally can be done by building the App on your Android or iOS device.
- Command:
  `npx nx export be-eneco-insights --verbose && npx nx   run be-eneco-insights:sync && npx nx run be-eneco-insights:run:ios`.
- next you can find your channelId in the app-info section (profile) or by doing
  this: Open your app and switch between your app and your device's home screen
  6 times in a row. The Channel ID will be copied to your clipboard, and you can
  paste it anywhere you want.
- After that u can use the channelId in Airship to send out a test.

### iOS

- In order to enable secure communication between AirshipSKD and Apple we use
  [Token Auth][20] setup. The Eneco smart meter app uses this token:
  `Airship Eneco Insights App BE`. You can find this token on [Develop apple
  authkeys][21]. If you do not have access, please contact to mobile app team.

### Android

- For Android we use the Firebase Cloud Messaging (FCM). Which is configured
  using these docs: [Airship FCM setup][22]. This way Airship can communicate
  between their sdk and Android.
- Owner of the Firebase project: <EMAIL>

[1]: #prerequisites
[2]: #architecture
[3]: #running-the-app
[4]: #environment-variables
[5]: #flows
[6]: https://developer.apple.com/download/more/
[7]: https://developer.android.com/studio
[8]: https://capacitorjs.com/docs/getting-started/environment-setup#cocoapods
[9]: https://capacitorjs.com/docs
[11]: https://nextjs.org/docs/advanced-features/static-html-export
[12]: ../new-containers.md#Nx-generator
[13]: https://nx.dev/packages/next/executors/server
[14]: #nextjs
[16]: chrome://inspect/#devices
[17]: ../.env
[18]: https://capacitorjs.com/docs/guides/live-reload#using-with-framework-clis
[19]:
  https://eneco.atlassian.net/wiki/spaces/WEB/pages/********/Push+notifications
[20]:
  https://docs.airship.com/platform/mobile/setup/configure-channels/#ios-channel-configuration
[21]: https://developer.apple.com/account/resources/authkeys/list
[22]:
  https://docs.airship.com/platform/mobile/setup/configure-channels/#android-channel-configuration
[23]: https://docs.airship.com/platform/mobile/setup/sdk/capacitor/
[24]: #push-notifications
