import { Dispatch, SetStateAction, useState, useId } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, FormProvider } from 'react-hook-form';
import * as yup from 'yup';

import postalCodeRegex from '@common/validation/postalCodeRegex';
import AddressFinder from '@components/AddressFinder/AddressFinder';
import RichText from '@components/RichText/RichText';
import { HeatComparisonToolRendering } from '@sitecore/types/HeatComparisonTool';
import { Form, Stack, Heading, Expandable, Stretch, Button, Text } from '@sparky';

import { HeatComparisonDataProps, HEATCOMPARISONSTAGES } from '../../types/heatComparison';

interface InputAddressStageProps {
  setStage: Dispatch<SetStateAction<string>>;
  setCalculatorData: Dispatch<SetStateAction<HeatComparisonDataProps>>;
  calculatorData: HeatComparisonDataProps;
  textualData: HeatComparisonToolRendering;
}

interface FormFieldsStageOne {
  postalCode: string;
  houseNumber: number;
  houseNumberSuffix?: string;
  city: string;
  street: string;
  isMoving?: boolean;
}

export const InputAddressStage: React.FC<InputAddressStageProps> = ({
  setStage,
  setCalculatorData,
  calculatorData,
  textualData,
}) => {
  const id = `${useId()}-${textualData.fields.address.moreInfoText.value.replaceAll(' ', '-')}`;
  const [isHouseNumberSuffixRequired, setIsHouseNumberSuffixRequired] = useState(false);

  const formSchema = yup.object({
    postalCode: yup.string().required('invullen maat').matches(postalCodeRegex),
    houseNumber: yup.number().required('invullen maat'),
    houseNumberSuffix: yup.string().optional(),

    street: yup.string().required(),
    city: yup.string().required(),
    isMoving: yup.boolean(),
  });

  const formMethods = useForm<FormFieldsStageOne>({
    mode: 'onBlur',
    defaultValues: {},

    resolver: yupResolver(formSchema),
  });

  const { handleSubmit } = formMethods;

  const onSubmit = (formData: FormFieldsStageOne) => {
    setCalculatorData({ ...calculatorData, ...formData });
    return setStage(HEATCOMPARISONSTAGES.USAGE_DETAILS);
  };

  return (
    <FormProvider {...formMethods}>
      <Form onSubmit={handleSubmit(onSubmit)}>
        <Stack gap="6">
          <Heading size={{ initial: 'S', md: 'L' }} as="h1">
            {textualData.fields.content.title.value}
          </Heading>

          <Text>{textualData.fields.address.content.value}</Text>
          <Stack.Item>
            <AddressFinder
              isSuffixRequired={isHouseNumberSuffixRequired}
              setIsSuffixRequired={setIsHouseNumberSuffixRequired}
              customLabels={{
                houseNumber: textualData?.fields?.address?.houseNumberFormField,
                postalCode: textualData?.fields?.address?.postalCodeFormField,
                houseNumberSuffix: textualData?.fields?.address?.houseNumberSuffixFormField,
              }}
            />
          </Stack.Item>
          <Expandable>
            <Stretch width={false}>
              <Expandable.Trigger aria-labelledby={id}>
                <Text id={id}>{textualData.fields.address.moreInfoText.value}</Text>
              </Expandable.Trigger>
            </Stretch>

            <Expandable.Content>
              <RichText html={textualData.fields.address.moreInfoContent.value} />
            </Expandable.Content>
          </Expandable>

          <Button type="submit">{textualData.fields.content.nextStepText.value}</Button>
        </Stack>
      </Form>
    </FormProvider>
  );
};
