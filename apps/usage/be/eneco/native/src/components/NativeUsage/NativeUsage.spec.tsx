import { Preferences } from '@capacitor/preferences';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { AppPreferences } from '@common/preferences';
import { useSubscriptionsGetCustomerInfo } from '@dc/hooks';
import renderApp from '@jest-tools/renderApp';
import { getCustomerInfoResponse as mockNoSmartInsightsSubscription } from '@mocks/dc/subscriptions/getCustomerInfo/noSubscriptions';
import { getCustomerInfoResponse as mockActiveSmartInsightsSubscription } from '@mocks/dc/subscriptions/getCustomerInfo/smartInsightsActive';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights';

import NativeUsage from './NativeUsage';

jest.mock('@common/application', () => ({
  ...jest.requireActual('@common/application'),
  useApplication: jest.fn().mockReturnValue({
    locale: 'nl-BE',
    locales: ['nl-BE'],
    language: 'nl',
    languages: ['nl'],
    searchParams: new URLSearchParams(),
    isEditMode: false,
  }),
}));

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useSubscriptionsGetCustomerInfo: jest.fn() as unknown as typeof useSubscriptionsGetCustomerInfo,
}));

const { USAGES_RECENT_GRAPHTYPE, USAGES_RECENT_VIEWTYPE, USAGES_RECENT_GRAPHUNIT } = AppPreferences;
describe('NativeUsage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    Preferences.clear();
  });

  it('should render', () => {
    renderApp(NativeUsage, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/usage',
    });
  });

  it('should render with default filters if nothing has been saved in localStorage', async () => {
    renderApp(NativeUsage, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/usage',
    });

    expect(await screen.findByText('Totaal', { exact: true })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: '€', checked: true })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'kWh', checked: false })).toBeInTheDocument();

    expect(screen.getByRole('radio', { name: 'U', checked: false })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'D', checked: false })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'W', checked: true })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'M', checked: false })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'J', checked: false })).toBeInTheDocument();
  });

  it('should render with custom filters after they have been stored locally at least once', async () => {
    await Preferences.set({ key: USAGES_RECENT_GRAPHTYPE, value: 'electricityUsage' });
    await Preferences.set({ key: USAGES_RECENT_VIEWTYPE, value: 'Quarter' });
    await Preferences.set({ key: USAGES_RECENT_GRAPHUNIT, value: 'EURO' });

    renderApp(NativeUsage, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/usage',
    });

    expect(await screen.findByText('Elektriciteit', {}, { timeout: 10 })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: '€', checked: true })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'kWh', checked: false })).toBeInTheDocument();

    expect(screen.getByRole('radio', { name: 'U', checked: true })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'D', checked: false })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'W', checked: false })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'M', checked: false })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'J', checked: false })).toBeInTheDocument();
  });

  it('should render the premium banner when user selects the Live tab and has no smart insights premium subscriptipn', async () => {
    (useSubscriptionsGetCustomerInfo as jest.Mock).mockReturnValue(mockNoSmartInsightsSubscription);

    renderApp(NativeUsage, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/usage',
    });

    const LiveTab = screen.getByRole('radio', { name: 'LIVE' });
    expect(LiveTab).toBeInTheDocument();
    await userEvent.click(LiveTab);

    expect(screen.getByText('Live inzicht in je verbruik?')).toBeInTheDocument();
    expect(
      screen.getByText('Met EnergieMonitor Premium volg je in realtime je energieverbruik in onze app.'),
    ).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Meer informatie over premium' })).toBeInTheDocument();
  });

  it('should not render the premium banner when user selects the Live tab and has a smart insights premium subscriptipn', async () => {
    (useSubscriptionsGetCustomerInfo as jest.Mock).mockReturnValue(mockActiveSmartInsightsSubscription);

    renderApp(NativeUsage, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/usage',
    });

    const LiveTab = screen.getByRole('radio', { name: 'LIVE' });
    expect(LiveTab).toBeInTheDocument();
    await userEvent.click(LiveTab);

    expect(
      screen.queryByText('Met EnergieMonitor Premium volg je in realtime je energieverbruik in onze app.'),
    ).not.toBeInTheDocument();
  });
});
