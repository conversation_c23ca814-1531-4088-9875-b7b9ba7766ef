import React, { FC } from 'react';

import { capitalizeFirstLetter } from '@common/string';
import { useFormatter, useTranslation } from '@i18n';
import { useContent } from '@sitecore/common';
import { NativeUsageRendering } from '@sitecore/types/NativeUsage';
import { Box, Stack, Text } from '@sparky';

import { GraphUnit, GraphType, ViewType } from './types';

const GraphHeader: FC<
  React.PropsWithChildren<{
    onGraphUnitChange: (value: string) => void;
    graphUnit: GraphUnit;
    graphType: GraphType;
    lastViewTypeFromData: ViewType | null;
    selectedDate: Date | null;
    startHour: string;
    endHour: string;
    totalUsageForSelectedPeriod: number;
    isLoading: boolean;
  }>
> = ({
  onGraphUnitChange,
  graphUnit,
  graphType,
  lastViewTypeFromData,
  selectedDate,
  startHour,
  endHour,
  totalUsageForSelectedPeriod,
  isLoading,
}) => {
  const { fields } = useContent<NativeUsageRendering>();
  const { t } = useTranslation();
  const { currency, format, number } = useFormatter();

  const getUsageGraphTotal = () => {
    if (lastViewTypeFromData === 'Quarter') {
      if (graphUnit === 'EURO') {
        return currency.euroThreeDecimals(totalUsageForSelectedPeriod);
      } else {
        return format(fields.data.kwhLabel.value, { usage: number.threeFractionDigits(totalUsageForSelectedPeriod) });
      }
    }

    const usageGraphTotal =
      graphUnit === 'EURO'
        ? currency.euro(totalUsageForSelectedPeriod)
        : totalUsageForSelectedPeriod < 10 && totalUsageForSelectedPeriod > -10
          ? format(fields.data.kwhLabel.value, { usage: number.twoFractionDigits(totalUsageForSelectedPeriod) })
          : format(fields.data.kwhLabel.value, { usage: number.noFractionDigits(totalUsageForSelectedPeriod) });

    return usageGraphTotal;
  };

  return (
    <Box paddingBottom="4">
      <Stack alignX="center">
        <Text color={isLoading ? 'textLowEmphasis' : 'textPrimary'} size="BodyXL" weight="bold">
          {lastViewTypeFromData &&
            selectedDate &&
            capitalizeFirstLetter(
              t(`usageGraphTitle${lastViewTypeFromData}`, {
                date: selectedDate.toString(),
                startHour: startHour,
                endHour: endHour,
              }),
            )}
        </Text>
        <Text color={isLoading ? 'textLowEmphasis' : 'textPrimary'} size="BodyL">
          {graphType === 'totalUsage' && graphUnit === 'KWH' ? <>&nbsp;</> : getUsageGraphTotal()}
        </Text>
      </Stack>
    </Box>
  );
};

export default GraphHeader;
