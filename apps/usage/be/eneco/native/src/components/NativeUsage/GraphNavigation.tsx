import React, { FC, useMemo } from 'react';

import { useTranslation } from '@i18n';
import { useContent } from '@sitecore/common';
import { NativeUsageRendering } from '@sitecore/types/NativeUsage';
import { Box, IconButton, Stack, Text } from '@sparky';
import { ChevronLeftIcon, ChevronRightIcon } from '@sparky/icons';

import { ViewType } from './types';
import { getDate, isCurrentDateRangeBoundary, ViewTypeRangeMap } from './usageHelpers';

const GraphNavigation: FC<
  React.PropsWithChildren<{
    isLoading: boolean;
    startDate: Date;
    currentDate: Date;
    startHour: string;
    endHour: string;
    lastViewTypeFromData: ViewType;
    onPreviousClick: () => void;
    onNextClick: () => void;
  }>
> = ({ isLoading, lastViewTypeFromData, currentDate, startDate, startHour, endHour, onPreviousClick, onNextClick }) => {
  const { t } = useTranslation();
  const { fields } = useContent<NativeUsageRendering>();

  const formattedStartDate = startDate.toString();
  const formattedEndDate = useMemo(() => {
    if (lastViewTypeFromData === 'Live') {
      return null;
    }

    return getDate(new Date(startDate), lastViewTypeFromData, ViewTypeRangeMap[lastViewTypeFromData] - 1).toString();
  }, [startDate, lastViewTypeFromData]);

  const dateRangeText = useMemo(() => {
    if (lastViewTypeFromData === 'Quarter') {
      return t(`dateRange${lastViewTypeFromData}`, {
        startDate: formattedStartDate,
        endDate: formattedEndDate ?? '',
        startHour,
        endHour,
      });
    }

    return t(`dateRange${lastViewTypeFromData}`, {
      startDate: formattedStartDate,
      endDate: formattedEndDate ?? '',
    });
  }, [t, lastViewTypeFromData, formattedStartDate, formattedEndDate, startHour, endHour]);

  return (
    <Box paddingTop="4">
      <Stack alignX="justify" alignY="center" direction="row">
        <IconButton isDisabled={isLoading} onClick={onPreviousClick} label={fields.data.previousButtonText.value}>
          <ChevronLeftIcon />
        </IconButton>

        <Text color={isLoading ? 'textLowEmphasis' : 'textPrimary'} align="center">
          {dateRangeText}
        </Text>

        <IconButton
          isDisabled={isLoading || isCurrentDateRangeBoundary(currentDate, startDate, lastViewTypeFromData)}
          onClick={onNextClick}
          label={fields.data.nextButtonText.value}>
          <ChevronRightIcon />
        </IconButton>
      </Stack>
    </Box>
  );
};

export default GraphNavigation;
