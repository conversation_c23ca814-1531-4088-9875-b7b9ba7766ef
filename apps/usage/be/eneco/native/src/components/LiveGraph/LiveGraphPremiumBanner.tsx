import RichText from '@components/RichText/RichText';
import { useSubscriptionsGetCustomerInfo } from '@dc/hooks';
import { useLinkComponent } from '@link';
import { mapImage, useContent } from '@sitecore/common';
import { NativeUsageRendering } from '@sitecore/types/NativeUsage';
import { Box, ButtonLink, Card, Heading, Image, Stack, Stretch, Text } from '@sparky';

const LiveGraphPremiumBanner = () => {
  const { fields } = useContent<NativeUsageRendering>();
  const Link = useLinkComponent();
  const { data: userInfoData } = useSubscriptionsGetCustomerInfo();

  const activeSmartInsightsSubscription = userInfoData?.subscriptions?.find(
    subscription =>
      subscription.serviceType === 'smart-insights' && ['Active', 'NonRenewing'].includes(subscription.status),
  );

  if (!userInfoData || activeSmartInsightsSubscription) {
    return null;
  }

  const imageProps = mapImage(fields?.premiumBanner.premiumBannerImage);

  return (
    <Box backgroundColor="backgroundPrimary">
      <Card>
        {imageProps.src && (
          <Box borderTopLeftRadius="m" borderTopRightRadius="m" overflow="hidden">
            <Image
              display="block"
              alt={imageProps.alt}
              width="100%"
              height="auto"
              aspectRatio="16/9"
              src={imageProps.src}
            />
          </Box>
        )}
        <Box padding="4">
          <Stack gap="2">
            <Heading as="h2" size="XS">
              {fields?.premiumBanner?.premiumBannerTitle?.value}
            </Heading>
            <Text size="BodyM">
              <RichText html={fields?.premiumBanner.premiumBannerContent?.value} />
            </Text>
          </Stack>
          {fields?.premiumBanner.premiumBannerLink && (
            <Box paddingTop="6">
              <Stretch>
                <Link href={fields.premiumBanner.premiumBannerLink.value.href} linkType="internal">
                  <ButtonLink size="compact">{fields.premiumBanner.premiumBannerLink.value.text}</ButtonLink>
                </Link>
              </Stretch>
            </Box>
          )}
        </Box>
      </Card>
    </Box>
  );
};
export default LiveGraphPremiumBanner;
