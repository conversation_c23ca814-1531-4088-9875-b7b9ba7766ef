import { useApplication } from '@common/application';
import { NotificationBox } from '@sparky';

const DataErrorNotification = () => {
  const { locale } = useApplication();

  const isDutch = locale === 'nl-BE';

  const title = isDutch ? 'Er is een fout opgetreden' : "Une erreur s'est produite";
  const text = isDutch
    ? 'De informatie is momenteel niet beschik<PERSON>ar. Probeer het later opnieuw.'
    : "L'information n'est pas disponible pour le moment. Réessayez plus tard.";

  return <NotificationBox isAlert={false} title={title} text={text} variant={'error'}></NotificationBox>;
};

export default DataErrorNotification;
