import { useMemo, useEffect, useState } from 'react';

import { useApplication } from '@common/application';
import { getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddresses } from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';

import useBusLabel from './useBusLabel';
import { addressFormatter } from '../pages/move/overview/address-formatter';
import { ADDRESS_PARAM } from '../utils/addSearchParams';

export const useAddressOptions = () => {
  const [addressIdentifier, setAddressIdentifier] = useState<string>('');
  const {
    selectedAccount: { crmAccountNumber: accountNumber },
  } = useSelfServiceAccount();
  const { data: session } = useSession();
  const { push, activePath } = useRouter();
  const { searchParams } = useApplication();

  const { data, isLoading, error } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddresses,
    {
      path: {
        accountNumber,
      },
    },
    [`/accounts/${accountNumber}/delivery-addresses`],
    session,
  );

  const busLabel = useBusLabel();

  const addressData = unwrapData(data);
  const addressOptions = useMemo(
    () =>
      addressData?.addresses?.map(({ id, details }) => ({
        value: id || '',
        label: addressFormatter(details, busLabel),
      })),
    [addressData, busLabel],
  );

  useEffect(() => {
    if (addressOptions?.length === 1) return setAddressIdentifier(addressOptions?.[0]?.value);
    const searchParamAddress = searchParams.get(ADDRESS_PARAM);
    if (!searchParamAddress && addressOptions && addressOptions.length > 0)
      return setAddressIdentifier(addressOptions?.[0]?.value);
    if (searchParamAddress?.endsWith('/')) {
      return setAddressIdentifier(searchParamAddress.split('/')[0]);
    }
    setAddressIdentifier(searchParamAddress as string);
  }, [addressOptions, searchParams]);

  const setAddressIdentifierAndPush = (value: string) => {
    setAddressIdentifier(value);
    const searchParamsEntries = Object.fromEntries(searchParams.entries());
    push({ pathname: activePath, query: { ...searchParamsEntries, address: value } }, undefined, { shallow: true });
  };

  return {
    addressIdentifier,
    setAddressIdentifier: setAddressIdentifierAndPush,
    addressOptions,
    isLoading,
    error,
  };
};
