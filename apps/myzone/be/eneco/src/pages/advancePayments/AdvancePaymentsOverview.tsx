import { isFuture, isPast, isToday } from 'date-fns';
import { useSWRConfig } from 'swr';

import RichText from '@components/RichText/RichText';
import { useSetNotification } from '@components-next/Notification/Notification';
import {
  AdvanceAmountCollectionResponse,
  AdvanceAmountDto,
  postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts,
} from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { ManageAdvanceAmountRendering } from '@sitecore/types/ManageAdvanceAmount';
import { Stack } from '@sparky';

import AdvanceNotifications from './AdvanceNotifications';
import AdvancePaymentByEnergyTypes from './components/AdvancePaymentByEnergyTypes';
import { useAdvances } from './hooks/useAdvances';
import { AdvanceOverviewSkeletons } from './skeletons/AdvanceOverviewSkeletons';
import TotalAdvancesCard from './TotalAdvancesCard';
import { calculateReconcileGlobalAdvanceAmount } from './utils/calculateReconcileAdvanceAmount';
import { canEditAdvanceAmount } from './utils/canEditAdvances';
import { VacancyCard } from './VacancyCard';
import { AddressFilter } from '../../components/AddressFilter';
import DataErrorNotification from '../../components/DataErrorNotification';
import { useAddressOptions } from '../../hooks/useAddressOptions';

const getRelevantContractStartDate = (advanceData: AdvanceAmountCollectionResponse | null) => {
  const paymentsWithDates =
    advanceData?.advancePayments?.map(payment => ({
      ...payment,
      parsedDate: new Date(payment.contractDetails!.contractStartDate!),
    })) ?? [];

  const pastPayments = paymentsWithDates.filter(p => isPast(p.parsedDate) || isToday(p.parsedDate));

  if (pastPayments.length > 0) {
    pastPayments.sort((a, b) => b.parsedDate.getTime() - a.parsedDate.getTime());
    return pastPayments[0].contractDetails?.contractStartDate;
  }

  const futurePayments = paymentsWithDates.filter(p => isFuture(p.parsedDate));

  if (futurePayments.length > 0) {
    futurePayments.sort((a, b) => a.parsedDate.getTime() - b.parsedDate.getTime());
    return futurePayments[0].contractDetails?.contractStartDate;
  }

  return null;
};

const AdvanceOverview = () => {
  const { fields } = useContent<ManageAdvanceAmountRendering>();
  const { addressIdentifier, isLoading: isLoadingAddress, error: errorAddress } = useAddressOptions();
  const { advances, isLoading, path, advanceCacheKey, error } = useAdvances(addressIdentifier);
  const { isCurrentAccountReader } = useSelfServiceAccount();
  const { mutate } = useSWRConfig();
  const setNotification = useSetNotification();

  const hasAnActiveContract = advances?.advancePayments?.some((advance: AdvanceAmountDto) => {
    const expiryDate = advance.contractDetails?.contractExpiryDate ?? null;
    return !expiryDate || isToday(expiryDate) || isFuture(expiryDate);
  });

  const currentAmount = advances?.advancePayments?.some(payment => payment.details?.vacancyChangeRequested === true)
    ? advances?.advancePayments?.reduce((total, payment) => {
        return total + (payment.details?.vacancyAmount || 0);
      }, 0)
    : advances?.totals?.currentAmount;

  const hasAdvanceData = advances?.advancePayments?.some(x => x.details?.currentAmount != null);
  const hasYearlyInvoicedMeter = advances?.advancePayments?.some(x => x.meterDetails?.invoiceFrequency === 'Yearly');
  const isDefaulter = advances?.advancePayments?.some(x => x.details?.isDefaulter);

  const relevantContractStartDate = getRelevantContractStartDate(advances);

  const currentMonth = new Date().getMonth() + 1;
  const isInMeterReadingMonth = advances?.advancePayments?.some(payment => {
    const meterReadingMonth = payment.meterDetails?.meterReadingMonth;
    const meterType = payment.meterDetails?.meterType;

    if (meterReadingMonth == null) return true;
    return (
      (meterType === 'Digital' && meterReadingMonth + 1 === currentMonth) ||
      (meterType === 'Analogue' && meterReadingMonth === currentMonth)
    );
  });

  if (isLoading || isLoadingAddress) return <AdvanceOverviewSkeletons />;

  const submitGlobalRecommendedAdvance = async ({ amount }: { amount: number | null | undefined }) => {
    if (amount == null) return;

    try {
      const { response } =
        await postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts({
          path: { ...path },
          body: { amount, type: 'Global' },
        });
      if (!response.ok) {
        throw new Error('request failed');
      }
    } catch (e) {
      void e;
      throw new Error('request failed');
    }

    await mutate(advanceCacheKey);
    setNotification({
      title: fields.commodityCard.recommendedAdvanceAmountNotification.value.title,
      text: <RichText html={fields.commodityCard.recommendedAdvanceAmountNotification.value.content} />,
      variant: fields.commodityCard.recommendedAdvanceAmountNotification.value.variant,
    });
  };

  const fetchingAdvancesError = error !== undefined && advances === undefined;
  const fetchingAddressError = errorAddress !== undefined && addressIdentifier === null;

  if (!isLoading && (fetchingAdvancesError || fetchingAddressError)) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Stack gap="6">
      <AddressFilter label={fields.data.addressSelectLabel?.value || 'Verbruiksadres [not in sitecore]'} />

      <AdvanceNotifications
        advanceData={advances}
        hasAdvanceData={hasAdvanceData ?? false}
        fields={fields}
        hasAnActiveContract={!!hasAnActiveContract}
        isInMeterReadingMonth={!!isInMeterReadingMonth}
        relevantContractStartDate={relevantContractStartDate}
        hasYearlyInvoicedMeter={hasYearlyInvoicedMeter ?? false}
        isDefaulter={isDefaulter ?? false}
        isLoading={isLoading}
      />

      {hasAnActiveContract &&
        hasAdvanceData &&
        hasYearlyInvoicedMeter &&
        relevantContractStartDate &&
        !isFuture(relevantContractStartDate) && (
          <TotalAdvancesCard
            fields={fields}
            recommendedAmount={advances?.totals?.recommendedAmount}
            currentAmount={currentAmount}
            hideRecommendedAmount={(isInMeterReadingMonth || isDefaulter) ?? true}
            reconcileAdvanceAmount={calculateReconcileGlobalAdvanceAmount({ advances })}
            hideEdit={
              !canEditAdvanceAmount({
                isCurrentAccountReader,
                advancePayments: advances?.advancePayments ?? [],
              })
            }
            addressIdentifier={addressIdentifier}
            globalRecommendedAdvancesOnChange={amount => submitGlobalRecommendedAdvance({ amount })}
          />
        )}

      {advances && hasYearlyInvoicedMeter && (
        <AdvancePaymentByEnergyTypes fields={fields} advancesData={advances} addressIdentifier={addressIdentifier} />
      )}

      <VacancyCard hideRequestLink={isDefaulter || isCurrentAccountReader} />
    </Stack>
  );
};

export default AdvanceOverview;
