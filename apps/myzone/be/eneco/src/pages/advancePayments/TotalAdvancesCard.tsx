import React, { useState } from 'react';

import { useFormatter } from '@i18n';
import { Fields } from '@sitecore/types/ManageAdvanceAmount';
import { Box, Bucket, Button, Heading, Stack, Text, TextLink } from '@sparky';
// eslint-disable-next-line dxp-rules/no-custom-styling
import { styled } from '@sparky/stitches';

import { addressSearchParam } from '../../utils/addSearchParams';

interface AdvanceCardProps {
  fields: Fields;
  recommendedAmount: number | null | undefined;
  currentAmount: number | null | undefined;
  reconcileAdvanceAmount: number | undefined | null;
  hideRecommendedAmount: boolean;
  hideEdit: boolean;
  addressIdentifier: string;
  globalRecommendedAdvancesOnChange: (amount: number | null | undefined) => Promise<void>;
}

const AlignYBaseline = styled(Stack, {
  alignItems: 'baseline',
});

const BorderedBox = styled(Box, {
  border: '1px solid $borderDividerLowEmphasis',
});

const TotalAdvancesCard: React.FC<AdvanceCardProps> = ({
  fields,
  recommendedAmount,
  currentAmount,
  reconcileAdvanceAmount,
  hideRecommendedAmount,
  hideEdit,
  addressIdentifier,
  globalRecommendedAdvancesOnChange,
}) => {
  const { currency } = useFormatter();
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  return (
    <Bucket title={fields.advanceCard.title.value}>
      <Bucket.Content>
        <Stack gap="4">
          <Text weight="bold">{fields.advanceCard.currentAmountTitle.value}</Text>
          <AlignYBaseline direction="row" gap="1">
            <Heading as="h3" size="L">
              {currency.euroNoFractionDigits(currentAmount ?? 0)}
            </Heading>
            <Text weight="bold">{fields.advanceCard.eachMonthLabel.value}</Text>
          </AlignYBaseline>
        </Stack>
        {!hideRecommendedAmount && currentAmount !== recommendedAmount && (
          <BorderedBox backgroundColor="backgroundSecondary" borderRadius="s" padding="6">
            <Stack gap="6">
              <Stack direction="row" alignX="justify" alignY="center">
                <Stack gap="4">
                  <Text weight="bold">{fields.advanceCard.recommendedAmountTitle.value}</Text>
                  <AlignYBaseline direction="row" gap="1">
                    <Heading as="h3" size="M">
                      {currency.euroNoFractionDigits(recommendedAmount ?? 0)}
                    </Heading>
                    <Text weight="bold">{fields.advanceCard.eachMonthLabel.value}</Text>
                  </AlignYBaseline>
                </Stack>
                <Stack>
                  {!hideEdit && (
                    <Button
                      action="primary"
                      size="compact"
                      type="button"
                      isLoading={isLoadingSubmit}
                      onClick={async () => {
                        setIsLoadingSubmit(true);
                        await globalRecommendedAdvancesOnChange(recommendedAmount);
                        setIsLoadingSubmit(false);
                      }}>
                      {fields.advanceCard.acceptRecommendedAmountLink.value.text}
                    </Button>
                  )}
                </Stack>
              </Stack>
              <ReconcileAdvanceAmountDisclaimer amount={reconcileAdvanceAmount} fields={fields} />
            </Stack>
          </BorderedBox>
        )}
        <Stack.Item>
          {!hideEdit && (
            <TextLink
              href={fields.advanceCard.editAdvanceAmountLink.value.href + addressSearchParam(addressIdentifier)}
              emphasis="high">
              {fields.advanceCard.editAdvanceAmountLink.value.text}
            </TextLink>
          )}
        </Stack.Item>
      </Bucket.Content>
    </Bucket>
  );
};

export default TotalAdvancesCard;

const ReconcileAdvanceAmountDisclaimer = ({
  amount,
  fields,
}: {
  fields: Fields;
  amount: number | null | undefined;
}) => {
  const { format, currency } = useFormatter();

  if (amount == null || amount === 0) return null;

  if (amount < 0)
    return (
      <Text size="BodyM">
        {format(fields?.advanceCard?.recommendedAmountTooLowDisclaimerLabel?.value ?? '[not in sitecore {amount}]', {
          amount: currency.euroNoFractionDigits(Math.abs(amount)),
        })}
      </Text>
    );
  else
    return (
      <Text size="BodyM">
        {format(fields?.advanceCard?.recommendedAmountTooHighDisclaimerLabel?.value ?? '[not in sitecore {amount}]', {
          amount: currency.euroNoFractionDigits(amount),
        })}
      </Text>
    );
};
