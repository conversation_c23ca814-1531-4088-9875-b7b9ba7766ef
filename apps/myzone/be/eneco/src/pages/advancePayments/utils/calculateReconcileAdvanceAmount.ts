import { AdvanceAmountCollectionResponse } from '@dc-be/client';

export const calculateReconcileIndividualAdvanceAmount = ({
  currentAmount,
  monthsUntilMeterReadingMonth,
  amountDueForMeterReadingMonth,
}: {
  currentAmount: number;
  monthsUntilMeterReadingMonth: number;
  amountDueForMeterReadingMonth: number;
}): number => {
  const userProjectedAmount = currentAmount * monthsUntilMeterReadingMonth;
  return userProjectedAmount - amountDueForMeterReadingMonth;
};

export const calculateReconcileGlobalAdvanceAmount = ({
  advances,
}: {
  advances: AdvanceAmountCollectionResponse | null;
}): number | null | undefined => {
  if (!advances) return null;

  return advances?.advancePayments?.reduce(
    (total, advance) =>
      total +
      calculateReconcileIndividualAdvanceAmount({
        currentAmount: advance.details?.currentAmount ?? 0,
        amountDueForMeterReadingMonth: advance.details?.amountDueForMeterReadingMonth ?? 0,
        monthsUntilMeterReadingMonth: advance.details?.monthsUntilMeterReadingMonth ?? 0,
      }),
    0,
  );
};
