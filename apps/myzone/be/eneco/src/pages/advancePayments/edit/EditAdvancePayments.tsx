import { useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import { useApplication } from '@common/application';
import RichText from '@components/RichText/RichText';
import {
  EnergyTypeDto,
  postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts,
  UpdateAdvancePaymentRequest,
} from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { EditAdvanceAmountRendering, Fields } from '@sitecore/types/EditAdvanceAmount';
import {
  AlertDialog,
  Bucket,
  Button,
  ButtonLink,
  Form,
  Grid,
  InputNumber,
  NotificationBox,
  Skeleton,
  Stack,
  Text,
} from '@sparky';
import { NotificationBoxProps } from '@sparky/types';

import { LoadingText } from '../../../components/Loading/LoadingText';
import { RoundedSkeleton } from '../../../components/Loading/RoundedSkeleton';
import { useRedirectAndNotifyBE } from '../../../hooks/useRedirectAndNotifyBE';
import { EnergyIcon } from '../components/EnergyIcon';
import ReconcileAmountNotification from '../components/ReconcileAmountNotification';
import { useAdvances } from '../hooks/useAdvances';
import {
  calculateReconcileGlobalAdvanceAmount,
  calculateReconcileIndividualAdvanceAmount,
} from '../utils/calculateReconcileAdvanceAmount';
import { canEditAdvanceAmount } from '../utils/canEditAdvances';

const EditAdvancePayments: React.FC = () => {
  const { fields } = useContent<EditAdvanceAmountRendering>();
  const { advances, isLoading, path, advanceCacheKey } = useAdvances();
  const { isCurrentAccountReader } = useSelfServiceAccount();
  const { searchParams } = useApplication();
  const { mutate } = useSWRConfig();
  const redirectAndNotify = useRedirectAndNotifyBE();
  const { currency, format } = useFormatter();
  const { push } = useRouter();

  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);
  const [isOpenConfirmDialog, setIsOpenConfirmDialog] = useState<boolean>(false);

  const [error, setError] = useState<NotificationBoxProps | null>(null);

  const eanNumber = searchParams.get('ean') || '';
  const isGlobal = !eanNumber;
  const advance = advances?.advancePayments?.find(x => x.meterDetails?.ean?.toString() === eanNumber.toString());
  const hasAdvanceAmountForContract = (): boolean => {
    if (isGlobal) return advances?.totals?.currentAmount != null && advances?.totals?.recommendedAmount != null;
    return advance?.details?.currentAmount != null && advance?.details?.recommendedAmount != null;
  };

  const FormSchema = yup.object({
    advanceAmount: yup
      .number()
      .typeError(fields.data?.advancePaymentFormField?.value?.requiredMessage ?? '[not in sitecore]: Veld verplicht')
      .required(fields.data?.advancePaymentFormField?.value?.requiredMessage ?? '[not in sitecore]: Veld verplicht')
      .test('min-max-test', fields.data?.advancePaymentFormField?.value?.validationMessage, function (value) {
        const min = isGlobal ? (advances?.totals?.minAmount ?? 0) : (advance?.details?.minAmount ?? 0);
        const max = isGlobal
          ? (advances?.totals?.maxAmount ?? Number.MAX_SAFE_INTEGER)
          : (advance?.details?.maxAmount ?? Number.MAX_SAFE_INTEGER);
        return value! >= min && value! <= max;
      }),
  });

  type FormValues = yup.InferType<typeof FormSchema>;
  const form = useForm<FormValues>({
    mode: 'onBlur',
    resolver: yupResolver(FormSchema),
  });

  const {
    control,
    setValue,
    watch,
    formState: { errors },
  } = form;

  const advanceAmount = watch('advanceAmount');

  const sendApiRequest = async ({ amount }: { amount: number }): Promise<Response> => {
    const request: UpdateAdvancePaymentRequest = {
      amount,
      type: isGlobal ? 'Global' : 'Granular',
      ...(eanNumber && { ean: eanNumber }),
    };
    const { response } =
      await postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts({
        path: { ...path },
        body: request,
      });
    if (!response.ok) {
      throw new Error('request failed');
    }
    setValue('advanceAmount', amount);
    await mutate(advanceCacheKey);
    return response;
  };

  const submitForm: SubmitHandler<FormValues> = async ({ advanceAmount }) => {
    try {
      setIsLoadingSubmit(true);
      await sendApiRequest({ amount: advanceAmount });
      redirectAndNotify({
        title: fields.notifications?.successNotification?.value?.title,
        text: fields.notifications?.successNotification?.value?.content,
        variant: fields.notifications?.successNotification?.value?.variant,
        route: fields.data?.saveButtonLinkk?.value?.href,
      });
    } catch {
      setIsLoadingSubmit(false);
      setError({
        isAlert: false,
        title: fields.notifications?.errorNotification?.value?.title,
        text: fields.notifications?.errorNotification?.value?.content,
        variant: fields.notifications?.errorNotification?.value?.variant,
      });
    }
  };

  useEffect(() => {
    if (isGlobal) setValue('advanceAmount', advances?.totals?.currentAmount ?? 0);
    else setValue('advanceAmount', advance?.details?.currentAmount ?? 0);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advances]);

  if (
    !isLoading &&
    advances &&
    !canEditAdvanceAmount({ isCurrentAccountReader, advancePayments: isGlobal ? advances.advancePayments : [advance] })
  ) {
    push(fields.data?.cancelButtonLinkk?.value?.href);
  }

  const AdvanceAmountNotFound = () => {
    return (
      <NotificationBox
        isAlert
        title={fields.notifications?.advanceAmountUnavailableNotification?.value?.title ?? '[todo in sitecore]: Titel'}
        text={
          <RichText
            html={
              fields.notifications?.advanceAmountUnavailableNotification?.value?.content ??
              '[todo in sitecore] We kunnen momenteel geen voorschotbedrag aangeven. Van zodra we data hebben kan je jouw voorschotbedrag aanpassen.'
            }
          />
        }
      />
    );
  };

  return (
    <Form onSubmit={form.handleSubmit(submitForm)}>
      <Bucket
        title={
          <BucketTitle
            fields={fields}
            energyType={advance?.meterDetails?.energyType ?? 'Electricity'}
            ean={advance?.meterDetails?.ean ?? ''}
            isLoading={isLoading}
          />
        }>
        <Bucket.Content>
          <Stack gap="4">
            {!hasAdvanceAmountForContract() && !isLoading && advances ? (
              <AdvanceAmountNotFound />
            ) : (
              <>
                <Grid columns={{ initial: 1, md: 2 }}>
                  {isLoading || !advances ? (
                    <Stack gap={0}>
                      <Skeleton height={100} />
                      <LoadingText />
                    </Stack>
                  ) : (
                    <Controller
                      control={control}
                      name="advanceAmount"
                      render={({ field }) => (
                        <InputNumber
                          {...field}
                          label={fields.data?.advancePaymentFormField?.value?.label}
                          hint={
                            <Text size="BodyS" color="textLowEmphasis">
                              {format(fields.data?.advancePaymentFormField?.value?.hint, {
                                min: currency.euro(
                                  isGlobal ? (advances?.totals?.minAmount ?? 0) : (advance?.details?.minAmount ?? 0),
                                ),
                                max: currency.euro(
                                  isGlobal ? (advances?.totals?.maxAmount ?? 0) : (advance?.details?.maxAmount ?? 0),
                                ),
                              })}
                            </Text>
                          }
                          formatOptions={{ currency: 'EUR', maximumFractionDigits: 0, minimumFractionDigits: 0 }}
                          error={errors.advanceAmount?.message}
                        />
                      )}
                    />
                  )}
                </Grid>

                <Stack.Item>
                  <Text weight="bold">{fields.data?.recommendedAmountLabel?.value}</Text>
                  {isLoading || !advances ? (
                    <LoadingText />
                  ) : (
                    <Text>
                      {isGlobal
                        ? currency.euro(advances?.totals?.recommendedAmount ?? 0)
                        : currency.euro(advance?.details?.recommendedAmount ?? 0)}
                    </Text>
                  )}
                </Stack.Item>
                {isLoading ? (
                  <Skeleton height={120} />
                ) : (
                  <ReconcileAmountNotification
                    advanceTooHighNotification={fields.notifications?.advanceTooHighNotification}
                    advanceTooLowNotification={fields.notifications?.advanceTooLowNotification}
                    advanceOkNotification={
                      fields.notifications?.advanceOkNotification ?? {
                        value: { content: '[todo not in sitecore]: Bedrag is ok', variant: 'success' },
                      }
                    }
                    predictedAmount={
                      isGlobal
                        ? calculateReconcileGlobalAdvanceAmount({ advances })
                        : calculateReconcileIndividualAdvanceAmount({
                            amountDueForMeterReadingMonth: advance?.details?.amountDueForMeterReadingMonth ?? 0,
                            currentAmount: Number(advanceAmount ?? 0),
                            monthsUntilMeterReadingMonth: advance?.details?.monthsUntilMeterReadingMonth ?? 0,
                          })
                    }
                  />
                )}
                {error && <NotificationBox {...error} />}
              </>
            )}
          </Stack>
        </Bucket.Content>

        <Bucket.Footer>
          <Bucket.Actions>
            {isLoading ? (
              <>
                <RoundedSkeleton height={50} width={100} />
                <RoundedSkeleton height={50} width={100} />
              </>
            ) : (
              <>
                {!isCurrentAccountReader && hasAdvanceAmountForContract() && (
                  <AlertDialog
                    title={
                      fields.notifications?.confirmSendAdvanceAmountDialog?.value?.title ?? '[todo sitecore] Titel'
                    }
                    isOpen={isOpenConfirmDialog}
                    setOpen={setIsOpenConfirmDialog}
                    description={
                      <RichText
                        html={
                          fields.notifications?.confirmSendAdvanceAmountDialog?.value?.content ??
                          '[todo sitecore] Ben je zeker?'
                        }
                      />
                    }
                    onConfirm={() => {
                      form.handleSubmit(submitForm)();
                    }}
                    confirmText={
                      fields.notifications?.confirmSendAdvanceAmountDialog?.value?.submitButtonText ??
                      '[todo sitecore] confirm'
                    }
                    denyText={
                      fields.notifications?.confirmSendAdvanceAmountDialog?.value?.cancelButtonText ??
                      '[todo sitecore] annuleren'
                    }
                    onDeny={() => {}}
                  />
                )}
                <Button
                  type="button"
                  isLoading={isLoadingSubmit}
                  onClick={() => {
                    if (!errors.advanceAmount) setIsOpenConfirmDialog(true);
                  }}>
                  {fields.data?.saveButtonLinkk?.value?.text}
                </Button>
                <ButtonLink action="secondary" href={fields.data?.cancelButtonLinkk?.value?.href}>
                  {fields.data?.cancelButtonLinkk?.value?.text}
                </ButtonLink>
              </>
            )}
          </Bucket.Actions>
        </Bucket.Footer>
      </Bucket>
    </Form>
  );
};

export default EditAdvancePayments;

const BucketTitle = ({
  energyType,
  ean,
  isLoading = false,
  fields,
}: {
  energyType: EnergyTypeDto;
  ean: string;
  isLoading: boolean;
  fields: Fields;
}) => {
  if (isLoading) {
    return (
      <Stack>
        <LoadingText />
      </Stack>
    );
  }

  if (!isLoading && !ean) return <Text weight="bold">{fields.data?.totalAmountTitle?.value}</Text>;
  const energyTypeField = fields.data?.energyTypesList?.value?.enum?.find(
    x => x?.name?.toLocaleLowerCase() === energyType?.toLocaleLowerCase(),
  );
  return (
    <Stack alignY="center" direction="row" gap={2}>
      <EnergyIcon energyType={energyType} size="medium" />
      <Text weight="bold">{`${energyTypeField?.label} - ${ean}`}</Text>
    </Stack>
  );
};
