import { getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts } from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';

import { ADDRESS_PARAM } from '../../../utils/addSearchParams';

/**
 * A hook to fetch the advances for a specific address
 * @param addressIdentifier
 */
export const useAdvances = (addressIdentifierParam?: string) => {
  const {
    selectedAccount: { crmAccountNumber: accountNumber },
  } = useSelfServiceAccount();
  const { query } = useRouter();
  const { data: session } = useSession();
  const addressIdentifier = addressIdentifierParam ? addressIdentifierParam : (query[ADDRESS_PARAM] as string);
  const advanceCacheKey = `/accounts/${accountNumber}/delivery-addresses/${addressIdentifier}/advance-amounts`;

  const { data, isLoading, error } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts,
    {
      path: {
        accountNumber,
        addressIdentifier,
      },
    },
    [advanceCacheKey],
    session,
  );

  return {
    advances: unwrapData(data),
    addressIdentifier,
    path: { accountNumber, addressIdentifier },
    session,
    advanceCacheKey,
    isLoading,
    error: error,
  };
};
