import { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Form<PERSON>rovider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import { useApplication } from '@common/application';
import RichText from '@components/RichText/RichText';
import {
  MoveEnergyTypeDto,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeter,
} from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { MoveEditMeterDetailsRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
// eslint-disable-next-line dxp-rules/no-dialog-from-sparky
import {
  Bucket,
  Button,
  ButtonLink,
  Dialog,
  Form,
  InputSelect,
  InputText,
  NotificationBox,
  Skeleton,
  Stack,
  Text,
  TextLink,
} from '@sparky';
import { ElectricityIcon, GasIcon } from '@sparky/icons';
import { NotificationBoxProps } from '@sparky/types';

import DataErrorNotification from '../../../../components/DataErrorNotification';
import ReadFormField from '../../../../components/ReadFormField';
import { useRedirectAndNotifyBE } from '../../../../hooks/useRedirectAndNotifyBE';
import { addressSearchParam } from '../../../../utils/addSearchParams';
import { fromBase64URL } from '../../../../utils/base64';
import { meterRegisterMapper, singleRegisterMapper } from '../../../../utils/move/meterRegisterMapper';
import { MeterReadingConfig, MeterReadingInputs } from '../../components/MeterReadingInputs';
import { useMove } from '../../hooks/useMove';
import {
  METER_REGISTERS_SCHEMA,
  MeterAndRegisterType,
  optionalPositiveNumber,
  oneOfTwo,
  NEW_METER_DATA_URL_PARAM,
} from '../utils/moveEditMeterDetailsUtils';

interface MeterValue {
  ean: string;
  meterNumber: string;
  energyType: MoveEnergyTypeDto;
}

interface BucketTitleProps {
  title: string;
}

const ElectricityBucketTitle: FC<BucketTitleProps> = ({ title }) => (
  <Stack direction={'row'} alignY={'center'}>
    <ElectricityIcon color="iconElectricity" />
    <Text>{title}</Text>
  </Stack>
);

const GasBucketTitle: FC<BucketTitleProps> = ({ title }) => (
  <Stack direction={'row'} alignY={'center'}>
    <GasIcon color="iconGas" />
    <Text>{title}</Text>
  </Stack>
);

const MoveFileEditMeterDetails: FC = () => {
  const { fields } = useContent<MoveEditMeterDetailsRenderingExtended>();
  const { selectedAccount, isCurrentAccountReader } = useSelfServiceAccount();
  const { path, move, isLoading, moveCacheKey, errorMove } = useMove();
  const { searchParams } = useApplication();
  const { mutate } = useSWRConfig();
  const { push } = useRouter();

  const redirectAndNotify = useRedirectAndNotifyBE();
  const addressIdentifier = path.addressIdentifier;

  const [error, setError] = useState<NotificationBoxProps | null>(null);
  const [showMeterReadingsDialog, setMeterReadingsDialog] = useState(false);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);
  const [showEanDialog, setShowEanDialog] = useState(false);
  const [readings, setReadings] = useState<MeterReadingConfig[]>([]);

  const isOldAddress = fields.meterDetails.isOldAddressFlag.value;

  const meters = isOldAddress ? (move?.oldAddress?.meters ?? []) : (move?.newAddress?.meters ?? []);
  const meterSearchParam = searchParams.get('meter') || '';
  const energyTypeSearchParam = searchParams.get('type') || '';

  const isNewMeter = meterSearchParam === NEW_METER_DATA_URL_PARAM;
  const meterValue: MeterValue | undefined = !isNewMeter
    ? meterSearchParam
      ? JSON.parse(fromBase64URL(meterSearchParam))
      : undefined
    : { ean: '', meterNumber: '', energyType: energyTypeSearchParam };
  const eanValue = meterValue?.ean;
  const meterNumberValue = meterValue?.meterNumber;

  // this is used for unfilled meters on a new address
  const newMeter = {
    ean: '',
    meterNumber: '',
    energyType: (energyTypeSearchParam === 'Electricity' ? 'Electricity' : 'Gas') as MoveEnergyTypeDto,
    isDigital: false,
    registers: [],
    mappedRegister: {
      showDayMeterReading: false,
      showNightMeterReading: false,
      showExclNightMeterReading: false,
      showSingleMeterReading: true,
    },
  };

  const meterAndRegisters: MeterAndRegisterType | undefined = isNewMeter
    ? newMeter
    : meters
        .map(meter => ({
          ...meter,
          mappedRegister: meterRegisterMapper(meter),
        }))
        .filter(meter => !meter.isDigital)
        .find(meter => meter.ean === eanValue && meter.meterNumber === meterNumberValue);

  const isDigitalMeter = meterAndRegisters?.isDigital ?? (meters.length > 0 && meters.every(m => m.isDigital));
  const previousUrl = fields.meterDetails.submitMoveFileButtonLink.value.href + addressSearchParam(addressIdentifier);
  const moveEnergyTypes: MoveEnergyTypeDto[] = ['Gas', 'Electricity'];

  const MeterReadingsSchemaObject = yup.object({
    dayMeterValue: optionalPositiveNumber(fields.meterDetails.dayMeterReadingFormField.value.validationMessage),
    meterValue: optionalPositiveNumber(fields.meterDetails.singleMeterReadingFormField.value.validationMessage),
    nightExclusiveMeterValue: optionalPositiveNumber(
      fields.meterDetails.exclusiveNightMeterReadingFormField.value.validationMessage,
    ),
    nightMeterValue: optionalPositiveNumber(fields.meterDetails.nightMeterReadingFormField.value.validationMessage),
    energyType: yup.mixed().oneOf(moveEnergyTypes),
    meterNumber: yup
      .string()
      .test('oneOfTwo', fields.meterDetails.meterNumberFormField.value.validationMessage, oneOfTwo),
    ean: yup.string().test('oneOfTwo', fields.meterDetails.eanCodeFormField.value.validationMessage, oneOfTwo),
    oldEAN: yup.string(),
    oldMeterNumber: yup.string(),
  });

  type FormValues = yup.InferType<typeof MeterReadingsSchemaObject>;

  const form = useForm<FormValues>({
    resolver: yupResolver(MeterReadingsSchemaObject),
    values: {
      dayMeterValue: undefined,
      meterValue: undefined,
      nightExclusiveMeterValue: undefined,
      nightMeterValue: undefined,
      meterNumber: meterAndRegisters?.meterNumber || undefined,
      energyType: meterAndRegisters?.energyType,
      ean: meterAndRegisters?.ean || undefined,
      oldEAN: meterAndRegisters?.ean || undefined,
      oldMeterNumber: meterAndRegisters?.meterNumber || undefined,
    },
  });

  const {
    setValue,
    register,
    formState: { errors, touchedFields },
    watch,
    trigger,
  } = form;

  const ean = watch('ean');
  const meterNumber = watch('meterNumber');

  const submitForm: SubmitHandler<FormValues> = async values => {
    try {
      setIsLoadingSubmit(true);
      const { response } =
        await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeter(
          {
            path: {
              accountNumber: selectedAccount.crmAccountNumber,
              addressIdentifier,
              locationType: isNewMeter ? 'New' : 'Old',
            },
            body: {
              ...values,
              energyType: values.energyType as MoveEnergyTypeDto,
              oldEAN: values.oldEAN,
              oldMeterNumber: values.oldMeterNumber,
            },
          },
        );
      if (response.ok) {
        mutate(moveCacheKey);
        push(previousUrl);
      }
    } catch (e) {
      void e;
      setError({
        isAlert: false,
        text: fields.meterDetails.errorNotification.value.content,
        variant: fields.meterDetails.errorNotification.value.variant,
      });
      return;
    } finally {
      setIsLoadingSubmit(false);
    }
  };

  useEffect(() => {
    if (!meterValue) {
      push(previousUrl);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meterValue]);

  useEffect(() => {
    if (isDigitalMeter && !isLoading) {
      redirectAndNotify({
        route: previousUrl,
        variant: fields.meterDetails.digitalMeterNotification.value.variant,
        title: fields.meterDetails.digitalMeterNotification.value.title,
        text: fields.meterDetails.digitalMeterNotification.value.content,
        headingLevel: 'h3',
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDigitalMeter, path.addressIdentifier, redirectAndNotify, isLoading]);

  useEffect(() => {
    if (isLoading) {
      return;
    }

    if (!meterAndRegisters || meterAndRegisters.registers?.length === 0) {
      setReadings([
        {
          schemaKey: METER_REGISTERS_SCHEMA.METER_VALUE,
          label: fields.meterDetails.singleMeterReadingFormField.value.label,
          placeholder: fields.meterDetails.singleMeterReadingFormField.value.placeholder,
        },
      ]);
      return;
    }

    const readings: MeterReadingConfig[] = [];

    meterAndRegisters.registers?.forEach(register => {
      const { showDayMeterReading, showNightMeterReading, showExclNightMeterReading, showSingleMeterReading } =
        singleRegisterMapper(register);

      const meterReadingExists = register.meterReading !== null && register.meterReading !== undefined;

      if (showDayMeterReading) {
        readings.push({
          schemaKey: METER_REGISTERS_SCHEMA.DAY_METER_VALUE,
          label: fields.meterDetails.dayMeterReadingFormField.value.label,
          placeholder: fields.meterDetails.dayMeterReadingFormField.value.placeholder,
        });
        meterReadingExists && setValue(METER_REGISTERS_SCHEMA.DAY_METER_VALUE, register.meterReading);
      }
      if (showNightMeterReading) {
        readings.push({
          schemaKey: METER_REGISTERS_SCHEMA.NIGHT_METER_VALUE,
          label: fields.meterDetails.nightMeterReadingFormField.value.label,
          placeholder: fields.meterDetails.nightMeterReadingFormField.value.placeholder,
        });
        meterReadingExists && setValue(METER_REGISTERS_SCHEMA.NIGHT_METER_VALUE, register.meterReading);
      }
      if (showExclNightMeterReading) {
        readings.push({
          schemaKey: METER_REGISTERS_SCHEMA.NIGHT_EXCLUSIVE_METER_VALUE,
          label: fields.meterDetails.exclusiveNightMeterReadingFormField.value.label,
          placeholder: fields.meterDetails.exclusiveNightMeterReadingFormField.value.placeholder,
        });
        meterReadingExists && setValue(METER_REGISTERS_SCHEMA.NIGHT_EXCLUSIVE_METER_VALUE, register.meterReading);
      }
      if (showSingleMeterReading) {
        readings.push({
          schemaKey: METER_REGISTERS_SCHEMA.METER_VALUE,
          label: fields.meterDetails.singleMeterReadingFormField.value.label,
          placeholder: fields.meterDetails.singleMeterReadingFormField.value.placeholder,
        });
        meterReadingExists && setValue(METER_REGISTERS_SCHEMA.METER_VALUE, register.meterReading);
      }
    });

    setReadings(readings);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading]);

  useEffect(() => {
    if (touchedFields.ean || touchedFields.meterNumber) {
      trigger('meterNumber');
      trigger('ean');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ean, meterNumber]);

  const suggestedEans = move?.newAddress?.suggestedEans
    ?.filter(ean => ean.energyType === energyTypeSearchParam)
    .map(({ ean }) => {
      if (!ean) {
        return {
          ean: '',
        };
      }
      if (ean.length < 8) {
        return {
          label: ean,
          value: ean,
        };
      }
      const obfuscatedEan = `${ean.substring(0, 4)}${'*'.repeat(ean.length - 8)}${ean.substring(ean.length - 4, ean.length)}`;
      return {
        label: obfuscatedEan,
        value: ean,
      };
    });

  const shouldShowSuggestedEans =
    isNewMeter && suggestedEans?.length && suggestedEans?.length >= 2 && suggestedEans?.length <= 5;

  if (!isLoading && errorMove !== undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <FormProvider {...form}>
      <Form onSubmit={form.handleSubmit(submitForm)}>
        <Stack gap="10">
          <Stack gap="6" alignX="start">
            <RichText html={fields.meterDetails.description.value} />
            {isLoading ? (
              <Skeleton width="100%" height={500} />
            ) : (
              <Bucket
                title={
                  meterAndRegisters?.energyType === 'Electricity' ? (
                    <ElectricityBucketTitle title={fields.meterDetails.electricityTitle?.value} />
                  ) : (
                    <GasBucketTitle title={fields.meterDetails.gasTitle?.value} />
                  )
                }>
                <Bucket.Content>
                  <>
                    <Stack gap="2">
                      {isOldAddress ? (
                        <ReadFormField
                          label={fields.meterDetails.eanCodeFormField.value.label}
                          value={meterAndRegisters?.ean}
                        />
                      ) : (
                        <>
                          {shouldShowSuggestedEans ? (
                            <InputSelect
                              label={fields.meterDetails.eanCodeFormField.value.label}
                              placeholder={fields.meterDetails.eanCodeDropdownPlaceholderText.value}
                              options={suggestedEans?.map(({ label, value }) => ({
                                label: label || '',
                                value: value || '',
                              }))}
                              {...register('ean')}
                              error={errors['ean']?.message}
                            />
                          ) : (
                            <InputText
                              label={fields.meterDetails.eanCodeFormField.value.label}
                              placeholder={fields.meterDetails.eanCodeFormField.value.placeholder}
                              hint={fields.meterDetails.eanCodeFormField.value.hint}
                              {...register('ean')}
                              error={errors['ean']?.message}
                            />
                          )}
                          <Text size="BodyS">
                            <TextLink onClick={() => setShowEanDialog(true)}>
                              {fields.meterDetails.eanInfoDialog.value.triggerText}
                            </TextLink>
                          </Text>
                        </>
                      )}
                    </Stack>
                    {isOldAddress ? (
                      <ReadFormField
                        label={fields.meterDetails.meterNumberFormField.value.label}
                        value={meterAndRegisters?.meterNumber}
                      />
                    ) : (
                      <InputText
                        label={fields.meterDetails.meterNumberFormField.value.label}
                        {...register('meterNumber')}
                        error={errors['meterNumber']?.message}
                      />
                    )}
                    <MeterReadingInputs
                      meterAndRegister={meterAndRegisters}
                      isCurrentAccountReader={isCurrentAccountReader}
                      meterReadingsDialogTitle={fields.meterDetails.meterReadingsInfoDialog.value.title}
                      onOpenMeterReadingsDialog={() => setMeterReadingsDialog(true)}
                      readings={readings}
                    />
                  </>
                </Bucket.Content>
                <Bucket.Footer>
                  <Bucket.Actions>
                    <Button type="submit" isLoading={isLoadingSubmit}>
                      {fields.navigation.nextButtonText.value}
                    </Button>
                    <ButtonLink
                      action="secondary"
                      href={
                        fields.meterDetails.submitMoveFileButtonLink.value.href + addressSearchParam(addressIdentifier)
                      }>
                      {fields.navigation.previousButtonText.value}
                    </ButtonLink>
                  </Bucket.Actions>
                </Bucket.Footer>
              </Bucket>
            )}
          </Stack>

          {error && <NotificationBox {...error} />}
        </Stack>
      </Form>

      <Dialog
        isOpen={showEanDialog}
        setOpen={setShowEanDialog}
        trigger={fields.meterDetails.eanInfoDialog.value.triggerText}
        title={fields.meterDetails.eanInfoDialog.value.title}
        description={fields.meterDetails.eanInfoDialog.value.content}
        width="regular"
      />

      <Dialog
        isOpen={showMeterReadingsDialog}
        setOpen={setMeterReadingsDialog}
        trigger={fields.meterDetails.meterReadingsInfoDialog.value.triggerText}
        title={fields.meterDetails.meterReadingsInfoDialog.value.title}
        description={fields.meterDetails.meterReadingsInfoDialog.value.content}
        width="regular"
      />
    </FormProvider>
  );
};

export default MoveFileEditMeterDetails;
