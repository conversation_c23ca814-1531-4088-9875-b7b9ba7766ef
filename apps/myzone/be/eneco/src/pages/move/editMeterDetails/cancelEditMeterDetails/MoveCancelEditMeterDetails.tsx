/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import {
  MoveEnergyTypeDto,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeter,
} from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { MoveEditMeterDetailsRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
// eslint-disable-next-line dxp-rules/no-dialog-from-sparky
import {
  Box,
  Bucket,
  Button,
  ButtonLink,
  Dialog,
  Form,
  Heading,
  NotificationBox,
  Skeleton,
  Stack,
  Text,
} from '@sparky';
import { ElectricityIcon, GasIcon } from '@sparky/icons';
import { NotificationBoxProps } from '@sparky/types';

import DataErrorNotification from '../../../../components/DataErrorNotification';
import ReadFormField from '../../../../components/ReadFormField';
import { useRedirectAndNotifyBE } from '../../../../hooks/useRedirectAndNotifyBE';
import { addressSearchParam } from '../../../../utils/addSearchParams';
import { isDateWithin28Days } from '../../../../utils/date/is-date-within-28-days';
import { moveStepNameToLink } from '../../../../utils/flow/flowStepNameToLink';
import { meterRegisterMapper, singleRegisterMapper } from '../../../../utils/move/meterRegisterMapper';
import { MeterReadingConfig, MeterReadingInputs } from '../../components/MeterReadingInputs';
import { useMove } from '../../hooks/useMove';
import {
  METER_REGISTERS_SCHEMA,
  MeterAndRegisterType,
  optionalPositiveNumber,
} from '../utils/moveEditMeterDetailsUtils';

const MoveCancelEditMeterDetails: FC = () => {
  const { fields } = useContent<MoveEditMeterDetailsRenderingExtended>();
  const { selectedAccount, isCurrentAccountReader } = useSelfServiceAccount();
  const { path, move, isLoading, moveCacheKey, errorMove } = useMove();
  const { mutate } = useSWRConfig();
  const { push } = useRouter();
  const redirectAndNotify = useRedirectAndNotifyBE();
  const addressIdentifier = path.addressIdentifier;
  const [error, setError] = useState<NotificationBoxProps | null>(null);
  const [showMeterReadingsDialog, setMeterReadingsDialog] = useState(false);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);
  const [readings, setReadings] = useState<MeterReadingConfig[][]>([]);
  const meters = move?.oldAddress?.meters ?? [];
  const metersAndRegisters: MeterAndRegisterType[] = meters
    .map(meter => ({
      ...meter,
      mappedRegister: meterRegisterMapper(meter),
    }))
    .filter(meter => !meter.isDigital)
    .sort((a, b) => (a.energyType ?? '').localeCompare(b.energyType ?? ''));

  const hasOnlyDigitalMeters = meters?.length && meters.every(x => x.isDigital);

  const moveEnergyTypes: MoveEnergyTypeDto[] = ['Gas', 'Electricity'];
  const MeterReadingsSchemaObject = yup.object({
    meters: yup.array().of(
      yup.object({
        [METER_REGISTERS_SCHEMA.METER_VALUE]: optionalPositiveNumber(
          fields.meterDetails.singleMeterReadingFormField.value.validationMessage,
        ),
        [METER_REGISTERS_SCHEMA.DAY_METER_VALUE]: optionalPositiveNumber(
          fields.meterDetails.dayMeterReadingFormField.value.validationMessage,
        ),
        [METER_REGISTERS_SCHEMA.NIGHT_EXCLUSIVE_METER_VALUE]: optionalPositiveNumber(
          fields.meterDetails.exclusiveNightMeterReadingFormField.value.validationMessage,
        ),
        [METER_REGISTERS_SCHEMA.NIGHT_METER_VALUE]: optionalPositiveNumber(
          fields.meterDetails.nightMeterReadingFormField.value.validationMessage,
        ),
        meterNumber: yup.string(),
        energyType: yup.mixed().oneOf(moveEnergyTypes),
        ean: yup.string(),
        oldEAN: yup.string(),
        oldMeterNumber: yup.string(),
      }),
    ),
  });

  type FormValues = yup.InferType<typeof MeterReadingsSchemaObject>;

  const form = useForm<FormValues>({
    resolver: yupResolver(MeterReadingsSchemaObject),
    defaultValues: { meters: [] },
  });

  const { setValue } = form;

  const submitForm: SubmitHandler<FormValues> = async ({ meters: values }) => {
    if (values) {
      const responses: any[] = [];
      try {
        setIsLoadingSubmit(true);
        // We use sequential execution here instead of Promise.all
        // to avoid race conditions. Each API call must finish before the next one gets fired.
        for (const formValue of values) {
          const response =
            await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeter(
              {
                path: {
                  accountNumber: selectedAccount.crmAccountNumber,
                  addressIdentifier,
                  locationType: 'Old',
                },
                body: {
                  ...formValue,
                  meterValue: formValue.meterValue ?? null,
                  dayMeterValue: formValue.dayMeterValue ?? null,
                  nightMeterValue: formValue.nightMeterValue ?? null,
                  nightExclusiveMeterValue: formValue.nightExclusiveMeterValue ?? null,
                  energyType: formValue.energyType as MoveEnergyTypeDto,
                  oldEAN: formValue.oldEAN,
                  oldMeterNumber: formValue.oldMeterNumber,
                },
              },
            );
          responses.push(response);
        }

        const isOk = responses.every(response => response.response.ok);
        mutate(moveCacheKey);
        if (isOk) {
          const url =
            moveStepNameToLink('CorrespondenceAddress', fields.navigation.cancelButtonLinkList, 'Cancel') +
            addressSearchParam(path.addressIdentifier);
          push(url);
        } else {
          throw new Error('one or more http calls failed');
        }
      } catch (e) {
        void e;
        setIsLoadingSubmit(false);
        setError({
          isAlert: false,
          text: fields.meterDetails.errorNotification.value.content,
          variant: fields.meterDetails.errorNotification.value.variant,
        });
        return;
      }
    }
  };

  const bucketElecTitle = (
    <Stack direction={'row'} alignY={'center'}>
      <ElectricityIcon color="iconElectricity" />
      <Text>{fields.meterDetails.electricityTitle?.value}</Text>
    </Stack>
  );
  const bucketGasTitle = (
    <Stack direction={'row'} alignY={'center'}>
      <GasIcon color="iconGas" />
      <Text>{fields.meterDetails.gasTitle?.value}</Text>
    </Stack>
  );

  useEffect(() => {
    const url =
      moveStepNameToLink('CorrespondenceAddress', fields.navigation.cancelButtonLinkList, 'Cancel') +
      addressSearchParam(path.addressIdentifier);

    if (isLoading) return;

    if (hasOnlyDigitalMeters) {
      redirectAndNotify({
        route: url,
        variant: fields.meterDetails.digitalMeterNotification.value.variant,
        title: fields.meterDetails.digitalMeterNotification.value.title,
        text: <RichText html={fields.meterDetails.digitalMeterNotification.value.content} />,
        headingLevel: 'h3',
      });
    }

    if (!isDateWithin28Days(move?.oldAddress?.keyTransfer ?? new Date().toISOString())) push(url);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasOnlyDigitalMeters, path.addressIdentifier, redirectAndNotify, move?.oldAddress?.keyTransfer, isLoading]);

  useEffect(() => {
    const readings: MeterReadingConfig[][] = [];
    metersAndRegisters?.length &&
      metersAndRegisters.forEach((meterAndRegister, index) => {
        if (meterAndRegister.ean) {
          setValue(`meters.${index}.ean`, meterAndRegister.ean);
          setValue(`meters.${index}.oldEAN`, meterAndRegister.ean);
        }
        if (meterAndRegister.meterNumber) {
          setValue(`meters.${index}.meterNumber`, meterAndRegister.meterNumber);
          setValue(`meters.${index}.oldMeterNumber`, meterAndRegister.meterNumber);
        }
        if (meterAndRegister.energyType) {
          setValue(`meters.${index}.energyType`, meterAndRegister.energyType);
        }

        readings[index] = [];

        meterAndRegister.registers?.forEach(register => {
          const mappedRegister = singleRegisterMapper(register);

          if (mappedRegister.showDayMeterReading) {
            readings[index]?.push({
              name: `meters.[${index}].${METER_REGISTERS_SCHEMA.DAY_METER_VALUE}`,
              schemaKey: METER_REGISTERS_SCHEMA.DAY_METER_VALUE,
              label: fields.meterDetails.dayMeterReadingFormField.value.label,
              placeholder: fields.meterDetails.dayMeterReadingFormField.value.placeholder,
              meterIndex: index,
            });

            setValue(
              readings[index].find(x => x.schemaKey === METER_REGISTERS_SCHEMA.DAY_METER_VALUE)?.name as any,
              register.meterReading,
            );
          }
          if (mappedRegister.showNightMeterReading) {
            readings[index]?.push({
              name: `meters.[${index}].${METER_REGISTERS_SCHEMA.NIGHT_METER_VALUE}`,
              schemaKey: METER_REGISTERS_SCHEMA.NIGHT_METER_VALUE,
              label: fields.meterDetails.nightMeterReadingFormField.value.label,
              placeholder: fields.meterDetails.nightMeterReadingFormField.value.placeholder,
              meterIndex: index,
            });

            setValue(
              readings[index].find(x => x.schemaKey === METER_REGISTERS_SCHEMA.NIGHT_METER_VALUE)?.name as any,
              register.meterReading,
            );
          }
          if (mappedRegister.showExclNightMeterReading) {
            readings[index]?.push({
              name: `meters.[${index}].${METER_REGISTERS_SCHEMA.NIGHT_EXCLUSIVE_METER_VALUE}`,
              schemaKey: METER_REGISTERS_SCHEMA.NIGHT_EXCLUSIVE_METER_VALUE,
              label: fields.meterDetails.exclusiveNightMeterReadingFormField.value.label,
              placeholder: fields.meterDetails.exclusiveNightMeterReadingFormField.value.placeholder,
              meterIndex: index,
            });

            setValue(
              readings[index].find(x => x.schemaKey === METER_REGISTERS_SCHEMA.NIGHT_EXCLUSIVE_METER_VALUE)
                ?.name as any,
              register.meterReading,
            );
          }
          if (mappedRegister.showSingleMeterReading) {
            readings[index]?.push({
              name: `meters.[${index}].${METER_REGISTERS_SCHEMA.METER_VALUE}`,
              schemaKey: METER_REGISTERS_SCHEMA.METER_VALUE,
              label: fields.meterDetails.singleMeterReadingFormField.value.label,
              placeholder: fields.meterDetails.singleMeterReadingFormField.value.placeholder,
              meterIndex: index,
            });

            setValue(
              readings[index].find(x => x.schemaKey === METER_REGISTERS_SCHEMA.METER_VALUE)?.name as any,
              register.meterReading,
            );
          }
        });
      });
    setReadings(readings);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, setValue]);

  const Skeletons = () => {
    return (
      <>
        <Skeleton width={'100%'} height={500}></Skeleton>
        <Skeleton width={'100%'} height={500}></Skeleton>
      </>
    );
  };

  if (!isLoading && errorMove !== undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <FormProvider {...form}>
      <Form onSubmit={form.handleSubmit(submitForm)}>
        <Box paddingTop={10}>
          <Stack gap="10">
            <Stack gap="6" alignX={'start'}>
              <Heading as="h1" size="M">
                {fields.meterDetails.title?.value}
              </Heading>
              <RichText html={fields.meterDetails.description.value} />
              {isLoading ? (
                <Skeletons />
              ) : (
                <>
                  {metersAndRegisters.map((meterAndRegister, index) => {
                    return (
                      <Bucket
                        key={meterAndRegister.ean}
                        title={meterAndRegister.energyType === 'Electricity' ? bucketElecTitle : bucketGasTitle}>
                        <Bucket.Content>
                          <>
                            <ReadFormField
                              label={fields.meterDetails.eanCodeFormField.value.label}
                              value={meterAndRegister.ean}
                            />
                            <ReadFormField
                              label={fields.meterDetails.meterNumberFormField.value.label}
                              value={meterAndRegister.meterNumber}
                            />
                            <MeterReadingInputs
                              meterAndRegister={meterAndRegister}
                              isCurrentAccountReader={isCurrentAccountReader}
                              meterReadingsDialogTitle={fields.meterDetails.meterReadingsInfoDialog.value.triggerText}
                              onOpenMeterReadingsDialog={() => setMeterReadingsDialog(true)}
                              readings={readings[index] ?? []}
                            />
                          </>
                        </Bucket.Content>
                      </Bucket>
                    );
                  })}
                </>
              )}
            </Stack>
            <NotificationBox
              title={fields.meterDetails.infoCancelFlowNotification.value.title}
              variant={fields.meterDetails.infoCancelFlowNotification.value.variant}
              text={<RichText html={fields.meterDetails.infoCancelFlowNotification.value.content} />}
              isAlert={false}
            />
            <Stack.Item shrink>
              <Stack gap="2" direction={'row'}>
                <Button type="submit" isLoading={isLoadingSubmit}>
                  {fields.navigation.nextButtonText.value}
                </Button>
                <ButtonLink
                  action="secondary"
                  href={
                    moveStepNameToLink('OldAddressKeyTransfer', fields.navigation.cancelButtonLinkList, 'Cancel') +
                    addressSearchParam(addressIdentifier)
                  }>
                  {fields.navigation.previousButtonText.value}
                </ButtonLink>
              </Stack>
            </Stack.Item>
            {error && <NotificationBox {...error} />}
          </Stack>
        </Box>
      </Form>

      <Dialog
        isOpen={showMeterReadingsDialog}
        setOpen={setMeterReadingsDialog}
        trigger={fields.meterDetails.meterReadingsInfoDialog.value.triggerText}
        title={fields.meterDetails.meterReadingsInfoDialog.value.title}
        description={fields.meterDetails.meterReadingsInfoDialog.value.content}
        width="regular"
      />
    </FormProvider>
  );
};

export default MoveCancelEditMeterDetails;
