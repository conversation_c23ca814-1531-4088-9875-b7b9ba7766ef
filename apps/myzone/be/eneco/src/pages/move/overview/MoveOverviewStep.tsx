import { useState } from 'react';

import { useRouter } from 'next/router';
import { useSWRConfig } from 'swr';

import RichText from '@components/RichText/RichText';
import { putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverview } from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { MoveOverviewStepRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
import { Bleed, Box, Button, Heading, NotificationBox, Skeleton, Stack } from '@sparky';
import { NotificationBoxProps } from '@sparky/types';

import MoveOverviewAddressEodReceived from './components/MoveOverviewAddressEodReceived';
import MoveOverviewNewAddressDetails from './components/MoveOverviewNewAddressDetails';
import MoveOverviewOldAddressDetails from './components/MoveOverviewOldAddressDetails';
import DataErrorNotification from '../../../components/DataErrorNotification';
import { useRedirectAndNotifyBE } from '../../../hooks/useRedirectAndNotifyBE';
import { addressSearchParam } from '../../../utils/addSearchParams';
import { moveStepNameToLink } from '../../../utils/flow/flowStepNameToLink';
import { useMove } from '../hooks/useMove';

const MoveOverviewStep = () => {
  const { fields } = useContent<MoveOverviewStepRenderingExtended>();
  const { path, moveCacheKey, move, isLoading, errorMove } = useMove();
  const { push } = useRouter();
  const { isCurrentAccountReader, isProspect } = useSelfServiceAccount();
  const { mutate } = useSWRConfig();

  const addressIdentifier = path?.addressIdentifier;
  const redirectAndNotify = useRedirectAndNotifyBE();

  const [error, setError] = useState<NotificationBoxProps>();
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);
  const isContinueFlow = move?.flow === 'ContinueFlow';

  if (isProspect || isCurrentAccountReader || (move && !move.status))
    push(fields.overviewContent.previousButtonLink.value.href + addressSearchParam(addressIdentifier));

  const isProd = process.env.NODE_ENV === 'production';
  if (isProd && !addressIdentifier) push(fields.overviewContent.previousButtonLink.value.href);

  const submitMoveFile = async () => {
    try {
      setIsLoadingSubmit(true);
      const { response } =
        await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverview(
          { path },
        );

      if (response.ok) {
        mutate(moveCacheKey);
        const url =
          moveStepNameToLink('Done', fields.navigation.continueButtonLinkList, 'Continue') +
          addressSearchParam(addressIdentifier);
        redirectAndNotify({
          route: url,
          variant: 'success',
          title:
            fields.overviewContent.moveFileSuccessfullyCreatedNotification?.value?.title ??
            'Move aangemaakt [not in sitecore]',
          text:
            fields.overviewContent.moveFileSuccessfullyCreatedNotification?.value?.content ??
            'Je move is correct aangemaakt [not in sitecore]',
          headingLevel: 'h3',
        });
      } else {
        setIsLoadingSubmit(false);
        setError({
          isAlert: false,
          text: fields.overviewContent.failedNotification.value.content,
          title: fields.overviewContent.failedNotification.value.title,
          variant: fields.overviewContent.failedNotification.value.variant,
        });
      }
    } catch {
      setIsLoadingSubmit(false);
      setError({
        isAlert: false,
        text: fields.overviewContent.failedNotification.value.content,
        title: fields.overviewContent.failedNotification.value.title,
        variant: fields.overviewContent.failedNotification.value.variant,
      });
    }
  };

  if (isLoading)
    return (
      <Stack gap="6">
        <Bleed top="2">
          <Stack gap="4">
            <Box padding={6}>
              <Stack gap={1}>
                <Skeleton width="100%" height={50} />
                <Skeleton width="100%" height={100} />
                <Skeleton width="100%" height={250} />
                <Skeleton width="100%" height={250} />
              </Stack>
            </Box>
          </Stack>
        </Bleed>
      </Stack>
    );

  if (!isLoading && errorMove !== undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Box paddingTop="12">
      <Stack gap="10">
        <Stack gap="4">
          <Heading as="h1" size="M">
            {fields.overviewContent.title.value}
          </Heading>
          <RichText html={fields.overviewContent.description.value} />
        </Stack>
        <Stack gap="6">
          {move?.oldAddress?.eod ? (
            <MoveOverviewAddressEodReceived
              title={fields.oldAddressCard.addressLabel.value}
              description={fields.oldAddressCard.eodSuccessDescription.value}
              eodFileNameLabel={fields.oldAddressCard.eodFileNameLabel.value}
              productsOnAddressLabel={fields.oldAddressCard.energyTypeLabel.value}
              fileName={move.oldAddress.eod?.fileName?.toString() ?? ''}
              showProductsOnAddres={true}
              eodChangeLink={{
                ...fields.oldAddressCard.eodChangeLink.value,
                href: moveStepNameToLink(
                  'Eod',
                  isContinueFlow ? fields.navigation.continueButtonLinkList : fields.navigation.cancelButtonLinkList,
                  isContinueFlow ? 'Continue' : 'Cancel',
                ),
              }}
              props={{ ...move }}
            />
          ) : (
            <MoveOverviewOldAddressDetails {...move} />
          )}

          {isContinueFlow &&
            (move?.newAddress?.eod ? (
              <MoveOverviewAddressEodReceived
                title={fields.newAddressCard.addressLabel.value}
                description={fields.newAddressCard.eodSuccessDescription.value}
                eodFileNameLabel={fields.newAddressCard.eodFileNameLabel.value}
                productsOnAddressLabel={fields.newAddressCard.energyTypeLabel.value}
                fileName={move.newAddress.eod?.fileName?.toString() ?? ''}
                eodChangeLink={{
                  ...fields.newAddressCard.eodChangeLink.value,
                  href: moveStepNameToLink('Eod', fields.navigation.continueButtonLinkList, 'Continue'),
                }}
                showProductsOnAddres={false}
                props={{ ...move }}
              />
            ) : (
              <MoveOverviewNewAddressDetails {...move} />
            ))}
          {error && <NotificationBox {...error} />}
          <Stack gap={4} direction="row">
            <Button onClick={() => submitMoveFile()} isLoading={isLoadingSubmit}>
              {fields.overviewContent.confirmButtonLink.value.text}
            </Button>
          </Stack>
        </Stack>
      </Stack>
    </Box>
  );
};

export default MoveOverviewStep;
