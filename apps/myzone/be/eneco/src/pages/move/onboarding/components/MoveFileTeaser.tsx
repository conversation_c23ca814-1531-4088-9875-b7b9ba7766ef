import { FC } from 'react';

import { MoveStepDto, MoveFileDetailResponse } from '@dc-be/client';
import { useContent } from '@sitecore/common';
import { MoveOnboardingRenderingExtended } from '@sitecore/types/manual/MoveOnboarding';
import { Box, Card, Heading, Stack, Stretch, TextLink, Badge } from '@sparky';
import { Attention, House, HouseCheckmark, Moving, OnePerson } from '@sparky/illustrations';

import { DetailsBox } from './DetailsBox';
import { useStatusMap } from './MoveStatus';
import { addressSearchParam } from '../../../../utils/addSearchParams';
import { moveStepNameToLink } from '../../../../utils/flow/flowStepNameToLink';

interface MoveFileTeaserProps {
  move: MoveFileDetailResponse;
  addressIdentifier: string;
  isCurrentAccountReader: boolean;
  onCancelMoveClick: () => void;
}

export const MoveFileTeaser: FC<MoveFileTeaserProps> = ({
  move,
  addressIdentifier,
  isCurrentAccountReader,
  onCancelMoveClick,
}) => {
  const { fields } = useContent<MoveOnboardingRenderingExtended>();
  const statusMap = useStatusMap(fields.moveFileTeaser.statusesList.value);
  const isContinueFlow = move.flow === 'ContinueFlow';
  const isMoveFile = !!move.moveRequestId;

  function getNextStepUrl(moveStep: MoveStepDto) {
    if (isContinueFlow) return moveStepNameToLink(moveStep, fields.onboarding.continueButtonLinkList, 'Continue');
    return moveStepNameToLink(moveStep, fields.onboarding.cancelButtonLinkList, 'Cancel');
  }

  return (
    <Card>
      <Box padding={6}>
        <Stack gap="4">
          <Stack gap={2} direction={'row'} alignY={'center'}>
            {isContinueFlow ? <Moving /> : <Attention />}
            <Stack.Item grow>
              <Stack gap={4}>
                <Stretch>
                  <Stack direction={'row'} gap={2} wrap alignX={'justify'}>
                    {isContinueFlow ? (
                      <Heading as="h2" size="S">
                        {fields.moveFileTeaser.title.value} {isMoveFile ? `#${move.moveRequestId}` : ''}
                      </Heading>
                    ) : (
                      <Heading as="h2" size="S">
                        {fields.moveCancelTeaser?.title.value}
                      </Heading>
                    )}
                    {statusMap.get(move.status) && (
                      <Badge color={statusMap.get(move.status)?.color}>{statusMap.get(move.status)?.label ?? ''}</Badge>
                    )}
                  </Stack>
                </Stretch>
                {!isCurrentAccountReader && !isMoveFile && (
                  <Stack gap="4">
                    {move.nextStep && (
                      <TextLink
                        emphasis="high"
                        href={getNextStepUrl(move.nextStep) + addressSearchParam(addressIdentifier)}>
                        {fields.moveFileTeaser.continueFlowLink.value.text}
                      </TextLink>
                    )}
                    {move.status !== 'Closed' && (
                      <TextLink onClick={onCancelMoveClick} emphasis="high">
                        {isContinueFlow
                          ? fields.moveFileTeaser.cancelFlowLink.value.text
                          : fields.moveCancelTeaser?.cancelFlowLink.value.text}
                      </TextLink>
                    )}
                  </Stack>
                )}
              </Stack>
            </Stack.Item>
          </Stack>
          {isContinueFlow && isMoveFile && (
            <>
              <Stack gap={2}>
                <DetailsBox
                  icon={<House size="small" />}
                  title={fields.moveFileTeaser.changeOldAddressTitle.value}
                  linkText={fields.moveFileTeaser.changeOldAddressLink.value.text}
                  linkHref={
                    fields.moveFileTeaser.changeOldAddressLink.value.href + addressSearchParam(addressIdentifier)
                  }
                  status={statusMap.get(move.oldAddress?.status)}
                />
                <DetailsBox
                  icon={<HouseCheckmark size="small" />}
                  title={fields.moveFileTeaser.changeNewAddressTitle.value}
                  linkText={fields.moveFileTeaser.changeNewAddressLink.value.text}
                  linkHref={
                    fields.moveFileTeaser.changeNewAddressLink.value.href + addressSearchParam(addressIdentifier)
                  }
                  status={statusMap.get(move.newAddress?.status)}
                />
                <DetailsBox
                  icon={<OnePerson size="small" />}
                  title={fields.moveFileTeaser.changeContactDetailsTitle.value}
                  linkText={fields.moveFileTeaser.changeContactDetailsLink.value.text}
                  linkHref={
                    fields.moveFileTeaser.changeContactDetailsLink.value.href + addressSearchParam(addressIdentifier)
                  }
                />
              </Stack>
              {move.status !== 'Closed' && !isCurrentAccountReader && (
                <TextLink onClick={onCancelMoveClick} emphasis="high">
                  {fields.moveFileTeaser.cancelFlowLink.value.text}
                </TextLink>
              )}
            </>
          )}
        </Stack>
      </Box>
    </Card>
  );
};
