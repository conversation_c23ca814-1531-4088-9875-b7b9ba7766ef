import { FC, useState } from 'react';

import { isFuture, isToday } from 'date-fns';
import { useRouter } from 'next/router';
import { useSWRConfig } from 'swr';

import RichText from '@components/RichText/RichText';
import {
  deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMove,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { MoveOnboardingRenderingExtended } from '@sitecore/types/manual/MoveOnboarding';
import { AlertDialog, Bleed, Stack, NotificationBox } from '@sparky';

import { AddressFilter } from '../../../components/AddressFilter';
import { useAddressOptions } from '../../../hooks/useAddressOptions';
import { useMove } from '../hooks/useMove';
import { MoveEmptyState } from './components/MoveEmptyState';
import { MoveFileTeaser } from './components/MoveFileTeaser';
import { MoveNotifications } from './components/MoveNotifications';
import { MoveOnboardingSkeleton } from './components/MoveOnboardingSkeleton';
import DataErrorNotification from '../../../components/DataErrorNotification';

const MoveOnboarding: FC = () => {
  const { fields } = useContent<MoveOnboardingRenderingExtended>();
  const { isProspect, isCurrentAccountReader, selectedAccount } = useSelfServiceAccount();
  const { addressIdentifier, isLoading: isLoadingAddress, error: errorAddress } = useAddressOptions();
  const { move, isLoading: isLoadingMove, moveCacheKey, session, errorMove } = useMove(addressIdentifier);
  const { mutate } = useSWRConfig();
  const [showModal, setShowModal] = useState(false);
  const { reload } = useRouter();
  const isLoading = isLoadingAddress || isLoadingMove;

  const { data, error } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier: addressIdentifier,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/products`],
    session,
  );

  const products = unwrapData(data);
  const isActiveContract = products?.activeProducts?.some(p => !p.endTime || isFuture(p.endTime) || isToday(p.endTime));

  const handleCancelMoveClick = () => {
    setShowModal(true);
  };

  const cancelCurrentMove = async () => {
    const { response } =
      await deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMove({
        path: {
          accountNumber: selectedAccount.crmAccountNumber,
          addressIdentifier: addressIdentifier,
        },
      });
    if (!response.ok) {
      // TODO: use sc error
    }
    await mutate(moveCacheKey);
    reload();
  };

  if (isProspect) {
    return null;
  }

  const fetchingAccountError = error != undefined && data === undefined;
  const fetchingMoveError = errorMove != undefined && move === undefined;
  const fetchingAddressError = errorAddress != undefined && addressIdentifier === null;

  if (!isLoading && (fetchingAccountError || fetchingMoveError || fetchingAddressError)) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  if (isCurrentAccountReader && !move) {
    return (
      <NotificationBox
        title={fields.onboarding.emptyReaderStateNotification.value.title}
        text={fields.onboarding.emptyReaderStateNotification.value.content}
        variant={fields.onboarding.emptyReaderStateNotification.value.variant}
        isAlert={false}
      />
    );
  }

  return (
    <Bleed top="2">
      <Stack gap="4">
        <AddressFilter label={fields.onboarding.deliveryAddressLabel.value} />

        {!isLoading ? (
          !isActiveContract ? (
            <NotificationBox
              isAlert={false}
              text={<RichText html={fields.onboarding.inactiveContractNotification.value.content} />}
              variant={fields.onboarding.inactiveContractNotification.value.variant}
              title={fields.onboarding.inactiveContractNotification.value.title}
            />
          ) : (
            <>
              {move && <MoveNotifications move={move} />}

              {!move || move?.status === 'None' || move?.status === 'Cancelled' ? (
                <MoveEmptyState addressIdentifier={addressIdentifier} isCurrentAccountReader={isCurrentAccountReader} />
              ) : (
                <MoveFileTeaser
                  move={move}
                  addressIdentifier={addressIdentifier}
                  isCurrentAccountReader={isCurrentAccountReader}
                  onCancelMoveClick={handleCancelMoveClick}
                />
              )}
            </>
          )
        ) : (
          <MoveOnboardingSkeleton />
        )}
      </Stack>
      <AlertDialog
        title={fields.moveFileTeaser.cancelMoveDialog.value.title}
        description={<RichText html={fields.moveFileTeaser.cancelMoveDialog.value.content} />}
        isOpen={showModal}
        setOpen={setShowModal}
        confirmText={fields.moveFileTeaser.cancelMoveDialog.value.submitButtonText || ''}
        onConfirm={cancelCurrentMove}
        onDeny={() => {}}
        denyText={fields.moveFileTeaser.cancelMoveDialog.value.cancelButtonText || ''}
      />
    </Bleed>
  );
};

export default MoveOnboarding;
