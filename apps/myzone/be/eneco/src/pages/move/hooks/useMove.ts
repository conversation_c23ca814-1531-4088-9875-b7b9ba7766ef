import { getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetail } from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';

import { ADDRESS_PARAM } from '../../../utils/addSearchParams';

/**
 * A hook to fetch the move details for a specific address, if the addressIdentifier is not provided it will be fetched from the searchParams
 * @param addressIdentifier
 * @returns The move details
 * @returns The search param you can use for next/previous link f.e. ?address=123
 * @returns The path you can use for requests to the DC BE
 * @returns The session you can use for requests to the DC BE
 */
export const useMove = (addressIdentifierParam?: string) => {
  const {
    selectedAccount: { crmAccountNumber: accountNumber },
  } = useSelfServiceAccount();
  const { query } = useRouter();
  const { data: session } = useSession();
  const addressIdentifier = addressIdentifierParam ? addressIdentifierParam : (query[ADDRESS_PARAM] as string);
  const moveCacheKey = `/accounts/${accountNumber}/delivery-addresses/${addressIdentifier}/move/detail`;

  const { data, isLoading, error } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetail,
    {
      path: {
        accountNumber,
        addressIdentifier,
      },
    },
    [moveCacheKey],
    session,
  );

  return {
    isLoading,
    move: unwrapData(data),
    moveCacheKey,
    path: { accountNumber, addressIdentifier },
    session,
    errorMove: error,
  };
};
