import { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { format } from 'date-fns';
import { Form<PERSON>rovider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransfer } from '@dc-be/client';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { MoveInDateStepRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
import { AlertDialog, Box, Bucket, Button, ButtonLink, Checkbox, Form, Heading, NotificationBox, Stack } from '@sparky';
import { NotificationBoxProps } from '@sparky/types';

// eslint-disable-next-line @nx/enforce-module-boundaries
import { Input } from '../../../../../../../../libs/sparky/src/components/Input/Input';
import DataErrorNotification from '../../../components/DataErrorNotification';
import ReadFormField from '../../../components/ReadFormField';
import useBusLabel from '../../../hooks/useBusLabel';
import { addressSearchParam } from '../../../utils/addSearchParams';
import { isDateNotTooFarInFuture, isDateNotTooFarInPast } from '../../../utils/date/move-date-validation';
import { moveStepNameToLink } from '../../../utils/flow/flowStepNameToLink';
import { useMove } from '../hooks/useMove';
import { addressFormatter } from '../overview/address-formatter';
const MoveInDateStep: FC = () => {
  const { move, path, moveCacheKey, errorMove, isLoading } = useMove();
  const {
    fields: { moveInDateForm, credentialsCard, navigation },
  } = useContent<MoveInDateStepRenderingExtended>();
  const { mutate } = useSWRConfig();
  const { push } = useRouter();
  const addressIdentifier = path?.addressIdentifier;
  const busLabel = useBusLabel();

  const [showTooFarInFutureDialog, setShowTooFarInFutureDialog] = useState(false);
  const [error, setError] = useState<NotificationBoxProps | null>(null);
  const [showTooFarInPastDialog, setShowTooFarInPastDialog] = useState(false);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  const previousButtonLink = `${moveStepNameToLink('NewAddress', navigation.continueButtonLinkList, 'Continue')}${addressSearchParam(path.addressIdentifier)}`;

  const isProd = process.env.NODE_ENV === 'production';
  if (isProd && !path.addressIdentifier)
    push(moveStepNameToLink('Done', navigation.continueButtonLinkList, 'Continue'));

  const FormSchema = yup.object({
    keyTransferDateUnknown: yup.boolean(),
    keyTransferDate: yup
      .string()
      .test('is-required', moveInDateForm.dateFormField.value.requiredMessage, function (value) {
        const keyTransferDateUnknown = this.parent.keyTransferDateUnknown as boolean;
        if (keyTransferDateUnknown) return true;
        if (typeof value !== 'string') return false;
        const date = new Date(value);
        return !isNaN(date.getTime());
      })
      .test('is-date-too-far-in-past', moveInDateForm.datePast28DaysDialog.value.triggerText, (value, context) =>
        isDateNotTooFarInPast(value, context),
      )
      .test('is-date-too-far-in-future', moveInDateForm.dateFuture1YearDialog.value.triggerText, (value, context) =>
        isDateNotTooFarInFuture(value, context),
      ),
  });

  type FormValues = yup.InferType<typeof FormSchema>;
  const resolver = yupResolver(FormSchema);

  const form = useForm<FormValues>({
    resolver,
    mode: 'onChange',
    values: {
      keyTransferDate: move?.newAddress?.deliveryDate ? format(move?.newAddress?.deliveryDate, 'yyyy-MM-dd') : '',
    },
  });
  const {
    register,
    formState: { errors },
    watch,
    setValue,
  } = form;

  const keyTransferDate = watch('keyTransferDate');
  const keyTransferDateUnknown = watch('keyTransferDateUnknown');

  useEffect(() => {
    if (keyTransferDateUnknown) setValue('keyTransferDate', undefined);
  }, [keyTransferDateUnknown, setValue]);

  const checkToShowDialog = () => {
    if (keyTransferDate) {
      setShowTooFarInFutureDialog(!isDateNotTooFarInFuture(keyTransferDate));
      setShowTooFarInPastDialog(!isDateNotTooFarInPast(keyTransferDate));
    }
  };

  const submitForm: SubmitHandler<FormValues> = async ({ keyTransferDateUnknown, keyTransferDate }) => {
    try {
      setIsLoadingSubmit(true);
      const { response } =
        await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransfer(
          {
            path: {
              ...path,
              locationType: 'New',
            },
            body: {
              keyTransferDate: keyTransferDate === '' ? undefined : keyTransferDate,
              keyTransferDateUnknown,
            },
          },
        );
      if (!response.ok) {
        setIsLoadingSubmit(false);
        return setError({
          isAlert: false,
          text: 'error',
          title: '',
          variant: 'error',
        });
      }
      await mutate(moveCacheKey);
      if (move?.oldAddress?.eod) {
        const url = moveStepNameToLink('ConfirmationOverview', navigation.continueButtonLinkList, 'Continue');
        return push(`${url}${addressSearchParam(addressIdentifier)}`);
      }
      const url = moveStepNameToLink('OldAddressKeyTransfer', navigation.continueButtonLinkList, 'Continue');
      push(`${url}${addressSearchParam(addressIdentifier)}`);
    } catch (e) {
      void e;
      setIsLoadingSubmit(false);
      return setError({
        isAlert: false,
        text: 'error',
        title: '',
        variant: 'error',
      });
    }
  };

  if (!isLoading && errorMove !== undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <FormProvider {...form}>
      <Form onSubmit={form.handleSubmit(submitForm)}>
        <Box paddingTop="10">
          <Stack gap="10">
            <Stack gap="6" alignX={'start'}>
              <Heading as="h1" size="M">
                {moveInDateForm.title.value}
              </Heading>
              <RichText html={moveInDateForm.description.value}></RichText>
              <Bucket title={credentialsCard.title.value}>
                <Bucket.Content>
                  <Stack gap="4">
                    <ReadFormField
                      label={credentialsCard.newAddressLabel.value}
                      value={addressFormatter(move?.newAddress?.newAddress, busLabel)}
                    />
                  </Stack>
                </Bucket.Content>
              </Bucket>
              <Input
                label={moveInDateForm.dateFormField.value.label}
                id="keyExchangeDate"
                type="date"
                error={errors.keyTransferDate?.message}
                disabled={keyTransferDateUnknown}
                {...register('keyTransferDate')}
                autoComplete="off"
              />
              <Checkbox
                label={moveInDateForm.dateUnknownFormField.value.label}
                hint={<RichText html={moveInDateForm.dateUnknownFormField.value.hint} />}
                {...register('keyTransferDateUnknown')}
              />
              {keyTransferDateUnknown && (
                <NotificationBox
                  title={moveInDateForm.dateUnknownNotification.value.title}
                  variant={moveInDateForm.dateUnknownNotification.value.variant}
                  text={<RichText html={moveInDateForm.dateUnknownNotification.value.content} />}
                  isAlert={false}
                />
              )}
            </Stack>
            <Stack.Item shrink>
              <Stack gap="2" direction={'row'}>
                <Button type="submit" onClick={() => checkToShowDialog()} isLoading={isLoadingSubmit}>
                  {navigation.nextButtonText.value}
                </Button>
                <ButtonLink action="secondary" href={previousButtonLink}>
                  {navigation.previousButtonText.value}
                </ButtonLink>
              </Stack>
            </Stack.Item>
            {error && <NotificationBox {...error} />}
          </Stack>
        </Box>
      </Form>
      <AlertDialog
        title={moveInDateForm.dateFuture1YearDialog.value.title}
        isOpen={showTooFarInFutureDialog}
        setOpen={setShowTooFarInFutureDialog}
        onConfirm={() => {}}
        confirmText={moveInDateForm.dateFuture1YearDialog.value.submitButtonText || ''}
        denyText={moveInDateForm.dateFuture1YearDialog.value.cancelButtonText || ''}
        onDeny={() => {}}>
        <RichText html={moveInDateForm.dateFuture1YearDialog.value.content} />
      </AlertDialog>
      <AlertDialog
        title={moveInDateForm.datePast28DaysDialog.value.title}
        isOpen={showTooFarInPastDialog}
        setOpen={setShowTooFarInPastDialog}
        onConfirm={() => {}}
        confirmText={moveInDateForm.datePast28DaysDialog.value.submitButtonText || ''}
        denyText={moveInDateForm.datePast28DaysDialog.value.cancelButtonText || ''}
        onDeny={() => {}}>
        <RichText html={moveInDateForm.datePast28DaysDialog.value.content} />
      </AlertDialog>
    </FormProvider>
  );
};

export default MoveInDateStep;
