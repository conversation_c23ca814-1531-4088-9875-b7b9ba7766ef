import { FC, useEffect, useState } from 'react';

import RichText from '@components/RichText/RichText';
import { useContent } from '@sitecore/common';
import { MoveEODUploadRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
import { Stack, Box, Heading } from '@sparky';

import { EodChoiceForm } from './components/EodChoiceForm';
import { EodUploadForm } from './components/EodUploadForm';
import DataErrorNotification from '../../../components/DataErrorNotification';
import { useMove } from '../hooks/useMove';

const MoveEodUploadStep: FC = () => {
  const [step, setStep] = useState<0 | 1>(0);
  const { move, isLoading, errorMove } = useMove();
  const isContinueFlow = move?.flow === 'ContinueFlow';

  const { fields } = useContent<MoveEODUploadRenderingExtended>();

  useEffect(() => {
    if (!isContinueFlow && move?.oldAddress?.eod) {
      setStep(1);
    }

    if (move?.newAddress?.eod || move?.oldAddress?.eod) {
      setStep(1);
    }
  }, [move?.newAddress?.eod, move?.oldAddress?.eod]);

  if (!isLoading && errorMove !== undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Box paddingTop="10">
      <Stack gap="6">
        <Stack gap="2">
          <Heading as="h1" size="M">
            {fields.uploadForm.title.value}
          </Heading>
          <RichText html={fields.uploadForm.description.value} />
        </Stack>
        {step === 0 ? <EodChoiceForm setStep={setStep} /> : <EodUploadForm setStep={setStep} />}
      </Stack>
    </Box>
  );
};

export default MoveEodUploadStep;
