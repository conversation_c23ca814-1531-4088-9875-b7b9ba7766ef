import { FC, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Form<PERSON>rovider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { useRedirectAndNotify } from '@components-next/Notification/Notification';
import { putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContact } from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { MoveResidentChangeContactDetailsRendering } from '@sitecore/types/MoveResidentChangeContactDetails';
import { Bucket, Button, ButtonLink, Form, InputEmail, InputText, NotificationBox, Skeleton, Stack } from '@sparky';
import { NotificationBoxProps } from '@sparky/types';

import DataErrorNotification from '../../../components/DataErrorNotification';
import PhoneNumberInputField, { phoneValidationScheme } from '../../../components/PhoneNumberInputField';
import { addressSearchParam } from '../../../utils/addSearchParams';
import { useMove } from '../hooks/useMove';

const MoveResidentChangeContactDetails: FC = () => {
  const { move, path, moveCacheKey, isLoading, errorMove } = useMove();
  const { isCurrentAccountReader } = useSelfServiceAccount();
  const { fields } = useContent<MoveResidentChangeContactDetailsRendering>();
  const [error, setError] = useState<NotificationBoxProps | null>(null);
  const [isLoadingApi, setIsLoadingApi] = useState(false);
  const addressIdentifier = path?.addressIdentifier;
  const { mutate } = useSWRConfig();
  const redirectAndNotify = useRedirectAndNotify();
  const isNewAddress = fields.settings.isNewAddressFlag.value;

  const FormSchema = yup.object({
    firstName: yup.string().nullable(),
    lastName: yup.string().nullable(),
    phoneNumber: phoneValidationScheme({
      validationMessage: fields.contactForm.phoneNumberFormField.value.validationMessage,
      isRequired: false,
    }).nullable(),
    email: yup.string().email().nullable(),
  });

  type FormValues = yup.InferType<typeof FormSchema>;
  const resolver = yupResolver(FormSchema);

  const defaultValues = isNewAddress ? move?.newAddress?.contact : move?.oldAddress?.contact;

  const form = useForm<FormValues>({
    resolver,
    mode: 'onBlur',
    values: defaultValues,
  });
  const {
    formState: { errors },
    register,
  } = form;

  const submitForm: SubmitHandler<FormValues> = async data => {
    try {
      setIsLoadingApi(true);
      const { response } =
        await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContact(
          {
            path: { locationType: fields.settings.isNewAddressFlag.value ? 'New' : 'Old', ...path },
            body: { ...data },
          },
        );

      if (!response.ok) {
        return setError({
          isAlert: false,
          title: fields.contractDetails.generalErrorNotification.value.title,
          text: fields.contractDetails.generalErrorNotification.value.content,
          variant: fields.contractDetails.generalErrorNotification.value.variant,
        });
      }
      await mutate(moveCacheKey);
      redirectAndNotify({
        route: `${fields.contractDetails.saveButtonLink.value.href}${addressSearchParam(addressIdentifier)}`,
        variant: fields.contractDetails.saveSuccessNotification.value.variant,
        title: fields.contractDetails.saveSuccessNotification.value.title,
        text: fields.contractDetails.saveSuccessNotification.value.content,
        headingLevel: 'h3',
      });
    } catch {
      return setError({
        isAlert: false,
        title: fields.contractDetails.generalErrorNotification.value.title,
        text: fields.contractDetails.generalErrorNotification.value.content,
        variant: fields.contractDetails.generalErrorNotification.value.variant,
      });
    } finally {
      setIsLoadingApi(false);
    }
  };

  if (!isLoading && errorMove != undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <FormProvider {...form}>
      <Form onSubmit={form.handleSubmit(submitForm)}>
        <Stack gap={10}>
          <Bucket title={fields.contactForm?.title.value}>
            <Bucket.Content>
              {isLoading ? (
                <>
                  <Skeleton width={'100%'} height={100} />
                  <Skeleton width={'100%'} height={100} />
                  <Skeleton width={'100%'} height={100} />
                  <Skeleton width={'100%'} height={100} />
                  <Skeleton width={'100%'} height={100} />
                </>
              ) : (
                <>
                  <RichText html={fields.contractDetails.description.value} />
                  <InputText
                    label={fields.contactForm.firstNameFormField.value.label}
                    hint={<RichText html={fields.contactForm.firstNameFormField.value.hint} />}
                    placeholder={fields.contactForm.firstNameFormField.value.placeholder}
                    error={errors.firstName?.message as string}
                    isDisabled={isCurrentAccountReader}
                    {...register('firstName')}
                  />
                  <InputText
                    label={fields.contactForm.lastNameFormField.value.label}
                    hint={<RichText html={fields.contactForm.lastNameFormField.value.hint} />}
                    placeholder={fields.contactForm.lastNameFormField.value.placeholder}
                    error={errors.lastName?.message as string}
                    isDisabled={isCurrentAccountReader}
                    {...register('lastName')}
                  />

                  <PhoneNumberInputField
                    label={fields.contactForm.phoneNumberFormField.value.label}
                    hint={<RichText html={fields.contactForm.phoneNumberFormField.value.hint} />}
                    placeholder={fields.contactForm.phoneNumberFormField.value.placeholder}
                    isDisabled={isCurrentAccountReader}
                    {...register('phoneNumber')}
                  />

                  <InputEmail
                    label={fields.contactForm.emailFormField.value.label}
                    autoComplete={'off'}
                    error={errors.email?.message as string}
                    isDisabled={isCurrentAccountReader}
                    {...register('email')}
                  />
                </>
              )}
            </Bucket.Content>
            {error && <NotificationBox {...error} />}

            {!isCurrentAccountReader && (
              <Bucket.Footer>
                <Bucket.Actions>
                  <Button type="submit" isLoading={isLoadingApi}>
                    {fields.contractDetails.saveButtonLink.value.text}
                  </Button>
                  <ButtonLink
                    action="secondary"
                    href={fields.contractDetails.cancelButtonLink.value.href + addressSearchParam(addressIdentifier)}>
                    {fields.contractDetails.cancelButtonLink.value.text}
                  </ButtonLink>
                </Bucket.Actions>
              </Bucket.Footer>
            )}
          </Bucket>
        </Stack>
      </Form>
    </FormProvider>
  );
};

export default MoveResidentChangeContactDetails;
