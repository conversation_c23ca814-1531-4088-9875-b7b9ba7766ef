import { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { format, isPast } from 'date-fns';
import { Controller, FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import {
  $SelectedCorrespondenceAddressDto,
  CorrespondenceAddressDto,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondence,
  SelectedCorrespondenceAddressDto,
} from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { MoveBillingAddressStepRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
import {
  Box,
  Bucket,
  Button,
  ButtonLink,
  Form,
  Heading,
  NotificationBox,
  RadioButton,
  RadioGroup,
  Skeleton,
  Stack,
  Text,
} from '@sparky';
import { NotificationBoxProps } from '@sparky/types';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { Input } from 'libs/sparky/src/components/Input/Input';

import useBusLabel from '../../../hooks/useBusLabel';
import { addressSearchParam } from '../../../utils/addSearchParams';
import { useMove } from '../hooks/useMove';
import MoveCustomBillingAddressForm from './components/MoveCustomBillingAddressForm';
import DataErrorNotification from '../../../components/DataErrorNotification';
import { isDateWithin28Days } from '../../../utils/date/is-date-within-28-days';
import { moveStepNameToLink } from '../../../utils/flow/flowStepNameToLink';
import { addressFormatter } from '../overview/address-formatter';

const MoveBillingAddressStep: FC = () => {
  const { move, path, moveCacheKey, isLoading, errorMove } = useMove();
  const { isCurrentAccountReader } = useSelfServiceAccount();
  const { fields } = useContent<MoveBillingAddressStepRenderingExtended>();
  const [error, setError] = useState<NotificationBoxProps | null>(null);
  const [isLoadingPutAddress, setIsLoadingPutAddress] = useState<boolean>(false);
  const addressIdentifier = path?.addressIdentifier;
  const { mutate } = useSWRConfig();
  const { push } = useRouter();
  const busLabel = useBusLabel();

  const isCancelFlow = fields.settings.isCancelFlowFlag.value;
  const oldAddressLabel = addressFormatter(move?.contactDetails?.contactCorrespondenceAddress, busLabel);
  const newAddressLabel = addressFormatter(move?.newAddress?.newAddress, busLabel);
  const options: { label: string; value: SelectedCorrespondenceAddressDto }[] = [
    { label: oldAddressLabel, value: 'Old' },
    { label: fields.addressForm.otherAddressLabel.value, value: 'Custom' },
  ];
  if (move?.newAddress?.newAddress?.street) options.splice(1, 0, { label: newAddressLabel, value: 'New' });

  const conditionalRequired = (validationMessage: string) =>
    yup.string().test('conditional-required', validationMessage, function (value) {
      const { destinationAddress } = this.parent;
      if (destinationAddress !== 'Custom') return true;
      // Otherwise ensure the field is not empty
      return !!value && value.trim().length > 0;
    });

  const FormSchema = yup.object({
    destinationAddress: yup
      .mixed<SelectedCorrespondenceAddressDto>()
      .oneOf($SelectedCorrespondenceAddressDto.enum)
      .required(),
    streetName: conditionalRequired(fields.addressForm.streetFormField?.value?.validationMessage),
    houseNumber: conditionalRequired(fields.addressForm.houseNumberFormField?.value?.validationMessage),
    zipCode: conditionalRequired(fields.addressForm.zipCodeFormField?.value?.validationMessage),
    city: conditionalRequired(fields.addressForm.cityFormField?.value?.validationMessage),
    country: conditionalRequired(fields.addressForm.countryFormField?.value?.validationMessage),
    busNumber: yup.string().nullable(),
    date: yup
      .string()
      .test('required-for-move-file', fields.dataForm.dateFormField.value.requiredMessage, function (value) {
        const { destinationAddress } = this.parent;
        // date field is only required when destination address is set to New
        if (destinationAddress !== 'New') return true;
        return !!value && value.trim().length > 0;
      }),
  });

  type FormValues = yup.InferType<typeof FormSchema>;
  const resolver = yupResolver(FormSchema);

  const form = useForm<FormValues>({
    resolver,
    mode: 'onBlur',
    values: {
      destinationAddress: move?.contactDetails?.selectedCorrespondenceAddress || 'Old',
      country: 'BE',
      date: move?.contactDetails?.correspondenceDate || undefined,
      busNumber: move?.contactDetails?.customCorrespondenceAddress?.bus,
      city: move?.contactDetails?.customCorrespondenceAddress?.municipality || undefined,
      houseNumber: move?.contactDetails?.customCorrespondenceAddress?.houseNumber || undefined,
      streetName: move?.contactDetails?.customCorrespondenceAddress?.street || undefined,
      zipCode: move?.contactDetails?.customCorrespondenceAddress?.postalCode || undefined,
    },
  });

  const { control, watch, setValue, register, formState, trigger } = form;

  const destinationAddress = watch('destinationAddress');

  const submitForm: SubmitHandler<FormValues> = async ({
    city,
    destinationAddress,
    houseNumber,
    streetName,
    zipCode,
    busNumber,
    country,
  }) => {
    try {
      setIsLoadingPutAddress(true);
      let customAddress: CorrespondenceAddressDto | undefined = undefined;
      if (destinationAddress === 'Custom') {
        customAddress = {
          bus: busNumber,
          country,
          houseNumber,
          municipality: city,
          postalCode: zipCode,
          street: streetName,
        };
      }

      const { response } =
        await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondence({
          path,
          body: {
            correspondenceDate: format(new Date(), 'yyyy-MM-dd'),
            selectedAddress: destinationAddress,
            customAddress,
          },
        });
      if (!response.ok) {
        setIsLoadingPutAddress(false);
        return setError({
          isAlert: false,
          text: fields.addressForm.generalErrorNotification.value.content,
          title: fields.addressForm.generalErrorNotification.value.title,
          variant: fields.addressForm.generalErrorNotification.value.variant,
        });
      }
      await mutate(moveCacheKey);
      const url = isCancelFlow
        ? moveStepNameToLink('ConfirmationOverview', fields.navigation.cancelButtonLinkList, 'Cancel')
        : fields.navigation.moveFileSubmitButtonLink?.value.href || '/my-eneco/move/contact-details';
      return push(`${url}${addressSearchParam(addressIdentifier)}`);
    } catch (e) {
      void e;
      setIsLoadingPutAddress(false);
      return setError({
        isAlert: false,
        text: fields.addressForm.generalErrorNotification.value.content,
        title: fields.addressForm.generalErrorNotification.value.title,
        variant: fields.addressForm.generalErrorNotification.value.variant,
      });
    }
  };

  useEffect(() => {
    if (!isCancelFlow && move?.newAddress?.deliveryDate) {
      const date = new Date(move.newAddress.deliveryDate);
      if (isPast(date)) {
        setValue('destinationAddress', 'New');
      }
    }
  }, [move?.newAddress?.deliveryDate, isCancelFlow]);

  useEffect(() => {
    if (formState.touchedFields.date) {
      trigger('date');
    }
  }, [destinationAddress]);

  const cancelButton = (): string => {
    const hasAnalogMeter = move?.oldAddress?.meters?.some(meter => meter.isDigital !== true) ?? true;
    const withIn28Days = isDateWithin28Days(move?.oldAddress?.keyTransfer ?? new Date().toISOString());

    if (hasAnalogMeter && withIn28Days)
      return moveStepNameToLink('OldAddressMeters', fields.navigation.cancelButtonLinkList, 'Cancel');
    return moveStepNameToLink('OldAddressKeyTransfer', fields.navigation.cancelButtonLinkList, 'Cancel');
  };

  if (!isLoading && errorMove !== undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <FormProvider {...form}>
      <Form onSubmit={form.handleSubmit(submitForm)}>
        <Box paddingTop={isCancelFlow ? 12 : 0}>
          <Stack gap="10">
            {fields.addressForm.title.value && (
              <Stack gap="4">
                <Heading as="h1" size="M">
                  {fields.addressForm.title.value}
                </Heading>
              </Stack>
            )}
            <Bucket
              title={
                isCancelFlow && destinationAddress === 'Old'
                  ? fields.addressForm.defaultAddressTitle.value
                  : fields.addressForm.customAddressTitle.value
              }>
              <Bucket.Content>
                <>
                  {isLoading ? (
                    <Box>
                      <Skeleton height={50} width="100%" />
                      <Skeleton height={50} width="100%" />
                    </Box>
                  ) : (
                    <Controller
                      control={control}
                      name="destinationAddress"
                      render={({ field: { onChange, value } }) => (
                        <RadioGroup
                          name="destinationAddress"
                          aria-labelledby="receiveAdvanceInvoices"
                          direction="column"
                          value={value}
                          onValueChange={onChange}>
                          {options.map(option => (
                            <RadioButton value={option.value} key={option.value}>
                              {option.label}
                            </RadioButton>
                          ))}
                        </RadioGroup>
                      )}
                    />
                  )}
                  {destinationAddress === 'Custom' && <MoveCustomBillingAddressForm />}
                  {!isCancelFlow && destinationAddress !== 'Old' && (
                    <>
                      <Text weight="bold">{fields.dataForm.title.value}</Text>
                      <Input
                        type="date"
                        {...fields.dataForm.dateFormField.value}
                        {...register('date')}
                        error={formState.errors.date?.message}
                      />
                    </>
                  )}
                </>
              </Bucket.Content>
              {!isCurrentAccountReader && !isCancelFlow && (
                <Bucket.Footer>
                  <Bucket.Actions>
                    <Button type="submit" isLoading={isLoadingPutAddress}>
                      {fields.navigation.moveFileSubmitButtonLink?.value.text || 'Opslaan'}
                    </Button>
                    <ButtonLink
                      action="secondary"
                      href={
                        (fields.navigation.moveFileCancelButtonLink?.value.href || '/my-eneco/move/contact-details') +
                        addressSearchParam(addressIdentifier)
                      }>
                      {fields.navigation.moveFileCancelButtonLink?.value.text || 'Annuleren'}
                    </ButtonLink>
                  </Bucket.Actions>
                </Bucket.Footer>
              )}
            </Bucket>
            {error && <NotificationBox {...error} />}
            {isCancelFlow && (
              <Stack gap="6" direction={'row'} alignX={'start'}>
                <Button type="submit" isLoading={isLoadingPutAddress}>
                  {fields.navigation.nextButtonText.value}
                </Button>
                <ButtonLink action="secondary" href={cancelButton() + addressSearchParam(addressIdentifier)}>
                  {fields.navigation.previousButtonText.value}
                </ButtonLink>
              </Stack>
            )}
          </Stack>
        </Box>
      </Form>
    </FormProvider>
  );
};

export default MoveBillingAddressStep;
