import RichText from '@components/RichText/RichText';
import { getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetail } from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { MoveNewAddressStepRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
import { Bleed, Box, Heading, Skeleton, Stack } from '@sparky';

import MoveNewAddressForm from './components/MoveNewAddressForm';
import DataErrorNotification from '../../../components/DataErrorNotification';
import { addressSearchParam } from '../../../utils/addSearchParams';
import { moveStepNameToLink } from '../../../utils/flow/flowStepNameToLink';
import { useMove } from '../hooks/useMove';

const MoveNewAddress = () => {
  const { fields } = useContent<MoveNewAddressStepRenderingExtended>();
  const { session, moveCacheKey, path: movePath, isLoading: isLoadingMove, errorMove, move } = useMove();
  const { addressIdentifier } = movePath;
  const { push } = useRouter();
  const { isCurrentAccountReader, isProspect } = useSelfServiceAccount();

  const {
    data: moveDataWrapped,
    isLoading,
    error,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetail,
    {
      path: movePath,
    },
    [moveCacheKey],
    session,
  );

  const moveData = unwrapData(moveDataWrapped);
  const hasUploadedEodForNewAddress = moveData?.newAddress?.eod;

  if (isProspect || isCurrentAccountReader || hasUploadedEodForNewAddress) {
    push(
      moveStepNameToLink('Done', fields.navigation.continueButtonLinkList, 'Continue') +
        addressSearchParam(addressIdentifier),
    );
  }

  if (!addressIdentifier) {
    push(moveStepNameToLink('Done', fields.navigation.continueButtonLinkList, 'Continue'));
  }

  if (
    !isLoading &&
    !isLoadingMove &&
    ((errorMove !== undefined && move === undefined) || (error !== undefined && moveDataWrapped === undefined))
  ) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Box paddingTop="10">
      <Stack gap="10">
        <Stack gap="4">
          <Heading as="h1" size="M">
            {fields.addressForm.title.value}
          </Heading>
          <RichText html={fields.addressForm.description.value} />
        </Stack>
        {isLoading ? (
          <Stack gap="6">
            <Bleed top="2">
              <Stack gap="4">
                <Box padding={6}>
                  <Stack gap={4}>
                    <Skeleton width="100%" height={50} />
                    <Skeleton width="100%" height={50} />
                    <Stack gap={4} direction="row">
                      <Skeleton width="50%" height={50} />
                      <Skeleton width="50%" height={50} />
                    </Stack>
                  </Stack>
                </Box>
              </Stack>
            </Bleed>
          </Stack>
        ) : (
          <MoveNewAddressForm {...moveData} />
        )}
      </Stack>
    </Box>
  );
};

export default MoveNewAddress;
