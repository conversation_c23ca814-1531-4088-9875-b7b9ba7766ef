import { FC, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import {
  $MoveReasonDto,
  deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMove,
  MoveReasonDto,
  postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasons,
} from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { SelectMoveReasonRendering } from '@sitecore/types/SelectMoveReason';
import {
  AlertDialog,
  Box,
  Button,
  Form,
  Heading,
  NotificationBox,
  RadioGroup,
  RadioTile,
  Stack,
  Stretch,
  Text,
  VisuallyHidden,
} from '@sparky';
import { Chat, ComfortCat, Moving, OnePlanet, Suitcase, TwoPeople } from '@sparky/illustrations';
import { NotificationBoxProps } from '@sparky/types';

import DataErrorNotification from '../../../components/DataErrorNotification';
import { addressSearchParam } from '../../../utils/addSearchParams';
import { moveStepNameToLink } from '../../../utils/flow/flowStepNameToLink';
import { useMove } from '../hooks/useMove';

const MoveReasonSchema = yup.string().oneOf($MoveReasonDto.enum);

const moveOptionMap = new Map<
  MoveReasonDto,
  { icon: JSX.Element; showModal?: true; moveFlow: 'continueFlow' | 'cancelFlow' }
>([
  ['MoveAndKeepEneco', { icon: <Moving size="small" key="1" />, moveFlow: 'continueFlow' }],
  ['MoveInWithSomeoneElse', { icon: <TwoPeople size="small" key="2" />, showModal: true, moveFlow: 'cancelFlow' }],
  ['MoveIntoResidentialCareCenter', { icon: <Suitcase size="small" key="3" />, moveFlow: 'cancelFlow' }],
  ['MoveToOtherCountry', { icon: <OnePlanet size="small" key="4" />, moveFlow: 'cancelFlow' }],
  ['MoveAndLeaveEneco', { icon: <ComfortCat size="small" key="5" />, showModal: true, moveFlow: 'cancelFlow' }],
  ['Other', { icon: <Chat size="small" key="6" />, moveFlow: 'cancelFlow' }],
]);

const MoveSelectReason: FC = () => {
  const { fields } = useContent<SelectMoveReasonRendering>();
  const [error, setError] = useState<NotificationBoxProps>();
  const { push } = useRouter();
  const { mutate } = useSWRConfig();
  const [showModal, setShowModal] = useState(false);
  const { move, path, moveCacheKey, errorMove, isLoading } = useMove();
  const { isProspect } = useSelfServiceAccount();
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  const FormSchema = yup.object({
    moveReason: MoveReasonSchema.required(fields.content.requiredText.value),
  });
  type FormValues = yup.InferType<typeof FormSchema>;
  const resolver = yupResolver(FormSchema);

  const form = useForm<FormValues>({
    resolver,
    values: {
      moveReason: move?.reason || 'MoveAndKeepEneco',
    },
  });

  const {
    handleSubmit,
    control,
    trigger,
    formState: { errors },
    getValues,
  } = form;

  const cancelCurrentMove = async () => {
    await deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMove({
      path,
    });
    await mutate(moveCacheKey);
    const url = moveStepNameToLink('Done', fields.content.continueButtonLinkList, 'Continue');
    return push(url + addressSearchParam(path.addressIdentifier));
  };

  const isProd = process.env.NODE_ENV === 'production';
  if (isProd && !path.addressIdentifier) push(fields.content.cancelButtonLink.value.href);

  const handleSubmitClick = async () => {
    await trigger(['moveReason']);
    if (Object.keys(errors).length) return;
    const moveReason = getValues('moveReason');
    if (moveOptionMap.get(moveReason)?.showModal) {
      setShowModal(true);
      return;
    }

    submitForm({ moveReason });
  };

  const submitForm: SubmitHandler<FormValues> = async ({ moveReason }) => {
    try {
      setIsLoadingSubmit(true);
      if (move) {
        await deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMove({
          path,
        });
        await mutate(moveCacheKey);
      }
      const { response } =
        await postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasons({
          path,
          body: {
            reasonDto: moveReason,
          },
        });
      if (response.ok) {
        mutate(moveCacheKey);
        const moveReason = getValues('moveReason');
        const nextPage =
          moveOptionMap.get(moveReason)?.moveFlow === 'continueFlow'
            ? moveStepNameToLink('Eod', fields.content.continueButtonLinkList, 'Continue')
            : moveStepNameToLink('Eod', fields.content.cancelButtonLinkList, 'Cancel');

        push(nextPage + addressSearchParam(path.addressIdentifier));
      } else {
        setIsLoadingSubmit(false);
        setError({
          isAlert: true,
          title: fields.content?.errorNotification.value.title,
          text: <RichText html={fields.content?.errorNotification.value.content} />,
          variant: 'error',
        });
      }
    } catch (e) {
      void e;
      setIsLoadingSubmit(false);
      setError({
        isAlert: true,
        title: fields.content?.errorNotification.value.title,
        text: <RichText html={fields.content?.errorNotification.value.content} />,
        variant: 'error',
      });
    }
  };

  if (isProspect) {
    return null;
  }

  if (!isLoading && errorMove != undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <FormProvider {...form}>
      <Box paddingTop="12">
        <Stack gap="6">
          <Stack gap="4">
            <Heading as="h1" size="M">
              {fields.content?.title.value}
            </Heading>
            <RichText html={fields.content?.description.value} />
          </Stack>
          <Form onSubmit={e => e.preventDefault()}>
            <Stack gap="10">
              <VisuallyHidden>
                <label id="moveReason">{fields.content?.title.value}</label>
              </VisuallyHidden>
              <Controller
                control={control}
                name="moveReason"
                render={({ field: { onChange, value }, fieldState }) => (
                  <RadioGroup
                    name="moveReason"
                    aria-labelledby="moveReason"
                    onValueChange={onChange}
                    value={value}
                    error={fieldState.error?.message}>
                    <Stretch>
                      <Stack gap="3">
                        {fields.content?.moveOptionsList.value.enum.map(moveOption => (
                          <MoveOptionRadioTile key={moveOption.name} moveOption={moveOption} />
                        ))}
                      </Stack>
                    </Stretch>
                  </RadioGroup>
                )}></Controller>
              {error && <NotificationBox {...error} />}
              <Stack gap={2}>
                <Text size="BodyS">
                  <RichText html={fields.content?.actionDescription.value} />
                </Text>
                <Stack gap={4} direction={'row'}>
                  <Button type="submit" onClick={handleSubmitClick} isLoading={isLoadingSubmit}>
                    {fields.content?.nextButtonLink.value.text}
                  </Button>
                  <Button onClick={cancelCurrentMove} action={'secondary'}>
                    {fields.content?.cancelButtonLink.value.text}
                  </Button>
                </Stack>
              </Stack>
            </Stack>
          </Form>
        </Stack>
      </Box>
      <AlertDialog
        title={fields.content?.dialog.value.title}
        description={<RichText html={fields.content?.dialog.value.content} />}
        isOpen={showModal}
        setOpen={setShowModal}
        confirmText={fields.content?.dialog.value.submitButtonText || ''}
        onConfirm={() => {}}
        onDeny={handleSubmit(submitForm)}
        denyText={fields.content?.dialog.value.cancelButtonText || ''}
      />
    </FormProvider>
  );
};

export default MoveSelectReason;

type MoveOptionRadioTileProps = {
  moveOption: { name: string; label: string };
};

const MoveOptionRadioTile = ({ moveOption }: MoveOptionRadioTileProps) => {
  if (!MoveReasonSchema.isValidSync(moveOption.name)) return <div>Wrong Sitecore name: {moveOption.name}</div>;

  return (
    <RadioTile value={moveOption.name} icon={moveOptionMap.get(moveOption.name)?.icon}>
      <Text size="BodyS">{moveOption.label}</Text>
    </RadioTile>
  );
};
