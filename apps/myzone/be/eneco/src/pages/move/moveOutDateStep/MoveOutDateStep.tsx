import { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { format } from 'date-fns';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransfer } from '@dc-be/client';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { MovingOutDateStepRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
import { AlertDialog, Box, Bucket, Button, ButtonLink, Checkbox, Form, Heading, NotificationBox, Stack } from '@sparky';
import { NotificationBoxProps } from '@sparky/types';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { Input } from 'libs/sparky/src/components/Input/Input';

import DataErrorNotification from '../../../components/DataErrorNotification';
import ReadFormField from '../../../components/ReadFormField';
import useBusLabel from '../../../hooks/useBusLabel';
import { addressSearchParam } from '../../../utils/addSearchParams';
import { isDateWithin28Days } from '../../../utils/date/is-date-within-28-days';
import {
  isDateNotTooFarInFuture,
  isDateNotTooFarInPast,
  isDateTodayOrPast,
} from '../../../utils/date/move-date-validation';
import { moveStepNameToLink } from '../../../utils/flow/flowStepNameToLink';
import { EnergyTypeList } from '../components/EnergyTypeList';
import { useMove } from '../hooks/useMove';
import { addressFormatter } from '../overview/address-formatter';

const MoveoutDateStep: FC = () => {
  const { move, path, moveCacheKey, isLoading, errorMove } = useMove();
  const {
    fields: { moveOutDateForm, credentialsCard, navigation },
  } = useContent<MovingOutDateStepRenderingExtended>();
  const { push } = useRouter();
  const { mutate } = useSWRConfig();
  const busLabel = useBusLabel();
  const [showTooFarInFutureDialog, setShowTooFarInFutureDialog] = useState(false);
  const [showTooFarInPastDialog, setShowTooFarInPastDialog] = useState(false);
  const [error, setError] = useState<NotificationBoxProps | null>(null);
  const [showTooFarInFutureForAnalogueCancelFlowDialog, setshowTooFarInFutureForAnalogueCancelFlowDialog] =
    useState(false);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  const isContinueFlow = move?.flow === 'ContinueFlow';
  const isProd = process.env.NODE_ENV === 'production';
  const hasAnalogMeter = move?.oldAddress?.meters?.some(meter => meter.isDigital !== true) ?? true;
  const hasOnlyDigitalMeters = move?.oldAddress?.meters?.every(meter => meter.isDigital);
  const isCancelFlowWithAnalogueMeter = !isContinueFlow && hasAnalogMeter;
  if (isProd && !path.addressIdentifier)
    push(moveStepNameToLink('Done', navigation.continueButtonLinkList, 'Continue'));

  const FormSchema = yup.object({
    keyTransferDateUnknown: yup.boolean(),
    keyTransferDate: yup
      .string()
      .test('is-required', moveOutDateForm.dateFormField.value.requiredMessage, function (value) {
        const keyTransferDateUnknown = this.parent.keyTransferDateUnknown as boolean;
        if (keyTransferDateUnknown) return true;
        if (typeof value !== 'string') return false;
        const date = new Date(value);
        return !isNaN(date.getTime());
      })
      .test('is-date-too-far-in-past', moveOutDateForm.datePast28DaysDialog.value.triggerText, (value, context) =>
        isDateNotTooFarInPast(value, context),
      )
      .test(
        'is-date-too-far-in-future',
        isCancelFlowWithAnalogueMeter
          ? moveOutDateForm.dateNotInFutureDialog.value.triggerText
          : moveOutDateForm.dateFuture1YearDialog.value.triggerText,
        (value, context) => {
          if (isContinueFlow) {
            return isDateNotTooFarInFuture(value, context);
          }
          return isCancelFlowWithAnalogueMeter ? isDateTodayOrPast(value) : isDateNotTooFarInFuture(value, context);
        },
      ),
  });

  type FormValues = yup.InferType<typeof FormSchema>;
  const resolver = yupResolver(FormSchema);

  const buttonLinkList = isContinueFlow ? navigation.continueButtonLinkList : navigation.cancelButtonLinkList;
  let backButtonLink;
  if (isContinueFlow) {
    backButtonLink = move?.newAddress?.eod
      ? moveStepNameToLink('Eod', buttonLinkList, 'Continue')
      : moveStepNameToLink('NewAddressKeyTransfer', buttonLinkList, 'Continue');
  } else {
    backButtonLink = moveStepNameToLink('Eod', buttonLinkList, 'Cancel');
  }

  const form = useForm<FormValues>({
    resolver,
    mode: 'onChange',
    values: {
      keyTransferDate: move?.oldAddress?.keyTransfer ? format(move?.oldAddress?.keyTransfer, 'yyyy-MM-dd') : '',
    },
  });

  const {
    register,
    formState: { errors },
    watch,
    setValue,
  } = form;

  const keyTransferDateUnknown = isContinueFlow ? watch('keyTransferDateUnknown') : false;
  const keyTransferDate = watch('keyTransferDate');

  useEffect(() => {
    if (keyTransferDateUnknown) setValue('keyTransferDate', undefined);
  }, [keyTransferDateUnknown, setValue]);

  const checkToShowDialog = () => {
    if (keyTransferDate) {
      if (isCancelFlowWithAnalogueMeter) {
        setshowTooFarInFutureForAnalogueCancelFlowDialog(!isDateTodayOrPast(keyTransferDate));
      } else {
        setShowTooFarInFutureDialog(!isDateNotTooFarInFuture(keyTransferDate));
      }
      setShowTooFarInPastDialog(!isDateNotTooFarInPast(keyTransferDate));
    }
  };

  const submitForm: SubmitHandler<FormValues> = async ({ keyTransferDateUnknown, keyTransferDate }) => {
    try {
      setIsLoadingSubmit(true);
      const { response } =
        await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransfer(
          {
            path: {
              ...path,
              locationType: 'Old',
            },
            body: {
              keyTransferDate: keyTransferDate === '' ? undefined : keyTransferDate,
              keyTransferDateUnknown,
            },
          },
        );
      if (!response.ok) {
        setIsLoadingSubmit(false);
        return setError({
          isAlert: false,
          text: 'error',
          title: '',
          variant: 'error',
        });
      }
      await mutate(moveCacheKey);
      const isContinueFlow = move?.flow === 'ContinueFlow';
      let url = '';
      if (isContinueFlow) url = moveStepNameToLink('ConfirmationOverview', buttonLinkList, 'Continue');
      else if (hasAnalogMeter && isDateWithin28Days(keyTransferDate ?? new Date().toISOString())) {
        url = moveStepNameToLink('OldAddressMeters', buttonLinkList, 'Cancel');
      } else {
        url = moveStepNameToLink('CorrespondenceAddress', buttonLinkList, 'Cancel');
      }
      push(url + addressSearchParam(path.addressIdentifier));
    } catch (e) {
      void e;
      setIsLoadingSubmit(false);
      return setError({
        isAlert: false,
        text: 'error',
        title: '',
        variant: 'error',
      });
    }
  };

  if (!isLoading && errorMove !== undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <>
      <Form onSubmit={form.handleSubmit(submitForm)}>
        <Box paddingTop="10">
          <Stack gap="10">
            <Stack gap="6" alignX={'start'}>
              <Heading as="h1" size="M">
                {moveOutDateForm.title.value}
              </Heading>
              <RichText html={moveOutDateForm.description.value}></RichText>
              <Bucket title={credentialsCard.title.value}>
                <Bucket.Content>
                  <Stack gap="4">
                    <ReadFormField
                      label={credentialsCard.newAddressLabel.value}
                      value={addressFormatter(move?.oldAddress?.oldAddress, busLabel)}
                    />
                    <ReadFormField
                      label={credentialsCard.energyTypeLabel.value}
                      value={
                        <EnergyTypeList
                          meters={move?.oldAddress?.meters}
                          sitecoreEnum={credentialsCard.energyTypeList.value.enum}
                        />
                      }
                    />
                  </Stack>
                </Bucket.Content>
              </Bucket>
              <Input
                label={moveOutDateForm.dateFormField.value.label}
                id="keyExchangeDate"
                type="date"
                error={errors.keyTransferDate?.message}
                disabled={keyTransferDateUnknown && isContinueFlow}
                {...register('keyTransferDate')}
                autoComplete="off"
              />
              {isContinueFlow && (
                <Checkbox
                  label={moveOutDateForm.dateUnknownFormField.value.label}
                  hint={moveOutDateForm.dateUnknownFormField.value.hint}
                  {...register('keyTransferDateUnknown')}
                />
              )}
              {keyTransferDateUnknown && (
                <NotificationBox
                  title={moveOutDateForm.dateUnknownNotification.value.title}
                  variant={moveOutDateForm.dateUnknownNotification.value.variant}
                  text={<RichText html={moveOutDateForm.dateUnknownNotification.value.content} />}
                  isAlert={false}
                />
              )}
              {!isContinueFlow && hasOnlyDigitalMeters && (
                <NotificationBox
                  title={moveOutDateForm.skipMeterReadingNotification.value.title}
                  variant={moveOutDateForm.skipMeterReadingNotification.value.variant}
                  text={<RichText html={moveOutDateForm.skipMeterReadingNotification.value.content} />}
                  isAlert={false}
                />
              )}
              {error && <NotificationBox {...error} />}
            </Stack>
            <Stack.Item shrink>
              <Stack gap="2" direction={'row'}>
                <Button type="submit" onClick={() => checkToShowDialog()} isLoading={isLoadingSubmit}>
                  {navigation.nextButtonText.value}
                </Button>
                <ButtonLink action="secondary" href={backButtonLink + addressSearchParam(path.addressIdentifier)}>
                  {navigation.previousButtonText.value}
                </ButtonLink>
              </Stack>
            </Stack.Item>
          </Stack>
        </Box>
      </Form>
      <AlertDialog
        title={moveOutDateForm.dateFuture1YearDialog.value.title}
        isOpen={showTooFarInFutureDialog}
        setOpen={setShowTooFarInFutureDialog}
        onConfirm={() => {}}
        confirmText={moveOutDateForm.dateFuture1YearDialog.value.submitButtonText || ''}
        denyText={moveOutDateForm.dateFuture1YearDialog.value.cancelButtonText || ''}
        onDeny={() => {}}>
        <RichText html={moveOutDateForm.dateFuture1YearDialog.value.content} />
      </AlertDialog>
      <AlertDialog
        title={moveOutDateForm.datePast28DaysDialog.value.title}
        isOpen={showTooFarInPastDialog}
        setOpen={setShowTooFarInPastDialog}
        onConfirm={() => {}}
        confirmText={moveOutDateForm.dateFuture1YearDialog.value.submitButtonText || ''}
        denyText={moveOutDateForm.datePast28DaysDialog.value.cancelButtonText || ''}
        onDeny={() => {}}>
        <RichText html={moveOutDateForm.datePast28DaysDialog.value.content} />
      </AlertDialog>
      <AlertDialog
        title={moveOutDateForm.dateNotInFutureDialog.value.title}
        isOpen={showTooFarInFutureForAnalogueCancelFlowDialog}
        setOpen={setshowTooFarInFutureForAnalogueCancelFlowDialog}
        onConfirm={() => {}}
        confirmText={moveOutDateForm.dateNotInFutureDialog.value.submitButtonText || ''}
        denyText={moveOutDateForm.dateNotInFutureDialog.value.cancelButtonText || ''}
        onDeny={() => {}}>
        <RichText html={moveOutDateForm.dateNotInFutureDialog.value.content} />
      </AlertDialog>
    </>
  );
};

export default MoveoutDateStep;
