import { FC } from 'react';

import { isBefore, subDays } from 'date-fns';

import RichText from '@components/RichText/RichText';
import { MoveEnergyTypeDto, MoveViewDetailAddressMeterResponse, TimeframeTypeCodeDto } from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { MoveFileAddressDetailsRendering } from '@sitecore/types/MoveFileAddressDetails';
import { Bleed, Bucket, NotificationBox, Stack, Text, TextLink } from '@sparky';
import { ElectricityIcon, GasIcon } from '@sparky/icons';

import DataErrorNotification from '../../../components/DataErrorNotification';
import InfoIconButton from '../../../components/InfoIconButton';
import { LoadingListSpinner } from '../../../components/Loading/LoadingListSpinner';
import ReadFormField from '../../../components/ReadFormField';
import useBusLabel from '../../../hooks/useBusLabel';
import { addressSearchParam } from '../../../utils/addSearchParams';
import { useMove } from '../hooks/useMove';
import IlcDateDifferenceNotification from '../notification/IlcDateDifference';
import { addressFormatter } from '../overview/address-formatter';
import { MoveFileEodUploadForm } from './components/MoveFileEodUploadForm';
import { toBase64URL } from '../../../utils/base64';
import { NEW_METER_DATA_URL_PARAM } from '../editMeterDetails/utils/moveEditMeterDetailsUtils';

const MoveFileAddressDetails: FC = () => {
  const { fields } = useContent<MoveFileAddressDetailsRendering>();
  const { move, path, isLoading, errorMove } = useMove();
  const { isCurrentAccountReader } = useSelfServiceAccount();
  const busLabel = useBusLabel();
  const isNewAddressDetails = fields.settings.isNewAddressFlag.value;
  const isLocked = move?.isLocked || move?.oldAddress?.isLocked || move?.newAddress?.isLocked;
  const addressDetails = isNewAddressDetails ? move?.newAddress : move?.oldAddress;

  const suggestedEans = move?.newAddress?.suggestedEans;

  const meterDataParams = new Map(
    addressDetails?.meters?.map(meter => [
      meter.ean!,
      toBase64URL(JSON.stringify({ ean: meter.ean!, meterNumber: meter.meterNumber! })),
    ]) ?? [],
  );

  const metersWithConditions = addressDetails?.meters?.map(meter => {
    const hasEod = isNewAddressDetails ? !!move?.newAddress?.eod : !!move?.oldAddress?.eod;
    const moveOutDate = move?.oldAddress?.keyTransfer;
    const moveOutDateIsMoreThan5DaysInThePast = moveOutDate && isBefore(new Date(moveOutDate), subDays(new Date(), 5));
    return {
      ...meter,
      isEditable:
        ((hasEod && !!meter.registers?.some(r => r.meterReading === null)) || !hasEod) &&
        !moveOutDateIsMoreThan5DaysInThePast &&
        !isLocked,
    };
  });

  const suggestedEnergyTypes = [...new Set(suggestedEans?.map(ean => ean.energyType))].filter(energyType =>
    metersWithConditions?.some(meter => meter.energyType === energyType),
  );

  if (!isLoading && errorMove !== undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Stack gap="4">
      {isNewAddressDetails && (
        <IlcDateDifferenceNotification
          initiatingLeavingCustomerDate={move?.newAddress?.initiatingLeavingCustomerDate}
          deliveryDate={move?.newAddress?.deliveryDate}
          notificationLessThan30={fields.notifications.moveInIlcDateDifference30DaysOrLessNotification}
          notificationMoreThan30={fields.notifications.moveInIlcDateDifferenceExceeding30DaysNotification}
        />
      )}

      {isNewAddressDetails && move?.newAddress?.meters && move.newAddress.meters.some(m => m.isSealed) && (
        <NotificationBox
          isAlert={false}
          text={<RichText html={fields.notifications.sealedMetersNotification.value.content} />}
          variant={fields.notifications.sealedMetersNotification.value.variant}
          title={fields.notifications.sealedMetersNotification.value.title}
        />
      )}

      <Bucket
        title={
          <Stack direction={'row'} alignY={'center'}>
            <Text weight={'bold'}>{fields.eodUpload.title.value}</Text>
            <Bleed vertical={4}>
              <InfoIconButton triggerText={fields.eodUpload.infoNotification.value.title}>
                <RichText html={fields.eodUpload.infoNotification.value.content} />
              </InfoIconButton>
            </Bleed>
          </Stack>
        }>
        <Bucket.Content>
          <MoveFileEodUploadForm />
        </Bucket.Content>
      </Bucket>
      <Bucket title={fields.address.title.value}>
        <Bucket.Content>
          <ReadFormField
            label={fields.address.addressLabel.value}
            value={addressFormatter(
              isNewAddressDetails ? move?.newAddress?.newAddress : move?.oldAddress?.oldAddress,
              busLabel,
            )}
            isLoading={isLoading}
          />
          {isNewAddressDetails ? (
            <>
              <ReadFormField
                label={fields.address.deliveryEndDateLabel.value}
                value={move?.newAddress?.deliveryDate}
                isLoading={isLoading}
              />
              <ReadFormField
                label={fields.address.vacancyLabel.value}
                value={move?.newAddress?.vacancy}
                isLoading={isLoading}
              />
            </>
          ) : (
            <ReadFormField
              label={fields.address.keyTransferDateLabel.value}
              value={move?.oldAddress?.keyTransfer}
              isLoading={isLoading}
            />
          )}
          {!isCurrentAccountReader && !isLocked && (
            <TextLink
              emphasis="high"
              href={fields.address.changeAddressLink.value.href + addressSearchParam(path.addressIdentifier)}>
              {fields.address.changeAddressLink.value.text}
            </TextLink>
          )}
        </Bucket.Content>
      </Bucket>

      {isNewAddressDetails &&
        suggestedEnergyTypes.map(energyType => {
          return (
            <Meter
              key={energyType}
              fields={energyType === 'Electricity' ? fields.electricityMeter : fields.gasMeter}
              addressIdentifier={path.addressIdentifier}
              isEditable={true}
              meterDataUrlParam={NEW_METER_DATA_URL_PARAM}
              type={energyType || 'Electricity'}
            />
          );
        })}

      {isLoading ? (
        <LoadingListSpinner />
      ) : (
        metersWithConditions
          ?.sort((a, b) => (a.energyType ?? '').localeCompare(b.energyType ?? ''))
          .map(meter => {
            if (meter.energyType === 'Electricity')
              return (
                <Meter
                  key={meter.ean}
                  meter={meter}
                  fields={fields.electricityMeter}
                  addressIdentifier={path.addressIdentifier}
                  meterDataUrlParam={meterDataParams.get(meter.ean!) || ''}
                  isEditable={meter.isEditable}
                  type="Electricity"
                />
              );
            return (
              <Meter
                key={meter.ean}
                meter={meter}
                fields={fields.gasMeter}
                addressIdentifier={path.addressIdentifier}
                meterDataUrlParam={meterDataParams.get(meter.ean!) || ''}
                isEditable={meter.isEditable}
                type="Gas"
              />
            );
          })
      )}

      <Bucket title={fields.contactDetails.title.value}>
        <Bucket.Content>
          {isNewAddressDetails ? (
            <>
              <ReadFormField
                label={fields.contactDetails.nameLabel.value}
                value={
                  (move?.newAddress?.contact?.representation ? move.newAddress.contact.representation + ' ' : '') +
                  (move?.newAddress?.contact?.firstName ? move.newAddress.contact.firstName + ' ' : '') +
                  (move?.newAddress?.contact?.lastName || '')
                }
                isLoading={isLoading}
              />
              <ReadFormField
                label={fields.contactDetails.phoneNumberLabel.value}
                value={move?.newAddress?.contact?.phoneNumber}
                isLoading={isLoading}
              />
              <ReadFormField
                label={fields.contactDetails.emailAddressLabel.value}
                value={move?.newAddress?.contact?.email}
                isLoading={isLoading}
              />
            </>
          ) : (
            <>
              <ReadFormField
                label={fields.contactDetails.nameLabel.value}
                value={
                  (move?.oldAddress?.contact?.representation ? move.oldAddress.contact.representation + ' ' : '') +
                  (move?.oldAddress?.contact?.firstName ? move.oldAddress.contact.firstName + ' ' : '') +
                  (move?.oldAddress?.contact?.lastName || '')
                }
                isLoading={isLoading}
              />
              <ReadFormField
                label={fields.contactDetails.phoneNumberLabel.value}
                value={move?.oldAddress?.contact?.phoneNumber}
                isLoading={isLoading}
              />
              <ReadFormField
                label={fields.contactDetails.emailAddressLabel.value}
                value={move?.oldAddress?.contact?.email}
                isLoading={isLoading}
              />
            </>
          )}
          {!isCurrentAccountReader && !isLocked && (
            <TextLink
              emphasis="high"
              href={fields.contactDetails.changeContactLink.value.href + addressSearchParam(path.addressIdentifier)}>
              {fields.contactDetails.changeContactLink.value.text}
            </TextLink>
          )}
        </Bucket.Content>
      </Bucket>
    </Stack>
  );
};

export default MoveFileAddressDetails;

interface MeterProps {
  meter?: MoveViewDetailAddressMeterResponse;
  fields:
    | MoveFileAddressDetailsRendering['fields']['electricityMeter']
    | MoveFileAddressDetailsRendering['fields']['gasMeter'];
  addressIdentifier: string;
  meterDataUrlParam: string;
  isEditable: boolean;
  type: MoveEnergyTypeDto;
}

export const Meter: FC<MeterProps> = ({ meter, fields, addressIdentifier, meterDataUrlParam, isEditable, type }) => {
  const isElectricity = type === 'Electricity';
  const meterReading = meter?.registers?.find(register => register.timeframeTypeCode === 'TH')?.meterReading;

  const { isCurrentAccountReader } = useSelfServiceAccount();
  const baseUrl = fields.changeMeterLink.value.href;
  const addressParam = addressSearchParam(addressIdentifier);
  const meterParam = `&meter=${meterDataUrlParam}`;
  const typeParam = meterDataUrlParam === NEW_METER_DATA_URL_PARAM ? `&type=${type}` : '';

  const href = `${baseUrl}${addressParam}${meterParam}${typeParam}`;

  return (
    <Bucket
      title={
        <Stack gap={2} direction="row">
          {isElectricity ? <ElectricityIcon color="iconElectricity" /> : <GasIcon color="iconGas" />}
          <Text weight="bold">{fields.title.value}</Text>
        </Stack>
      }>
      <Bucket.Content>
        <ReadFormField label={fields.eanLabel.value} value={meter?.ean || '-'} />
        <ReadFormField label={fields.numberLabel.value} value={meter?.meterNumber || '-'} />
        {meter?.isDigital ? (
          <RichText html={fields.description.value} />
        ) : (
          <>
            {isElectricity ? (
              <ElectricityMeterRegisters
                electricityMeter={meter}
                fields={fields as MoveFileAddressDetailsRendering['fields']['electricityMeter']}
              />
            ) : (
              <ReadFormField
                label={fields.defaultRateLabel.value}
                value={meterReading === 0 || meterReading ? meterReading + ' m³' : '-'}
              />
            )}
            {isEditable && !isCurrentAccountReader && (
              <TextLink emphasis="high" href={href}>
                {fields.changeMeterLink.value.text}
              </TextLink>
            )}
          </>
        )}
      </Bucket.Content>
    </Bucket>
  );
};

const ElectricityMeterRegisters: FC<{
  electricityMeter: MoveViewDetailAddressMeterResponse | undefined;
  fields: MoveFileAddressDetailsRendering['fields']['electricityMeter'];
}> = ({ electricityMeter, fields }) => {
  const getMeterReadingByTimeframeTypeCode = (timeframeTypeCode: TimeframeTypeCodeDto) => {
    const register = electricityMeter?.registers?.find(register => register.timeframeTypeCode === timeframeTypeCode);
    if (register?.meterReading === null) {
      return '-';
    }
    if (register !== undefined) {
      return `${register.meterReading} kWh`;
    }
    return undefined;
  };

  const meterReadings = [
    {
      label: fields.defaultRateLabel.value,
      value: getMeterReadingByTimeframeTypeCode('TH'),
    },
    {
      label: fields.dailyRateLabel.value,
      value: getMeterReadingByTimeframeTypeCode('HI'),
    },
    {
      label: fields.nightlyRateLabel.value,
      value: getMeterReadingByTimeframeTypeCode('LO'),
    },
    {
      label: fields.excludingNightlyRateLabel.value,
      value: getMeterReadingByTimeframeTypeCode('EX'),
    },
  ];

  const hasMeterReading = meterReadings.some(reading => reading.value !== undefined);

  return hasMeterReading ? (
    meterReadings.map(reading => {
      if (reading.value === undefined) {
        return null;
      }
      return <ReadFormField key={reading.label} label={reading.label} value={reading.value} />;
    })
  ) : (
    <ReadFormField label={fields.defaultRateLabel.value} value="-" />
  );
};
