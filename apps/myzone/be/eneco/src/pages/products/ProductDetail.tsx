import { FC, Fragment, useEffect } from 'react';

import { useSWRConfig } from 'swr';

import { useApplication } from '@common/application';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import {
  deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitch,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProducts,
  Promotion,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import mapScEnumToMap from '@dc-be/utils/mapScEnumToMap';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { MyEnecoProductDetailsRendering } from '@sitecore/types/MyEnecoProductDetails';
import { Badge, Bleed, Bucket, IconButton, InputSelect, Skeleton, Stack, Text, TextLink } from '@sparky';
import { InfoIcon } from '@sparky/icons';
import { useTracking } from '@tracking';

import ProductCard, { ProductCardFields } from './components/ProductCard';
import InfoIconButton from '../../components/InfoIconButton';
import ReadFormField from '../../components/ReadFormField';
import { canUserSwitchProduct } from './productSwitch/utils/userCanSwitchProduct';
import { useSetNotificationAndScrollToTop } from '../../hooks/useSetNotificationAndScrollToTop';

const ProductDetail = () => {
  const TERMINATED_CONTRACT_STATUS = 'Terminated';
  const { data: session } = useSession();
  const { selectedAccount, isCurrentAccountReader, isProspect } = useSelfServiceAccount();
  const { routerBasePath, pushHref } = useRouter();
  const { fields } = useContent<MyEnecoProductDetailsRendering>();
  const { date, format } = useFormatter();
  const { language } = useApplication();
  const { searchParams } = useApplication();
  const { trackEventBE } = useTracking();
  const setNotification = useSetNotificationAndScrollToTop();
  const { mutate } = useSWRConfig();

  const contractNumber = searchParams.get('contract') || '';

  const { data, isLoading } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProducts,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        contractNumber,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/contracts/${contractNumber}/products`],
    session,
  );

  const productData = unwrapData(data);

  useEffect(() => {
    if (productData?.productName)
      trackEventBE('product_details', {
        status: 'pageView',
        data: { productName: productData?.productName },
      });
  }, [productData?.productName, trackEventBE]);

  if (data?.error?.errors?.status === 404) {
    pushHref(routerBasePath);
    return;
  }

  if (isProspect) return null;

  const meterTypes = mapScEnumToMap(fields.meterCard.typesList?.value ?? null);
  const serviceComponents = mapScEnumToMap(fields.meterCard.serviceComponentTypesList?.value ?? null);
  const productTypes = mapScEnumToMap(fields.meterCard.productTypesList?.value ?? null);
  const meterRegimes = mapScEnumToMap(fields.meterCard.meterRegimeTypesList?.value ?? null);

  const isActiveProduct = productData?.contractProductStatusExternal !== TERMINATED_CONTRACT_STATUS || false;
  const isDynamicTariff = productData?.tariff?.productType === 'Dynamic';

  const canUpdateMeterRegime =
    !isCurrentAccountReader &&
    isActiveProduct &&
    !productData?.meter?.meterRegime?.requested &&
    !isDynamicTariff &&
    !(productData?.meter?.meterRegime?.current === 'NotApplicable') &&
    productData?.type !== 'Gas';

  let activeFrom: string | undefined;
  let renewalDate: string | undefined;
  const isRenewal =
    productData?.renewal?.renewalType === 'Regular' ||
    productData?.renewal?.renewalType === 'Migration' ||
    productData?.renewal?.renewalType === 'TacitChanged';
  const twoMonthsLater = new Date();
  twoMonthsLater.setMonth(twoMonthsLater.getMonth() + 2);

  try {
    if (productData?.tariffChartStartDate) activeFrom = date.long(productData?.tariffChartStartDate);
    if (productData?.renewal?.nextRenewalDate) {
      renewalDate = date.long(productData?.renewal.nextRenewalDate);
      const nextRenewalDate = new Date(productData?.renewal.nextRenewalDate);
      if (nextRenewalDate >= twoMonthsLater) {
        renewalDate = undefined;
      }
    }
  } catch {
    activeFrom = undefined;
    renewalDate = undefined;
  }

  const productCardFields: ProductCardFields = {
    productStatusesList: fields.productCardStatus.productStatusesList,
    card: {
      undeterminedTerminationText: fields.productCardDetails.undeterminedTerminationText,
      undeterminedTerminationPopoverText: fields.productCardDetails.undeterminedTerminationPopoverText,
      terminatesAtText: fields.productCardDetails.terminatesAtText,
      terminatesAtPopoverText: fields.productCardDetails.terminatesAtPopoverText,
      terminatedAtPopoverText: fields.productCardDetails.terminatedAtPopoverText,
      terminatedAtText: fields.productCardDetails.terminatedAtText,
      inProgressText: fields.productCardDetails.inProgressText,
      inProgressPopoverText: fields.productCardDetails.inProgressPopoverText,
      startsAtText: fields.productCardDetails.startsAtText,
      startsAtPopoverText: fields.productCardDetails.startsAtPopoverText,
      energyTypeElectricityText: fields.productCardDetails.energyTypeElectricityText,
      energyTypeGasText: fields.productCardDetails.energyTypeGasText,
      detailsLink: fields.productCardDetails.link,
    },
  };

  let currentTariffCardText = activeFrom
    ? format(fields.tariffCard.currentTariffFormField.value.label, { date: activeFrom })
    : '';
  if (productData?.contractProductStatusExternal === 'Terminated') {
    currentTariffCardText = productData?.activeUntil
      ? format(fields.tariffCard.expiredTariffCardLabel?.value, { date: date.short(productData?.activeUntil) })
      : '';
  }

  const cancelProductSwitch = async () => {
    try {
      const { response } =
        await deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitch({
          path: {
            accountNumber: selectedAccount.crmAccountNumber,
            addressIdentifier: productData?.addressId ?? '',
          },
          query: {
            ean: productData?.meter?.ean ?? '',
            productSwitchId: productData?.renewal?.productSwitchId ?? '',
          },
        });
      if (!response.ok) {
        throw new Error('Failed to cancel product switch');
      }
      setNotification({
        notificationOptions: {
          title: fields.tariffCard.cancelProductSwitchSuccessNotification?.value.title ?? '',
          text: fields.tariffCard.cancelProductSwitchSuccessNotification?.value.text ?? '',
          variant: 'success',
        },
      });
      mutate(`/accounts/${selectedAccount.crmAccountNumber}/contracts/${contractNumber}/products`);
    } catch {
      setNotification({
        notificationOptions: {
          title: fields.tariffCard.cancelProductSwitchErrorNotification?.value.title ?? '',
          text: fields.tariffCard.cancelProductSwitchErrorNotification?.value.text ?? '',
          variant: 'error',
        },
      });
    }
  };

  return (
    <Stack gap={6}>
      {isLoading && (
        <Stack.Item grow>
          <Stack gap="2">
            <Text size="BodyS" weight="bold">
              {fields.productDetails.locationFormField.value.label}
            </Text>
            <Skeleton variant="rectangular" width={'100%'} height={48} />
          </Stack>
        </Stack.Item>
      )}
      {productData?.address && (
        <InputSelect
          label={fields.productDetails.locationFormField.value.label}
          name="location"
          options={[{ value: productData?.address, label: productData?.address }]}
          placeholder={''}
          isDisabled></InputSelect>
      )}
      <ProductCard
        contractId={productData?.contractId}
        contractProductStatusExternal={productData?.contractProductStatusExternal}
        startTime={productData?.activeFrom}
        endTime={productData?.activeUntil}
        productName={productData?.productName}
        type={productData?.type}
        fields={productCardFields}
        isLoading={isLoading}
      />
      <Bucket title={fields.tariffCard.title.value}>
        <Bucket.Content>
          <ReadFormField
            label={fields.tariffCard.productNameFormField.value.label}
            value={productData?.productName}
            isLoading={isLoading}
          />
          <ReadFormField
            label={fields.meterCard.typeLabel.value}
            value={productTypes.get(productData?.tariff?.productType)}
            infoText={fields.tariffCard.productNameFormField.value.hint}
            isLoading={isLoading}
          />
          {(productData?.tariff?.promotions?.length ?? 0) > 0 && (
            <ReadFormField
              label={fields.tariffCard.promotionFormField.value.label}
              value={<PromotionBadges promotions={productData?.tariff?.promotions} />}
              isLoading={isLoading}
            />
          )}
          {activeFrom && (productData?.tariff?.currentTariff?.length ?? 0) > 0 && (
            <ReadFormField
              label={currentTariffCardText}
              value={
                <Stack gap="2">
                  {productData?.tariff?.currentTariff?.map((tariff, i) => {
                    return (
                      <Fragment key={(tariff.startDate ?? 0) + '' + i}>
                        {i > 0 ? (
                          <TextLink target="_blank" href={tariff?.tariffCardByLanguage?.[language.toUpperCase()]}>
                            {fields.tariffCard.currentTariffAddendumLinkText.value}
                          </TextLink>
                        ) : (
                          <TextLink target="_blank" href={tariff?.tariffCardByLanguage?.[language.toUpperCase()]}>
                            {fields.tariffCard.currentTariffLink.value.text}
                          </TextLink>
                        )}
                      </Fragment>
                    );
                  })}
                </Stack>
              }
              isLoading={isLoading}
            />
          )}
          {isRenewal && renewalDate && (productData?.renewal?.tariff?.currentTariff?.length ?? 0) > 0 && (
            <ReadFormField
              label={format(fields.tariffCard.nextTariffFormField.value.label, { date: renewalDate })}
              value={
                <Stack gap="2">
                  {productData?.renewal?.tariff?.currentTariff?.map((tariff, i) => {
                    return (
                      <Fragment key={(tariff.startDate ?? 0) + '' + i}>
                        {i > 0 ? (
                          <TextLink target="_blank" href={tariff?.tariffCardByLanguage?.[language.toUpperCase()]}>
                            {fields.tariffCard.nextTariffAddendumLinkText.value}
                          </TextLink>
                        ) : (
                          <TextLink target="_blank" href={tariff?.tariffCardByLanguage?.[language.toUpperCase()]}>
                            {fields.tariffCard.nextTariffLink.value.text}
                          </TextLink>
                        )}
                      </Fragment>
                    );
                  })}
                </Stack>
              }
              isLoading={isLoading}
            />
          )}
          {canUserSwitchProduct({
            isCurrentAccountReader,
            isActiveProduct,
            hasPendingProductSwitch:
              productData?.renewal?.status === 'PendingProductSwitch' ||
              productData?.renewal?.status === 'TransitioningToProductSwitch',
            hasProductSwitchOptions: !!productData?.renewal?.hasProductSwitchOptions,
            isSocialTariff: !!productData?.tariff?.isSocialTariff,
          }) && (
            <TextLink
              emphasis="high"
              href={`${fields.tariffCard.productUpdateLink.value.href}?contract=${contractNumber}&ean=${productData?.meter?.ean}`}
              onClick={() =>
                trackEventBE('product_details_edit_product', {
                  status: 'click',
                  data: { productName: productData?.productName },
                })
              }>
              {fields.tariffCard.productUpdateLink.value.text}
            </TextLink>
          )}
          {/* If the product switch is in transition, meaning already accepted by Eneco but only active in the next month, we can show the cancel product switch link */}
          {productData?.renewal?.status === 'TransitioningToProductSwitch' &&
            !!productData?.renewal?.productSwitchId && (
              <TextLink emphasis="high" onClick={cancelProductSwitch}>
                {fields.tariffCard.cancelProductSwitchLink.value.text}
              </TextLink>
            )}
        </Bucket.Content>
      </Bucket>
      <Bucket title={fields.meterCard.title.value}>
        <Bucket.Content>
          <ReadFormField
            label={fields.meterCard.typeLabel.value}
            value={meterTypes.get(productData?.meter?.meterType)}
            isLoading={isLoading}
          />
          <ReadFormField
            label={fields.meterCard.eanFormField.value.label}
            value={productData?.meter?.ean}
            infoText={fields.meterCard.eanInfoPopoverText.value}
            isLoading={isLoading}
          />
          <ReadFormField
            label={fields.meterCard.meterNumberFormField.value.label}
            value={productData?.meter?.meterNumber}
            isLoading={isLoading}
          />
          <ReadFormField
            label={fields.meterCard.serviceLabel.value}
            value={serviceComponents.get(productData?.meter?.service)}
            isLoading={isLoading}
          />
          {productData?.meter?.meterRegime && (
            <ReadFormField
              label={fields.meterCard.meterRegimeLabel.value}
              value={
                productData?.meter.meterRegime.changeRequestPending
                  ? meterRegimes.get(productData?.meter.meterRegime.requested)
                    ? `${meterRegimes.get(productData?.meter.meterRegime.requested)} ${fields.meterCard.meterRegimeChangeRequestPendingDescription.value}`
                    : undefined
                  : meterRegimes.get(productData?.meter.meterRegime.current)
              }
              infoText={fields.meterCard.meterRegimePopoverText.value}
              isLoading={isLoading}
            />
          )}
          {canUpdateMeterRegime && (
            <TextLink
              emphasis="high"
              href={`${fields.meterCard.meterUpdateLink.value.href}?contract=${contractNumber}`}
              onClick={() =>
                trackEventBE('product_details_edit_meter_regime', {
                  status: 'click',
                  data: { productName: productData?.productName },
                })
              }>
              {fields.meterCard.meterUpdateLink.value.text}
            </TextLink>
          )}
        </Bucket.Content>
      </Bucket>
      <Bucket
        title={
          <Stack direction={'row'} alignY={'center'}>
            <Text weight={'bold'}>{fields.gridOperator.title.value}</Text>
            <Bleed vertical={3}>
              <InfoIconButton triggerText={fields.gridOperator.helpText.value}>
                {fields.gridOperator.helpText.value}
              </InfoIconButton>
            </Bleed>
          </Stack>
        }>
        <Bucket.Content>
          <ReadFormField
            label={fields.gridOperator.label.value}
            value={productData?.gridOperator?.name}
            isLoading={isLoading}
          />
        </Bucket.Content>
      </Bucket>
    </Stack>
  );
};

interface PromotionBadgesProps {
  promotions: Array<Promotion> | null | undefined;
}

const PromotionBadges: FC<PromotionBadgesProps> = ({ promotions }) => {
  const { locale } = useRouter();
  const localeIndex = locale.toUpperCase();

  if (!promotions) return null;

  return (
    <Stack gap={1}>
      {promotions.map((promotion, index) => (
        <Stack key={`${promotion.text}${index}`} direction={'row'} alignY={'center'}>
          <Badge>{promotion.text?.[localeIndex] || ''}</Badge>
          <Bleed vertical={3}>
            <TrackedDialog
              trigger={
                <IconButton label={promotion.text?.[localeIndex] || ''}>
                  <InfoIcon />
                </IconButton>
              }>
              {promotion.disclaimer?.[localeIndex]}
            </TrackedDialog>
          </Bleed>
        </Stack>
      ))}
    </Stack>
  );
};

export default ProductDetail;
