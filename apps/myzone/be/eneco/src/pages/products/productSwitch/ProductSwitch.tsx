import { FC, useEffect, useMemo, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import { useApplication } from '@common/application';
import RichText from '@components/RichText/RichText';
import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitch,
  postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitch,
  ProductSwitchRecordDto,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth/useSession';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { ProductSwitchRendering } from '@sitecore/types/ProductSwitch';
import {
  AlertDialog,
  Box,
  Button,
  Divider,
  Form,
  Heading,
  NotificationBox,
  PageGrid,
  RadioCard,
  RadioGroup,
  Skeleton,
  Stack,
  Stretch,
  Text,
  TextLink,
} from '@sparky';
// eslint-disable-next-line dxp-rules/no-custom-styling
import { styled } from '@sparky/stitches';
import { NotificationBoxProps } from '@sparky/types';

import { ProductSwitchHeading } from './ProductSwitchHeading';
import ProductSwitchOverview from './ProductSwitchOverview';
import { canUserSwitchProduct } from './utils/userCanSwitchProduct';
import { RoundedSkeleton } from '../../../components/Loading/RoundedSkeleton';
import { useAddressOptions } from '../../../hooks/useAddressOptions';
import { useRedirectAndNotifyBE } from '../../../hooks/useRedirectAndNotifyBE';
import { inlineCheckmarkReplacement } from '../../../utils/richTextReplacements';

export const SCHEMA_SELECTED_PRODUCTS_SWITCH_ID = 'selectedProductsSwitchId' as const;
export const SCHEMA_IS_AGREEMENT_CHECKED = 'isAgreementChecked' as const;
export const CURRENT = 'current';

const ProductSwitch = () => {
  const { fields } = useContent<ProductSwitchRendering>();
  const { data: session } = useSession();
  const { selectedAccount, isCurrentAccountReader } = useSelfServiceAccount();
  const { addressIdentifier, isLoading: isLoadingAddressOptions } = useAddressOptions();
  const { format } = useFormatter();
  const { searchParams } = useApplication();
  const { mutate } = useSWRConfig();
  const redirectAndNotify = useRedirectAndNotifyBE();
  const { push } = useRouter();
  const { locale } = useApplication();

  const contractIdSearchParam = searchParams.get('contract');
  const eanSearchParam = searchParams.get('ean');

  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);
  const [activeIndexProductSwitch, setActiveIndexProductSwitch] = useState<number>(0);
  const [error, setError] = useState<NotificationBoxProps | null>(null);
  const [isOpenCancelDialog, setIsOpenCancelDialog] = useState<boolean>(false);

  const { data: dataWrapped, isLoading: isLoadingData } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitch,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/products/switch`],
    session,
  );

  const isLoading = isLoadingData || isLoadingAddressOptions;

  const data = unwrapData(dataWrapped);

  const sortedProducts = useMemo(() => {
    if (!data?.records) return [];
    return [...data.records]
      .filter(x =>
        canUserSwitchProduct({
          isCurrentAccountReader,
          hasPendingProductSwitch: !!x.current?.hasActiveProductSwitch,
          hasProductSwitchOptions: !!x.switchOptions?.length,
          isActiveProduct: !!x.current?.isActive,
          isSocialTariff: !!x.current?.hasSocialTarriff,
        }),
      )
      .sort((a, b) => {
        // 1) force the matching EAN to the front
        const aIsMatch = a.current?.ean === eanSearchParam;
        const bIsMatch = b.current?.ean === eanSearchParam;
        if (aIsMatch && !bIsMatch) return -1;
        if (bIsMatch && !aIsMatch) return 1;

        // 2) otherwise, sort by energyType from the matching EAN
        const startingEnergyType = data.records?.find(x => x.current?.ean === eanSearchParam)?.current?.energyType;

        if (startingEnergyType === 'Gas') {
          return (b.current?.energyType ?? '').localeCompare(a.current?.energyType ?? '');
        }
        return (a.current?.energyType ?? '').localeCompare(b.current?.energyType ?? '');
      });
  }, [data?.records, eanSearchParam, isCurrentAccountReader]);

  const activeProduct: ProductSwitchRecordDto | undefined = sortedProducts?.[activeIndexProductSwitch];
  const isElectricity = activeProduct?.current?.energyType === 'Electricity';

  type FormValues = yup.InferType<typeof FormSchema>;

  const FormSchema = yup.object({
    [SCHEMA_SELECTED_PRODUCTS_SWITCH_ID]: yup.array().of(yup.string().required('error')).required('error'),
    [SCHEMA_IS_AGREEMENT_CHECKED]: yup
      .boolean()
      .oneOf([true], fields.summaryStep.termsAndConditionsCheckboxFormField.value.validationMessage),
  });

  const resolver = yupResolver(FormSchema);

  const form = useForm<FormValues>({
    resolver,
    mode: 'onBlur',
    defaultValues: {
      selectedProductsSwitchId: [],
    },
  });

  const { control, handleSubmit, watch, setValue } = form;
  const selectedProductsSwitchId = watch('selectedProductsSwitchId');

  const submitForm: SubmitHandler<FormValues> = async ({ selectedProductsSwitchId }) => {
    try {
      setIsLoadingSubmit(true);
      const switchIdsAttachedEans: { productSwitchId: string; ean: string }[] = sortedProducts.map((sp, index) => ({
        ean: sp.current?.ean ?? '',
        productSwitchId: selectedProductsSwitchId?.[index],
      }));
      const actualSwitchIdsAttachedEans = switchIdsAttachedEans.filter(x => x.productSwitchId !== CURRENT);
      const { response } =
        await postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitch({
          path: { accountNumber: selectedAccount.crmAccountNumber, addressIdentifier },
          body: {
            records: actualSwitchIdsAttachedEans,
          },
        });
      if (!response.ok) {
        setIsLoadingSubmit(false);
        return setError({
          isAlert: false,
          text: fields.navigation.errorNotification.value.content,
          title: fields.navigation.errorNotification.value.title,
          variant: fields.navigation.errorNotification.value.variant,
        });
      }
      await mutate([`/accounts/${selectedAccount.crmAccountNumber}/products/switch`]);
      redirectAndNotify({
        route: `${fields.navigation.saveLink.value.href}?contract=${contractIdSearchParam}`,
        text: fields.navigation.successNotification.value.content,
        title: fields.navigation.successNotification.value.title,
        variant: fields.navigation.successNotification.value.variant,
      });
    } catch (e) {
      void e;
      setIsLoadingSubmit(false);
      return setError({
        isAlert: false,
        text: fields.navigation.errorNotification.value.content,
        title: fields.navigation.errorNotification.value.title,
        variant: fields.navigation.errorNotification.value.variant,
      });
    }
  };

  const editPreviousSelectedSwitch = (ean: string) => {
    const index = sortedProducts?.findIndex(p => p.current?.ean === ean);
    if (index != null) setActiveIndexProductSwitch(index);
  };

  useEffect(() => {
    data?.records?.forEach((_, index) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setValue(`selectedProductsSwitchId.${index}` as any, CURRENT);
      return;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.records]);

  useEffect(() => {
    if (!isLoading && data && !sortedProducts.length) {
      setIsLoadingSubmit(true);
      redirectAndNotify({
        route: `${fields.navigation.cancelLink.value.href}?contract=${contractIdSearchParam}`,
        text: <RichText html={fields.navigation.keepCurrentProductNotification.value.content} />,
        title: fields.navigation.keepCurrentProductNotification.value.title,
        variant: fields.navigation.keepCurrentProductNotification.value.variant,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, data, sortedProducts, redirectAndNotify, contractIdSearchParam]);

  const CompareProduct = ({ activeProduct }: { activeProduct: ProductSwitchRecordDto | undefined }) => {
    if (!activeProduct) return null;

    const nextButtonOnClick = () => {
      // Only redirect if the user does not want to switch for the FIRST product & has no other products selected (= 'current' for every product). Otherwise, we show upselling flow until the overview page
      const onlyCurrentProductsSelected = (selectedProductsSwitchId as string[]).every(
        switchId => switchId === CURRENT,
      );
      if (onlyCurrentProductsSelected && activeIndexProductSwitch === 0) {
        setIsLoadingSubmit(true);
        redirectAndNotify({
          route: `${fields.navigation.cancelLink.value.href}?contract=${contractIdSearchParam}`,
          text: <RichText html={fields.navigation.keepCurrentProductNotification.value.content} />,
          title: fields.navigation.keepCurrentProductNotification.value.title,
          variant: fields.navigation.keepCurrentProductNotification.value.variant,
        });
      } else {
        if (activeIndexProductSwitch < (data?.records?.length ?? 0)) {
          setActiveIndexProductSwitch(prev => prev + 1);
        }
      }
    };

    return (
      <>
        <ProductSwitchHeading
          title={
            <span>
              {isElectricity
                ? format(fields.electricityStep.title.value, { step: activeIndexProductSwitch + 1 })
                : format(fields.gasStep.title.value, { step: activeIndexProductSwitch + 1 })}
            </span>
          }
          subTitle={format(isElectricity ? fields.electricityStep.subTitle.value : fields.gasStep.subTitle.value, {
            ean: activeProduct.current?.ean ?? '',
          })}
          sortedProducts={sortedProducts}
          stepperLabels={{
            electricityStepLabel: fields.navigation.electricityStepLabel.value,
            gasStepLabel: fields.navigation.gasStepLabel.value,
            summaryStepLabel: fields.navigation.summaryStepLabel.value,
          }}
          activeIndexProductSwitch={activeIndexProductSwitch}
        />

        <Controller
          control={control}
          name={`selectedProductsSwitchId.${activeIndexProductSwitch}`}
          render={({ field: { onChange, value, ...rest } }) => (
            <RadioGroup aria-labelledby="productSwitch" isFullWidth value={value} onValueChange={onChange} {...rest}>
              <Stack direction={{ initial: 'column', md: 'row' }} gap="6">
                {activeProduct?.current && (
                  <ProductProposalItem
                    key={activeProduct.current.ean}
                    value={CURRENT}
                    isCurrent={true}
                    id={activeProduct.current.ean || ''}
                    technicalProductName={activeProduct.current.technicalProductName || ''}
                    promoLoss={activeProduct.current.promoLoss}
                    tariffChartUrl={
                      locale === 'fr-BE'
                        ? activeProduct.current.tariffChartUrlFr
                        : activeProduct.current.tariffChartUrlNl
                    }
                  />
                )}
                {activeProduct?.switchOptions?.map(item => {
                  return (
                    <ProductProposalItem
                      key={item.switchId}
                      value={item.switchId || ''}
                      isCurrent={false}
                      id={item.switchId || ''}
                      technicalProductName={item.technicalProductName || ''}
                      promoLoss={activeProduct.current?.promoLoss}
                      tariffChartUrl={locale === 'fr-BE' ? item.tariffChartUrlFr : item.tariffChartUrlNl}
                    />
                  );
                })}
              </Stack>
            </RadioGroup>
          )}
        />

        <Text size="BodyXS" color="textLowEmphasis">
          <RichText
            html={
              isElectricity
                ? fields.electricityStep.productSwitchDescription.value
                : fields.gasStep.productSwitchDescription.value
            }
          />
        </Text>
        <Stack direction="row" gap={5}>
          <Button isLoading={isLoadingSubmit} onClick={nextButtonOnClick}>
            {selectedProductsSwitchId?.[activeIndexProductSwitch] === CURRENT
              ? fields.navigation.keepCurrentProductText.value
              : isElectricity
                ? fields.electricityStep.continueButtonText.value
                : fields.gasStep.continueButtonText.value}
          </Button>
          <AlertDialog
            isOpen={isOpenCancelDialog}
            setOpen={setIsOpenCancelDialog}
            confirmText={fields.navigation.cancelDialog.value.submitButtonText ?? ''}
            denyText={fields.navigation.cancelDialog.value.cancelButtonText ?? ''}
            title={fields.navigation.cancelDialog.value.title}
            description={<RichText html={fields.navigation.cancelDialog.value.content} />}
            trigger={<Button action="secondary">{fields.navigation.cancelLink.value.text}</Button>}
            onConfirm={() => {
              push(`${fields.navigation.cancelLink.value.href}?contract=${contractIdSearchParam}`);
            }}
            onDeny={() => {}}
          />
        </Stack>
      </>
    );
  };

  const ProductSwitchContent = () => {
    return sortedProducts && activeIndexProductSwitch > sortedProducts.length - 1 ? (
      <ProductSwitchOverview
        sortedProducts={sortedProducts}
        editPreviousSelectedSwitch={editPreviousSelectedSwitch}
        isLoadingSubmit={isLoadingSubmit}
      />
    ) : (
      <CompareProduct activeProduct={activeProduct} />
    );
  };

  return (
    <FormProvider {...form}>
      <Form onSubmit={handleSubmit(submitForm)}>
        <Box paddingY={{ initial: '4', md: '12' }}>
          <PageGrid>
            <PageGrid.Item gridColumn="1/-1">
              <Stack gap="10" alignX={'start'}>
                {isLoading ? <CompareProductSkeletons /> : <ProductSwitchContent />}
                {error && <NotificationBox {...error} />}
              </Stack>
            </PageGrid.Item>
          </PageGrid>
        </Box>
      </Form>
    </FormProvider>
  );
};

type ProductProposalItemProps = {
  isCurrent: boolean;
  id: string;
  value: string;
  technicalProductName: string;
  promoLoss?: boolean;
  tariffChartUrl: string | null | undefined;
};

const StyledStack = styled(Stack, {
  flex: 1,
  '& .sparky-card': {
    height: '100%',
  },
  '& .sparky-cardOverlay': {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  '& .sparky-cardOverlay > .sparky-stack': {
    flex: 1,
  },
});

const HiddenRibbonContainer = styled('div', {
  display: 'none',
  '@md': {
    display: 'block',
    visibility: 'hidden',
  },
});

const ProductProposalItem: FC<ProductProposalItemProps> = ({
  isCurrent,
  id,
  value,
  technicalProductName,
  promoLoss,
  tariffChartUrl,
}) => {
  const { fields } = useContent<ProductSwitchRendering>();

  const sitecoreProduct = fields.items.find(item => item.name === technicalProductName);
  const productName = sitecoreProduct?.fields.data.productName.value || technicalProductName + ' [Not in Sitecore]';
  const productFeatures = sitecoreProduct?.fields.data.productFeaturesContent.value;
  const asterisk = promoLoss && !isCurrent ? '*' : null;

  return (
    <StyledStack>
      {!isCurrent && (
        // this is a workaround to hide the ribbon when the product is not current, but still have the correct height
        <HiddenRibbonContainer aria-hidden={true}>
          <RadioCard.Ribbon emphasis={'medium'}>{fields.productProposal.currentProductLabel.value}</RadioCard.Ribbon>
        </HiddenRibbonContainer>
      )}
      <Stack.Item grow>
        <RadioCard key={id} value={value} ariaLabelledby={id}>
          {isCurrent && <RadioCard.Ribbon>{fields.productProposal.currentProductLabel.value}</RadioCard.Ribbon>}
          <Stack>
            <Box padding="6">
              <Stack gap="2">
                <Stack direction={{ initial: 'column', lg: 'row' }} gap="3">
                  <Stack direction={'row'} gap="3" alignY={'center'}>
                    <RadioCard.Indicator />
                    <Heading size="XS" as="h3">
                      {productName} {asterisk}
                    </Heading>
                  </Stack>
                </Stack>
                <RichText html={productFeatures} replacements={inlineCheckmarkReplacement} />
              </Stack>
            </Box>
            <Stack.Item grow />
            {tariffChartUrl && (
              <>
                <Divider />
                <Box padding="4">
                  <Stack alignX={'center'}>
                    <TextLink href={tariffChartUrl} target="_blank">
                      {fields.productProposal.rateCardLinkLabel.value}
                    </TextLink>
                  </Stack>
                </Box>
              </>
            )}
          </Stack>
        </RadioCard>
      </Stack.Item>
    </StyledStack>
  );
};

export default ProductSwitch;

const HiddenLoadingText = styled(Text, {
  visibility: 'hidden',
  paddingTop: '$3',
  paddingBottom: '$3',
});

const CompareProductSkeletons = () => {
  return (
    <Stretch>
      <Stack gap="10">
        <Stretch>
          <Box backgroundColor="backgroundPrimary">
            <HiddenLoadingText aria-hidden={true}>Loading...</HiddenLoadingText>
          </Box>
        </Stretch>
        <Stack gap="2">
          <Skeleton width={150} height={32} />
          <Skeleton />
          <Skeleton />
          <Skeleton width={250} />
        </Stack>

        <StyledStack direction={{ initial: 'column', lg: 'row' }} gap={10} wrap>
          <Stack.Item grow>
            <RoundedSkeleton height={250} />
          </Stack.Item>
          <Stack.Item grow>
            <RoundedSkeleton height={250} />
          </Stack.Item>
          <Stack.Item grow>
            <RoundedSkeleton height={250} />
          </Stack.Item>
        </StyledStack>
      </Stack>
    </Stretch>
  );
};
