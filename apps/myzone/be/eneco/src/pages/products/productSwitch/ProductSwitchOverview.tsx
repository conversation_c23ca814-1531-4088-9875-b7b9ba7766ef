import { FC, useState } from 'react';

import { addMonths, formatDate, startOfMonth } from 'date-fns';
import { nanoid } from 'nanoid';
import { useFormContext } from 'react-hook-form';

import { useApplication } from '@common/application';
import RichText from '@components/RichText/RichText';
import { ProductSwitchRecordDto } from '@dc-be/client';
import { useRouter } from '@dxp-next';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { ProductSwitchRendering } from '@sitecore/types/ProductSwitch';
import { AlertDialog, Button, Checkbox, Grid, Stack, Stretch } from '@sparky';

import { CURRENT, SCHEMA_SELECTED_PRODUCTS_SWITCH_ID } from './ProductSwitch';
import { ProductSwitchHeading } from './ProductSwitchHeading';
import SummaryCard from './SummaryCard';
import { getDateLocale } from '../../../utils/date/get-locale';

type ProductSwitchOverviewProps = {
  sortedProducts: ProductSwitchRecordDto[];
  editPreviousSelectedSwitch: (ean: string) => void;
  isLoadingSubmit?: boolean;
};

const ProductSwitchOverview: FC<ProductSwitchOverviewProps> = ({
  sortedProducts,
  editPreviousSelectedSwitch: editPreviousSelectedProductSwitch,
  isLoadingSubmit,
}) => {
  const { fields } = useContent<ProductSwitchRendering>();
  const { locale, searchParams } = useApplication();
  const { push } = useRouter();
  const { format } = useFormatter();
  const {
    watch,
    register,
    formState: { errors },
  } = useFormContext();

  const [isOpenCancelDialog, setIsOpenCancelDialog] = useState<boolean>(false);

  const contractIdSearchParam = searchParams.get('contract');
  const selectedProductSwitchIds: string[] = watch(SCHEMA_SELECTED_PRODUCTS_SWITCH_ID);

  return (
    <>
      <ProductSwitchHeading
        title={fields.summaryStep.title.value}
        subTitle={format(fields.summaryStep.description.value, {
          date: formatDate(startOfMonth(addMonths(new Date(), 1)), 'dd MMMM yyyy', { locale: getDateLocale(locale) }),
        })}
        sortedProducts={sortedProducts}
        stepperLabels={{
          electricityStepLabel: fields.navigation.electricityStepLabel.value,
          gasStepLabel: fields.navigation.gasStepLabel.value,
          summaryStepLabel: fields.navigation.summaryStepLabel.value,
        }}
        activeIndexProductSwitch={sortedProducts?.length}
      />
      <Stack direction="column" gap={6}>
        <Stack.Item>
          <Stretch>
            <Grid
              columns={{
                initial: 2,
                sm: 2,
                lg: selectedProductSwitchIds.filter(id => id !== CURRENT).length <= 2 ? 2 : 4,
              }}
              alignY="stretch"
              gap={6}>
              {selectedProductSwitchIds.map((id, index) => (
                <ProductCard
                  switchId={id}
                  index={index}
                  key={nanoid()}
                  sortedProducts={sortedProducts}
                  editPreviousSelectedProductSwitch={editPreviousSelectedProductSwitch}
                />
              ))}
            </Grid>
          </Stretch>
        </Stack.Item>
        <Stack.Item>
          <Checkbox
            label={fields.summaryStep.termsAndConditionsCheckboxFormField.value.label}
            {...register('isAgreementChecked')}
            error={(errors?.isAgreementChecked?.message as string) ?? ''}
          />
        </Stack.Item>
      </Stack>
      <Stack direction="row" gap={4}>
        <Button type="submit" isLoading={isLoadingSubmit}>
          {fields.navigation.saveLink.value.text}
        </Button>
        <AlertDialog
          isOpen={isOpenCancelDialog}
          setOpen={setIsOpenCancelDialog}
          confirmText={fields.navigation.cancelDialog.value.submitButtonText ?? ''}
          denyText={fields.navigation.cancelDialog.value.cancelButtonText ?? ''}
          title={fields.navigation.cancelDialog.value.title}
          description={<RichText html={fields.navigation.cancelDialog.value.content} />}
          trigger={<Button action="secondary">{fields.navigation.cancelLink.value.text}</Button>}
          onConfirm={() => {
            push(`${fields.navigation.cancelLink.value.href}?contract=${contractIdSearchParam}`);
          }}
          onDeny={() => {}}
        />
      </Stack>
    </>
  );
};

const ProductCard = ({
  switchId,
  index,
  sortedProducts,
  editPreviousSelectedProductSwitch,
}: {
  switchId: string;
  index: number;
  sortedProducts: ProductSwitchRecordDto[];
  editPreviousSelectedProductSwitch: (ean: string) => void;
}) => {
  const { fields } = useContent<ProductSwitchRendering>();
  const { locale } = useApplication();

  const isFrench = locale === 'fr-BE';
  if (!sortedProducts) return null;

  const originalProduct = sortedProducts?.[index]?.current;
  const newProduct = sortedProducts?.[index]?.switchOptions?.find(option => option.switchId === switchId);

  if (newProduct == null || originalProduct == null) return null;

  const isElectricity = originalProduct?.energyType === 'Electricity';
  const fieldGroup = isElectricity ? fields.summaryElectricityCard : fields.summaryGasCard;

  return (
    <Grid.Item>
      <Stretch height={true}>
        <SummaryCard
          fieldGroup={fieldGroup}
          originalProduct={originalProduct}
          newProduct={newProduct}
          isFrench={isFrench}
          onEdit={editPreviousSelectedProductSwitch}
        />
      </Stretch>
    </Grid.Item>
  );
};

export default ProductSwitchOverview;
