import { ReactNode } from 'react';

import RichText from '@components/RichText/RichText';
import { ProductSwitchRecordDto } from '@dc-be/client';
import { Box, Heading, ProgressIndicator, Stack, Stretch } from '@sparky';

import { inlineStrongReplacement } from '../../../utils/richTextReplacements';

export const ProductSwitchHeading = ({
  title,
  subTitle,
  activeIndexProductSwitch,
  sortedProducts,
  stepperLabels,
}: {
  title: ReactNode;
  subTitle: string;
  sortedProducts: ProductSwitchRecordDto[];
  activeIndexProductSwitch: number;
  stepperLabels: { summaryStepLabel: string; electricityStepLabel: string; gasStepLabel: string };
}) => {
  return (
    <>
      <Stretch>
        <Box backgroundColor="backgroundPrimary">
          <ProgressIndicator minChildrenToHideLabels={1}>
            {/* Items that are eligible for product switch */}
            {sortedProducts?.map((product, index) => (
              <ProgressIndicator.Item
                key={product.current?.ean}
                isActive={activeIndexProductSwitch === index}
                onClick={() => {}}>
                {product.current?.energyType === 'Electricity'
                  ? stepperLabels.electricityStepLabel
                  : stepperLabels.gasStepLabel}
              </ProgressIndicator.Item>
            ))}

            {/* Overview page */}
            <ProgressIndicator.Item
              key={'overview'}
              isActive={activeIndexProductSwitch > sortedProducts.length - 1}
              onClick={() => {}}>
              {stepperLabels.summaryStepLabel}
            </ProgressIndicator.Item>
          </ProgressIndicator>
        </Box>
      </Stretch>

      <Stack gap="2">
        <Heading size="M" as="h2" id="productSwitch">
          {title}
        </Heading>
        <div>
          <RichText html={subTitle} replacements={inlineStrongReplacement} />
        </div>
      </Stack>
    </>
  );
};
