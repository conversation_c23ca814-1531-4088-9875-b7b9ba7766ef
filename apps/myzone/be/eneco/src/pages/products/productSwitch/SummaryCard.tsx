import { FC } from 'react';

import RichText from '@components/RichText/RichText';
import { ProductSwitchCurrentProductResponse, ProductSwitchProductResponse } from '@dc-be/client';
import { ProductSwitchRendering } from '@sitecore/types/ProductSwitch';
import { Bucket, Stack, Stretch, TextLink } from '@sparky';
// eslint-disable-next-line dxp-rules/no-custom-styling
import { styled } from '@sparky/stitches';

import InfoText from '../../../components/InfoText';
import ReadFormField from '../../../components/ReadFormField';

type SummaryCardProps = {
  fieldGroup:
    | ProductSwitchRendering['fields']['summaryElectricityCard']
    | ProductSwitchRendering['fields']['summaryGasCard'];
  originalProduct: ProductSwitchCurrentProductResponse;
  newProduct: ProductSwitchProductResponse;
  isFrench: boolean;
  onEdit: (ean: string) => void;
};

const SummaryCard: FC<SummaryCardProps> = ({
  fieldGroup,
  originalProduct: originalContract,
  newProduct: newContract,
  isFrench,
  onEdit,
}) => (
  <Stretch height={true}>
    <StretchedBucket title={fieldGroup.title.value}>
      <Bucket.Content>
        <Stack.Item grow>
          <Stack direction="column" gap={6}>
            <ReadFormField label={fieldGroup.eanLabel.value} value={originalContract.ean} />
            <ReadFormField label={fieldGroup.productNameLabel.value} value={newContract.technicalProductName} />
            <ReadFormField
              label={
                <Stack alignY="center" direction="row" gap={2}>
                  <span>{fieldGroup.annualAmountLabel.value}</span>
                  <InfoText triggerText={''}>
                    <RichText html={fieldGroup.annualAmountDescription?.value ?? '[not in sitecore]'} />
                  </InfoText>
                </Stack>
              }
              value={newContract.preferred}
            />
            <ReadFormField
              label={fieldGroup.rateCardLabel.value}
              value={
                <TextLink
                  target="_blank"
                  href={isFrench ? (newContract.tariffChartUrlFr ?? '') : (newContract.tariffChartUrlNl ?? '')}>
                  {fieldGroup.rateCardLinkLabel.value}
                </TextLink>
              }
            />
          </Stack>
        </Stack.Item>
        <Stack.Item grow />
        <Stack.Item shrink>
          {originalContract.ean && (
            <TextLink onClick={() => onEdit(originalContract.ean!)} emphasis="high">
              {fieldGroup.editButtonLabel.value}
            </TextLink>
          )}
        </Stack.Item>
      </Bucket.Content>
    </StretchedBucket>
  </Stretch>
);

export default SummaryCard;

// th-child(3) Needed to stretch the content in a Bucket.
const StretchedBucket = styled(Bucket, {
  height: '100% !important',
  '& > div:nth-child(3)': {
    height: '100% !important',
  },
  '& > div:nth-child(3) > div': {
    height: '100% !important',
  },
});
