import { FC } from 'react';

import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts,
  GetOverviewForAddressResponseItem,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { MyEnecoProductRendering } from '@sitecore/types/MyEnecoProduct';
import { Bleed, Box, Card, Heading, Image, Stack, TextLink } from '@sparky';

import ProductCard, { ProductCardFields } from './components/ProductCard';
import { AddressFilter } from '../../components/AddressFilter';
import DataErrorNotification from '../../components/DataErrorNotification';
import { LoadingText } from '../../components/Loading/LoadingText';
import { RoundedSkeleton } from '../../components/Loading/RoundedSkeleton';
import { useAddressOptions } from '../../hooks/useAddressOptions';

const ProductOverview = () => {
  const { fields } = useContent<MyEnecoProductRendering>();
  const { addressIdentifier, addressOptions, isLoading, error: addressError } = useAddressOptions();
  const { isProspect } = useSelfServiceAccount();
  const productCardFields: ProductCardFields = {
    productStatusesList: fields.productCardStatus.productStatusesList,
    card: {
      undeterminedTerminationText: fields.productCardDetails.undeterminedTerminationText,
      undeterminedTerminationPopoverText: fields.productCardDetails.undeterminedTerminationPopoverText,
      terminatesAtText: fields.productCardDetails.terminatesAtText,
      terminatedAtText: fields.productCardDetails?.terminatedAtText ?? {
        value: 'Uw contract is beëndigd op {date} [not in sitecore]',
      },
      terminatesAtPopoverText: fields.productCardDetails.terminatesAtPopoverText,
      terminatedAtPopoverText: fields.productCardDetails?.terminatedAtPopoverText ?? {
        value: 'Uw contract is beëndigd op {date} [not in sitecore]',
      },
      startsAtText: fields.productCardDetails.startsAtText,
      startsAtPopoverText: fields.productCardDetails.startsAtPopoverText,
      energyTypeElectricityText: fields.productCardDetails.energyTypeElectricityText,
      energyTypeGasText: fields.productCardDetails.energyTypeGasText,
      detailsLink: fields.productCardDetails.link,
      inProgressPopoverText: fields.productCardDetails.inProgressPopoverText,
      inProgressText: fields.productCardDetails.inProgressText,
    },
  };

  if (isProspect) return null;

  const fetchingAddressError = addressError !== undefined && addressIdentifier === null;

  if (!isLoading && fetchingAddressError) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Stack gap={6}>
      <AddressFilter label={fields.productOverview.locationFormField.value.label} />
      <DeliveryAddressProducts
        isLoadingAddress={isLoading}
        addressIdentifier={addressIdentifier}
        productCardFields={productCardFields}
      />
      {addressOptions?.length === 0 && (
        <Stack.Item>
          <Card>
            {
              <Box paddingTop={10}>
                <Image
                  src={fields.addProductCta.image.value.formats[0].src}
                  alt={fields.addProductCta.image.value.alt}
                  height="150px"
                  width="100%"
                  objectFit="contain"
                />
              </Box>
            }
            <Box padding={6}>
              <Stack gap="1">
                <Heading as="h3" size="3XS">
                  {fields.addProductCta.title.value}
                </Heading>
                <Box paddingTop="3">
                  <TextLink
                    emphasis="high"
                    href={fields.addProductCta.link.value.href}
                    target={fields.addProductCta.link.value.target}>
                    {fields.addProductCta.link.value.text}
                  </TextLink>
                </Box>
              </Stack>
            </Box>
          </Card>
        </Stack.Item>
      )}
      <Bleed bottom="2" />
    </Stack>
  );
};

interface DeliveryAddressProductsProps {
  addressIdentifier: string;
  productCardFields: ProductCardFields;
  isLoadingAddress: boolean;
}

const DeliveryAddressProducts: FC<DeliveryAddressProductsProps> = ({
  addressIdentifier,
  productCardFields,
  isLoadingAddress,
}) => {
  const { fields } = useContent<MyEnecoProductRendering>();
  const {
    selectedAccount: { crmAccountNumber: accountNumber },
  } = useSelfServiceAccount();
  const { data: session } = useSession();

  const {
    data,
    isLoading: isLoadingProducts,
    error,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts,
    {
      path: {
        accountNumber,
        addressIdentifier,
      },
    },
    [`/accounts/${accountNumber}/address/${addressIdentifier}/products`],
    session,
  );

  const deliveryAddressProductsData = unwrapData(data);

  const isLoading = isLoadingAddress || isLoadingProducts;

  const fetchingProductsError = error !== undefined && data === undefined;

  if (!isLoading && fetchingProductsError) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Stack gap={4}>
      <ProductCategory
        products={deliveryAddressProductsData?.activeProducts}
        title={fields.productOverview.activeSectionTitle.value}
        fields={productCardFields}
        isLoading={isLoading}
      />
      <ProductCategory
        products={deliveryAddressProductsData?.expiredProducts}
        title={fields.productOverview.historicalSectionTitle.value}
        fields={productCardFields}
      />
    </Stack>
  );
};

interface ProductCategoryProps {
  products: GetOverviewForAddressResponseItem[] | undefined | null;
  title: string;
  fields: ProductCardFields;
  isLoading?: boolean;
}

const ProductCategory: FC<ProductCategoryProps> = ({ title, products, fields, isLoading }) => {
  if (isLoading) {
    return (
      <>
        <LoadingText height={24} />
        <Box paddingY={2}>
          <RoundedSkeleton width="100%" height={140} />
          <RoundedSkeleton width="100%" height={140} />
        </Box>
      </>
    );
  }

  if (products && products.length) {
    // Show elec above gas
    products.sort((a, b) => {
      const order = ['Electricity', 'Gas'];
      const indexA = order.indexOf(a.type ?? '') === -1 ? Infinity : order.indexOf(a.type ?? '');
      const indexB = order.indexOf(b.type ?? '') === -1 ? Infinity : order.indexOf(b.type ?? '');
      return indexA - indexB;
    });

    return (
      <>
        <Heading as="h2" size={'2XS'}>
          {title}
        </Heading>
        {products.map(product => {
          return <ProductCard key={product.contractId} {...product} isInOverview={true} fields={fields} />;
        })}
      </>
    );
  }
};

export default ProductOverview;
