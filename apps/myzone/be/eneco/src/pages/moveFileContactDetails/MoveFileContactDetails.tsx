import RichText from '@components/RichText/RichText';
import { MoveAddressDto, SelectedCorrespondenceAddressDto } from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { MoveFileContactDetailsRendering } from '@sitecore/types/manual/MoveFileContactDetails';
import { Bucket, Stack, TextLink } from '@sparky';

import DataErrorNotification from '../../components/DataErrorNotification';
import ReadFormField from '../../components/ReadFormField';
import useBusLabel from '../../hooks/useBusLabel';
import { addressSearchParam } from '../../utils/addSearchParams';
import { useMove } from '../move/hooks/useMove';
import { addressFormatter } from '../move/overview/address-formatter';

const MoveFileContactDetails = () => {
  const { fields } = useContent<MoveFileContactDetailsRendering>();
  const { move, path, isLoading, errorMove } = useMove();
  const { isCurrentAccountReader } = useSelfServiceAccount();
  const busLabel = useBusLabel();
  const addressMap = new Map<SelectedCorrespondenceAddressDto | undefined, MoveAddressDto | undefined>([
    ['New', move?.newAddress?.newAddress],
    ['Old', move?.contactDetails?.contactCorrespondenceAddress],
    ['Custom', move?.contactDetails?.customCorrespondenceAddress],
  ]);
  const selectedAddressKey = move?.contactDetails?.selectedCorrespondenceAddress;
  const correspondenceAddress = addressMap.get(selectedAddressKey);

  if (!isLoading && errorMove !== undefined && move === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Stack gap={4}>
      <Bucket title={fields.knownInfo.title.value}>
        <Bucket.Content>
          <ReadFormField label={fields.knownInfo.lastNameLabel.value} value={move?.contractDetails?.lastName} />
          <ReadFormField label={fields.knownInfo.firstNameLabel.value} value={move?.contractDetails?.firstName} />
        </Bucket.Content>
      </Bucket>
      <Bucket title={fields.changeInfo.title.value}>
        <Bucket.Content>
          <RichText html={fields.changeInfo.description.value} />
          <TextLink href={fields.changeInfo.link.value.href} emphasis="high">
            {fields.changeInfo.link.value.text}
          </TextLink>
        </Bucket.Content>
      </Bucket>
      <Bucket title={fields.billingAddress.title.value}>
        <Bucket.Content>
          <ReadFormField
            label={fields.billingAddress.addressLabel.value}
            value={addressFormatter(correspondenceAddress, busLabel)}
          />
          {!isCurrentAccountReader && (
            <TextLink
              href={fields.billingAddress.link.value.href + addressSearchParam(path.addressIdentifier)}
              emphasis="high">
              {fields.billingAddress.link.value.text}
            </TextLink>
          )}
        </Bucket.Content>
      </Bucket>
    </Stack>
  );
};

export default MoveFileContactDetails;
