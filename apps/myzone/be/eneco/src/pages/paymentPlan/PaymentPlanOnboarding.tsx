import { FC, Fragment } from 'react';

import RichText from '@components/RichText/RichText';
import {
  PaymentPlanDto,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlans,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibility,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be/useSelfServiceAccount';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { PaymentPlanOnboardingRendering } from '@sitecore/types/PaymentPlanOnboarding';
import { Badge, Bleed, Box, Bucket, Button, ButtonLink, Card, Divider, Heading, Stack, Text } from '@sparky';
import { Tariffs } from '@sparky/illustrations';

import {
  BILLING_ACCOUNT_NUMBER_PARAM,
  ONBOARDING_PAYMENT_PLAN_STEP,
  PAYMENT_PLAN_PREVIOUS_STEP_PARAM,
} from './utils/constants';
import DataErrorNotification from '../../components/DataErrorNotification';
import { LoadingListSpinner } from '../../components/Loading/LoadingListSpinner';
import { LoadingText } from '../../components/Loading/LoadingText';

const PaymentPlanOnboarding: FC = () => {
  const { selectedAccount } = useSelfServiceAccount();
  const { data: session } = useSession();

  const {
    data: paymentPlanData,
    isLoading,
    error,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlans,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/payment-plans`],
    session,
  );

  const data = unwrapData(paymentPlanData);

  if (isLoading) {
    return <LoadingListSpinner />;
  }

  if (!isLoading && error !== undefined && data === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  if (data?.paymentPlans) {
    return <PaymentPlanOverview paymentPlan={data?.paymentPlans} />;
  }

  return <PaymentPlanEligibility />;
};

type PaymentPlan = PaymentPlanDto[];

const PaymentPlanOverview: FC<{ paymentPlan: PaymentPlan }> = ({ paymentPlan }) => {
  const { fields } = useContent<PaymentPlanOnboardingRendering>();
  const { format, date, currency } = useFormatter();

  return (
    <Stack gap={6}>
      {paymentPlan.map((plan, index) => {
        const unpaidText =
          plan.invoices?.[0].paymentMethod === 'DirectDebit'
            ? (fields.data.unpaidDirectDebitText?.value ?? 'Wordt betaald door je bank [Not in Sitecore]')
            : fields.data.unpaidText.value;
        return (
          <Fragment key={plan.invoices?.[0].invoiceId}>
            <Heading as="h2" size="S">
              {fields.data.paymentPlanTitle.value} {paymentPlan.length > 1 && `${index + 1}`}
            </Heading>
            {fields.data.paymentPlanDescription.value && (
              <div>
                <RichText
                  html={format(fields.data.paymentPlanDescription.value, {
                    amount: currency.euro(plan.openAmount || 0),
                    slices: plan.amountOfSlices || 0,
                  })}
                />
              </div>
            )}
            <Card overflow={'hidden'} elevation="S">
              {plan.slices?.map((slice, index) => (
                <Fragment key={slice.start}>
                  <Box paddingX="6" paddingY="4">
                    <Stack gap="4" direction="row" alignY="center">
                      {/* eslint-disable-next-line dxp-rules/no-inline-css */}
                      <div style={{ alignSelf: 'flex-start' }}>
                        <Tariffs size={'small'} />
                      </div>
                      <Stack gap={1}>
                        <Heading as="h3" size="3XS">
                          {fields.data.paymentPlanSliceTitle.value} {index + 1}
                        </Heading>
                        <Text color="textLowEmphasis" size="BodyS">
                          {slice.paid
                            ? fields.data.paidText.value
                            : format(unpaidText, {
                                date: date.medium(slice.end ?? new Date()),
                              })}
                        </Text>
                        <Bleed bottom="1">
                          <Text weight={'bold'}>{currency.euro(slice.amount || 0)}</Text>
                        </Bleed>
                      </Stack>
                      <Stack.Item grow />
                      {plan.status === 'Activated' && (
                        <PaymentStatusBadge
                          isFirstUnpaid={plan.slices?.findIndex(s => !s.paid) === index}
                          isPaid={slice.paid ?? false}
                        />
                      )}
                    </Stack>
                  </Box>
                  {index < (plan.slices?.length ?? 0) - 1 && <Divider />}
                </Fragment>
              ))}
            </Card>
          </Fragment>
        );
      })}
    </Stack>
  );
};

const PaymentStatusBadge: FC<{ isPaid: boolean; isFirstUnpaid: boolean }> = ({ isPaid, isFirstUnpaid }) => {
  const { fields } = useContent<PaymentPlanOnboardingRendering>();

  if (isFirstUnpaid) {
    // TODO: get url from Worldline
    return <Button size={'compact'}>{fields.data.payNowButtonText.value}</Button>;
  }

  return (
    <Badge color={isPaid ? 'success' : 'warning'}>
      {isPaid ? fields.data.paidText.value : fields.data.unpaidText.value}
    </Badge>
  );
};

const PaymentPlanEligibility: FC = () => {
  const { fields } = useContent<PaymentPlanOnboardingRendering>();
  const { selectedAccount, isCurrentAccountReader } = useSelfServiceAccount();
  const { data: session } = useSession();
  const { data: eligibilityData, isLoading } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibility,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
      query: {
        includeConditions: true,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/payment-plans/eligibility`],
    session,
  );

  const data = unwrapData(eligibilityData);

  const isEligible = data?.isEligible;
  const hasMultipleBillingAccounts = data?.billingAccountNumbers && data?.billingAccountNumbers?.length > 1;

  if (isLoading) {
    return (
      <Bucket title={<LoadingText />}>
        <Bucket.Content>
          <LoadingText width={200} />
          <LoadingText />
        </Bucket.Content>
      </Bucket>
    );
  }

  return (
    <Bucket title={isEligible ? fields.data.availableTitle.value : fields.data.unavailableTitle.value}>
      <Bucket.Content>
        <RichText
          html={
            hasMultipleBillingAccounts
              ? fields.data.multipleBillingDescription.value
              : isEligible
                ? fields.data.singleBillingDescription.value
                : fields.data.unavailableDescription.value
          }
        />
      </Bucket.Content>
      {isEligible && !hasMultipleBillingAccounts && !isCurrentAccountReader && (
        <Bucket.Footer>
          <Bucket.Actions>
            <ButtonLink
              href={`${fields.data.buttonLink.value.href}?${BILLING_ACCOUNT_NUMBER_PARAM}=${data?.billingAccountNumbers?.[0]}&${PAYMENT_PLAN_PREVIOUS_STEP_PARAM}=${ONBOARDING_PAYMENT_PLAN_STEP}`}>
              {fields.data.buttonLink.value.text}
            </ButtonLink>
          </Bucket.Actions>
        </Bucket.Footer>
      )}
    </Bucket>
  );
};

export default PaymentPlanOnboarding;
