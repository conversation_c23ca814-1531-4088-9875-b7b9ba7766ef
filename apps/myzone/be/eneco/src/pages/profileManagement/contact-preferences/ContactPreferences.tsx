import { useEffect, useRef, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import {
  EnergyMonitorFrequency,
  GetAccountContactPreferencesResponse,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferences,
  InvoiceContactPreference,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferences,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { unwrapValidationError } from '@dc-be/utils/unwrapError';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { ContactPreferencesRendering } from '@sitecore/types/ContactPreferences';
import { AlertDialog, Bucket, Button, Form, RadioButton, RadioGroup, Stack, Text } from '@sparky';
import { useTracking } from '@tracking';

import DataErrorNotification from '../../../components/DataErrorNotification';
import { LoadingText } from '../../../components/Loading/LoadingText';
import { useSetNotificationAndScrollToTop } from '../../../hooks/useSetNotificationAndScrollToTop';
import { inlineStrongReplacement } from '../../../utils/richTextReplacements';

enum InvoicePreferences {
  POST = 'post',
  EMAIL = 'email',
}

const ContactPreferences = () => {
  const { fields } = useContent<ContactPreferencesRendering>();
  const { format } = useFormatter();
  const { selectedAccount, isProspect, isCurrentAccountReader } = useSelfServiceAccount();
  const { data: session } = useSession();
  const setNotification = useSetNotificationAndScrollToTop();
  const { mutate } = useSWRConfig();
  const { trackEventBE } = useTracking();
  const [isLoadingSubmit1, setIsLoadingSubmit1] = useState<boolean>(false);
  const [isLoadingSubmit2, setIsLoadingSubmit2] = useState<boolean>(false);
  const [isLoadingSubmit3, setIsLoadingSubmit3] = useState<boolean>(false);

  const [isOpenDiscardPromoDialog, setIsOpenDiscardPromoDialog] = useState<boolean>(false);
  const [communicationPreferenceValue, setCommunicationPreferenceValue] = useState<string | undefined>();

  // Button refs
  const button1Ref = useRef<HTMLButtonElement>(null);
  const button2Ref = useRef<HTMLButtonElement>(null);
  const button3Ref = useRef<HTMLButtonElement>(null);

  const ContactPreferencesSchema = yup.object({
    communicationPreference: yup
      .string()
      .required(fields.contractContactPreferencesCommunicationChannelFormField.value.requiredMessage),
    receiveAdvanceInvoices: yup
      .string()
      .required(fields.contractContactPreferencesAdvanceChargeFormField.value.requiredMessage),
    receiveUpdatesCommunication: yup
      .string()
      .required(fields.notificationsContactPreferencesNewsletterFormField.value.requiredMessage),
    receiveMarketingCommunication: yup
      .string()
      .required(fields.contractContactPreferencesCommunicationChannelFormField.value.requiredMessage),
    meterReadingReminderFrequency: yup
      .string()
      .required(fields.remindersContactPreferencesFrequencyOptionsFormField.value.requiredMessage),
  });

  type FormValues = yup.InferType<typeof ContactPreferencesSchema>;
  const resolver = yupResolver(ContactPreferencesSchema);

  const { data, error: errorAccounts } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/contact/details`],
    session,
  );

  const accountDetails = unwrapData(data);

  const {
    data: contactPreferenceData,
    isLoading: isLoadingContactPreference,
    error: errorContactPreference,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferences,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/contact/preferences`],
    session,
  );

  const accountPreferences = unwrapData(contactPreferenceData);
  const {
    formState: { errors },
    handleSubmit,
    control,
    setValue,
    getValues,
  } = useForm<FormValues>({
    resolver,
    defaultValues: {
      communicationPreference:
        fields.contractContactPreferencesCommunicationChannelFormField.value.options[0].value?.toLowerCase(),
      receiveAdvanceInvoices:
        fields.contractContactPreferencesAdvanceChargeFormField.value.options[0].value?.toLowerCase(),
      receiveUpdatesCommunication:
        fields.notificationsContactPreferencesNewsletter.value.options[0].value?.toLowerCase(),
      receiveMarketingCommunication:
        fields.notificationsContactPreferencesNewsletterFormField.value.options[0].value?.toLowerCase(),
      meterReadingReminderFrequency:
        fields.remindersContactPreferencesFrequencyOptionsFormField.value.options[0].value?.toLowerCase(),
    },
  });

  const submitForm: SubmitHandler<FormValues> = async (
    {
      communicationPreference,
      meterReadingReminderFrequency,
      receiveAdvanceInvoices,
      receiveMarketingCommunication,
      receiveUpdatesCommunication,
    },
    event,
  ) => {
    const advancePayments = receiveAdvanceInvoices === 'yes';
    const invoicePreferences: InvoiceContactPreference =
      communicationPreference === InvoicePreferences.EMAIL ? 'Email' : 'Post';
    const enecoNews = receiveUpdatesCommunication === 'yes';
    const promotions = receiveMarketingCommunication === 'yes';
    let energyMonitorFrequency: EnergyMonitorFrequency;
    switch (meterReadingReminderFrequency) {
      case 'never':
        energyMonitorFrequency = 'Never';
        break;
      case 'weekly':
        energyMonitorFrequency = 'Weekly';
        break;
      case 'biweekly':
        energyMonitorFrequency = 'Biweekly';
        break;
      case 'monthly':
        energyMonitorFrequency = 'Monthly';
        break;
      default:
        throw new Error(`Invalid value: ${receiveMarketingCommunication}`);
    }

    const nativeEvent = event?.nativeEvent as SubmitEvent;
    const submitter = nativeEvent.submitter;
    if (submitter === button1Ref.current) {
      setIsLoadingSubmit1(true);
    } else if (submitter === button2Ref.current) {
      setIsLoadingSubmit2(true);
    } else if (submitter === button3Ref.current) {
      setIsLoadingSubmit3(true);
    }

    try {
      const { response, error } = await putEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferences({
        path: { accountNumber: selectedAccount?.crmAccountNumber },
        body: { advancePayments, invoicePreferences, energyMonitorFrequency, promotions, enecoNews },
      });
      if (response.ok) {
        mutate(`/accounts/${selectedAccount.crmAccountNumber}/contact/preferences`);
        setNotification({
          notificationOptions: {
            variant: 'success',
            title: fields.editSuccessNotification?.value.title,
            text: fields.editSuccessNotification?.value.content,
            headingLevel: 'h3',
          },
          shouldScroll: true,
        });
        trackEventBE('optin_data_submit', {
          status: 'success',
          data: { advancePayments, invoicePreferences, energyMonitorFrequency, promotions, enecoNews },
        });
      } else {
        const validationErrors = unwrapValidationError(error);
        if (validationErrors.length > 0) {
          setNotification({
            notificationOptions: {
              variant: 'error',
              title: fields.editErrorNotification?.value.title,
              text: fields.editErrorNotification?.value.content,
              headingLevel: 'h3',
            },
            shouldScroll: true,
          });
          trackEventBE('optin_data_submit', {
            status: 'failed',
            message: 'unexpected error occurred',
            data: { advancePayments, invoicePreferences, energyMonitorFrequency, promotions, enecoNews },
          });
        }
      }
    } catch {
      setNotification({
        notificationOptions: {
          variant: 'error',
          title: fields.editErrorNotification?.value.title,
          text: fields.editErrorNotification?.value.content,
          headingLevel: 'h3',
        },
        shouldScroll: true,
      });
      trackEventBE('optin_data_submit', {
        status: 'failed',
        message: 'unexpected error occurred',
        data: { advancePayments, invoicePreferences, energyMonitorFrequency, promotions, enecoNews },
      });
    } finally {
      setIsLoadingSubmit1(false);
      setIsLoadingSubmit2(false);
      setIsLoadingSubmit3(false);
    }
  };

  useEffect(() => {
    if (accountPreferences) {
      const setFormValues = (preferences: GetAccountContactPreferencesResponse | undefined) => {
        setValue(
          'communicationPreference',
          preferences?.invoicePreferences?.toLowerCase() === InvoicePreferences.EMAIL
            ? InvoicePreferences.EMAIL
            : InvoicePreferences.POST,
        );
        setValue('receiveAdvanceInvoices', preferences?.advancePayments ? 'yes' : 'no');
        setValue('receiveUpdatesCommunication', preferences?.enecoNews ? 'yes' : 'no');
        setValue('receiveMarketingCommunication', preferences?.promotions ? 'yes' : 'no');
        setValue('meterReadingReminderFrequency', preferences?.energyMonitorFrequency?.toLowerCase() ?? 'never');
      };

      setFormValues(accountPreferences);
    }
  }, [accountPreferences, setValue]);

  if (isProspect) {
    return null;
  }

  if (
    !isLoadingContactPreference &&
    ((errorAccounts !== undefined && data === undefined) ||
      (errorContactPreference !== undefined && contactPreferenceData === undefined))
  ) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Form onSubmit={handleSubmit(submitForm)}>
      <Stack gap={10}>
        <Bucket title={fields.contractContactPreferencesTitle.value}>
          <Bucket.Content>
            <Stack gap={5}>
              <div>
                <RichText
                  html={format(fields.contractContactPreferencesContent.value, {
                    email: accountDetails?.email ?? '',
                  })}
                  replacements={inlineStrongReplacement}
                />
              </div>
            </Stack>

            <Stack.Item>
              <label htmlFor="communicationPreference">
                <Text weight={'bold'} size={'BodyM'}>
                  {fields.contractContactPreferencesCommunicationChannelFormField.value.label}
                </Text>
              </label>
            </Stack.Item>
            <Controller
              name="communicationPreference"
              control={control}
              render={({ field: { onChange } }) => (
                <>
                  <AlertDialog
                    title={fields.discardDialog?.value?.title ?? 'promo [not in sitecore]'}
                    confirmText={fields.discardDialog?.value?.submitButtonText ?? 'confirm [not in sitecore]'}
                    denyText={fields.discardDialog?.value?.cancelButtonText ?? 'cancel [not in sitecore]'}
                    description={
                      <RichText html={fields.discardDialog?.value?.content ?? 'content [not in sitecore]'} />
                    }
                    onConfirm={() => {
                      onChange(communicationPreferenceValue);
                    }}
                    isOpen={isOpenDiscardPromoDialog}
                    setOpen={value => {
                      setIsOpenDiscardPromoDialog(value);
                    }}
                    onDeny={() => {}}></AlertDialog>
                  {isLoadingContactPreference ? (
                    <Stack.Item>
                      <LoadingText height={24} width={150} />
                      <LoadingText height={24} width={150} />
                    </Stack.Item>
                  ) : (
                    <RadioGroup
                      error={errors.communicationPreference?.message}
                      name="communicationPreference"
                      aria-labelledby="communicationPreference"
                      direction="column"
                      value={getValues('communicationPreference')}
                      onValueChange={value => {
                        if (
                          (accountPreferences as { hasEBillingPromotion?: boolean })?.hasEBillingPromotion &&
                          value === InvoicePreferences.POST
                        ) {
                          setCommunicationPreferenceValue(value);
                          setIsOpenDiscardPromoDialog(true);
                        } else {
                          onChange(value);
                        }
                      }}>
                      <RadioButton
                        value={fields.contractContactPreferencesCommunicationChannelFormField.value.options[0].value?.toLowerCase()}>
                        {fields.contractContactPreferencesCommunicationChannelFormField.value.options[0].label}
                      </RadioButton>
                      <RadioButton
                        value={fields.contractContactPreferencesCommunicationChannelFormField.value.options[1].value?.toLowerCase()}>
                        {fields.contractContactPreferencesCommunicationChannelFormField.value.options[1].label}
                      </RadioButton>
                    </RadioGroup>
                  )}
                </>
              )}
            />

            <Stack.Item>
              <label htmlFor="receiveAdvanceInvoices">
                <Text weight={'bold'} size={'BodyM'}>
                  {fields.contractContactPreferencesAdvanceChargeFormField.value.label}
                </Text>
              </label>
            </Stack.Item>
            {isLoadingContactPreference ? (
              <Stack.Item>
                <LoadingText height={24} width={150} />
                <LoadingText height={24} width={150} />
              </Stack.Item>
            ) : (
              <Controller
                control={control}
                name="receiveAdvanceInvoices"
                render={({ field: { onChange } }) => (
                  <RadioGroup
                    name="receiveAdvanceInvoices"
                    aria-labelledby="receiveAdvanceInvoices"
                    direction="column"
                    value={getValues('receiveAdvanceInvoices')}
                    onValueChange={onChange}>
                    <RadioButton
                      value={fields.contractContactPreferencesAdvanceChargeFormField.value.options[0].value?.toLowerCase()}>
                      {fields.contractContactPreferencesAdvanceChargeFormField.value.options[0].label}
                    </RadioButton>
                    <RadioButton
                      value={fields.contractContactPreferencesAdvanceChargeFormField.value.options[1].value?.toLowerCase()}>
                      {fields.contractContactPreferencesAdvanceChargeFormField.value.options[1].label}
                    </RadioButton>
                  </RadioGroup>
                )}
              />
            )}
          </Bucket.Content>
          {!isCurrentAccountReader && (
            <Bucket.Footer>
              <Bucket.Actions>
                <Button type="submit" ref={button1Ref} isLoading={isLoadingSubmit1}>
                  {fields.contractContactPreferencesButtonSaveText.value}
                </Button>
              </Bucket.Actions>
            </Bucket.Footer>
          )}
        </Bucket>

        <Stack gap={10}>
          <Bucket title={fields.notificationsContactPreferencesTitle.value}>
            <Bucket.Content>
              <Stack.Item>
                <label htmlFor="receiveUpdatesCommunication">
                  <Text weight={'bold'} size={'BodyM'}>
                    {fields.notificationsContactPreferencesNewsletter.value.label}
                  </Text>
                </label>
              </Stack.Item>
              {isLoadingContactPreference ? (
                <Stack.Item>
                  <LoadingText height={24} width={150} />
                  <LoadingText height={24} width={150} />
                </Stack.Item>
              ) : (
                <Controller
                  control={control}
                  name="receiveUpdatesCommunication"
                  render={({ field: { onChange } }) => (
                    <RadioGroup
                      name="receiveUpdatesCommunication"
                      aria-labelledby="receiveUpdatesCommunication"
                      direction="column"
                      value={getValues('receiveUpdatesCommunication')}
                      onValueChange={onChange}>
                      <RadioButton
                        value={fields.notificationsContactPreferencesNewsletter.value.options[0].value?.toLowerCase()}>
                        {fields.notificationsContactPreferencesNewsletter.value.options[0].label}
                      </RadioButton>
                      <RadioButton
                        value={fields.notificationsContactPreferencesNewsletter.value.options[1].value?.toLowerCase()}>
                        {fields.notificationsContactPreferencesNewsletter.value.options[1].label}
                      </RadioButton>
                    </RadioGroup>
                  )}
                />
              )}

              <Stack.Item>
                <label htmlFor="receiveMarketingCommunication">
                  <Text weight={'bold'} size={'BodyM'}>
                    {fields.notificationsContactPreferencesNewsletterFormField.value.label}
                  </Text>
                </label>
              </Stack.Item>
              {isLoadingContactPreference ? (
                <Stack.Item>
                  <LoadingText height={24} width={150} />
                  <LoadingText height={24} width={150} />
                </Stack.Item>
              ) : (
                <Controller
                  control={control}
                  name="receiveMarketingCommunication"
                  render={({ field: { onChange } }) => (
                    <RadioGroup
                      name="receiveMarketingCommunication"
                      aria-labelledby="receiveMarketingCommunication"
                      direction="column"
                      value={getValues('receiveMarketingCommunication')}
                      onValueChange={onChange}>
                      <RadioButton
                        value={fields.notificationsContactPreferencesNewsletterFormField.value.options[0].value?.toLowerCase()}>
                        {fields.notificationsContactPreferencesNewsletterFormField.value.options[0].label}
                      </RadioButton>
                      <RadioButton
                        value={fields.notificationsContactPreferencesNewsletterFormField.value.options[1].value?.toLowerCase()}>
                        {fields.notificationsContactPreferencesNewsletterFormField.value.options[1].label}
                      </RadioButton>
                    </RadioGroup>
                  )}
                />
              )}
            </Bucket.Content>
            {!isCurrentAccountReader && (
              <Bucket.Footer>
                <Bucket.Actions>
                  <Button type="submit" ref={button2Ref} isLoading={isLoadingSubmit2}>
                    {fields.notificationsContactPreferencesButtonSaveText.value}
                  </Button>
                </Bucket.Actions>
              </Bucket.Footer>
            )}
          </Bucket>
        </Stack>

        <Stack gap={10}>
          <Bucket title={fields.remindersContactPreferencesTitle.value}>
            <Bucket.Content>
              <Stack.Item>
                <label htmlFor="meterReadingReminderFrequency">
                  <Text weight={'bold'} size={'BodyM'}>
                    {fields.remindersContactPreferencesFrequencyOptionsFormField.value.label}
                  </Text>
                </label>
              </Stack.Item>
              {isLoadingContactPreference ? (
                <Stack.Item>
                  <LoadingText height={24} width={150} />
                  <LoadingText height={24} width={150} />
                  <LoadingText height={24} width={150} />
                  <LoadingText height={24} width={150} />
                </Stack.Item>
              ) : (
                <Controller
                  control={control}
                  name="meterReadingReminderFrequency"
                  render={({ field: { onChange } }) => (
                    <RadioGroup
                      name="meterReadingReminderFrequency"
                      aria-labelledby="meterReadingReminderFrequency"
                      direction="column"
                      value={getValues('meterReadingReminderFrequency')}
                      onValueChange={onChange}>
                      <RadioButton
                        value={fields.remindersContactPreferencesFrequencyOptionsFormField.value.options[0].value?.toLowerCase()}>
                        {fields.remindersContactPreferencesFrequencyOptionsFormField.value.options[0].label}
                      </RadioButton>
                      <RadioButton
                        value={fields.remindersContactPreferencesFrequencyOptionsFormField.value.options[1].value?.toLowerCase()}>
                        {fields.remindersContactPreferencesFrequencyOptionsFormField.value.options[1].label}
                      </RadioButton>
                      <RadioButton
                        value={fields.remindersContactPreferencesFrequencyOptionsFormField.value.options[2].value?.toLowerCase()}>
                        {fields.remindersContactPreferencesFrequencyOptionsFormField.value.options[2].label}
                      </RadioButton>
                      <RadioButton
                        value={fields.remindersContactPreferencesFrequencyOptionsFormField.value.options[3].value?.toLowerCase()}>
                        {fields.remindersContactPreferencesFrequencyOptionsFormField.value.options[3].label}
                      </RadioButton>
                    </RadioGroup>
                  )}
                />
              )}
            </Bucket.Content>
            {!isCurrentAccountReader && (
              <Bucket.Footer>
                <Bucket.Actions>
                  <Button type="submit" ref={button3Ref} isLoading={isLoadingSubmit3}>
                    {fields.remindersContactPreferencesButtonSaveText.value}
                  </Button>
                </Bucket.Actions>
              </Bucket.Footer>
            )}
          </Bucket>
        </Stack>
      </Stack>
    </Form>
  );
};

export default ContactPreferences;
