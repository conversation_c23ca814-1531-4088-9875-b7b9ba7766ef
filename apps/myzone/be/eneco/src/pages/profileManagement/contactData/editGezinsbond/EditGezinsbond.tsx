import { useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useRouter } from 'next/router';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
// eslint-disable-next-line import/no-unresolved
import { EditGezinsbondRendering } from '@sitecore/types/EditGezinsBond';
import { AlertDialog, Box, Bucket, Button, Form, Stack, TextLink } from '@sparky';
import { useTracking } from '@tracking';

import { MembershipNumberInput } from './MembershipNumberInput';
import DataErrorNotification from '../../../../components/DataErrorNotification';
import { useRedirectAndNotifyBE } from '../../../../hooks/useRedirectAndNotifyBE';
import { useSetNotificationAndScrollToTop } from '../../../../hooks/useSetNotificationAndScrollToTop';

const EditGezinsbond = () => {
  const { push } = useRouter();
  const { fields } = useContent<EditGezinsbondRendering>();
  const redirectAndNotify = useRedirectAndNotifyBE();
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  const editGezinsbondSchema = yup.object({
    membershipNumber: yup
      .string()
      .required(fields.editGezinsbondMembershipNumberFormField.value.requiredMessage)
      .matches(/^\d{3}-\d{3}-\d{3}$/, fields.editGezinsbondMembershipNumberFormField.value.validationMessage),
  });
  type FormValues = yup.InferType<typeof editGezinsbondSchema>;
  const resolver = yupResolver(editGezinsbondSchema);
  const { formState, handleSubmit, setValue, control, watch } = useForm<FormValues>({
    resolver,
    defaultValues: { membershipNumber: '' },
  });
  const { selectedAccount, isCurrentAccountReader } = useSelfServiceAccount();
  const { data: session } = useSession();
  const setNotification = useSetNotificationAndScrollToTop();
  const { mutate } = useSWRConfig();
  const { trackEventBE } = useTracking();

  const { data, isLoading, error } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/contact/details`],
    session,
  );

  useEffect(() => {
    if (data?.data?.data?.gezinsBondNumber) {
      setValue('membershipNumber', data.data.data.gezinsBondNumber);
    }
  }, [data, setValue]);

  const membershipNumber = watch('membershipNumber');

  const submitForm: SubmitHandler<FormValues> = async ({ membershipNumber }) => {
    try {
      setIsLoadingSubmit(true);
      const accountByAccountNumberContactDetails = data?.data?.data;
      const { response } = await putEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails({
        path: {
          accountNumber: selectedAccount?.crmAccountNumber,
        },
        body: { ...accountByAccountNumberContactDetails, gezinsBondNumber: membershipNumber },
      });
      if (response.ok) {
        await mutate(`/accounts/${selectedAccount.crmAccountNumber}/contact/details`).then(() => {
          trackEventBE('gezinsbond_submit', { status: 'success' });
          redirectAndNotify({
            route: fields.editGezinsbondSaveButtonLink?.value?.href ?? '/my-eneco/profile-management/contact-details/',
            variant: 'success',
            title: fields.editGezinsbondSuccessNotification?.value.title,
            text: fields.editGezinsbondSuccessNotification?.value.content,
            headingLevel: 'h3',
          });
        });
      }
    } catch {
      setIsLoadingSubmit(false);
      setNotification({
        notificationOptions: {
          variant: 'error',
          title: fields.editGezinsbondErrorNotification?.value.title,
          text: fields.editGezinsbondErrorNotification?.value.content,
          headingLevel: 'h3',
        },
        shouldScroll: true,
      });
      trackEventBE('gezinsbond_submit', { status: 'failed', message: 'unexpected error occurred' });
    }
  };

  const submitClearMembershipNumber = async () => {
    try {
      const accountByAccountNumberContactDetails = data?.data?.data;
      const { response } = await putEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails({
        path: {
          accountNumber: selectedAccount?.crmAccountNumber,
        },
        body: { ...accountByAccountNumberContactDetails, gezinsBondNumber: undefined },
      });
      if (response.ok) {
        await mutate(`/accounts/${selectedAccount.crmAccountNumber}/contact/details`).then(() => {
          redirectAndNotify({
            route: fields.editGezinsbondSaveButtonLink?.value?.href ?? '/my-eneco/profile-management/contact-details/',
            variant: 'success',
            title: fields.editGezinsbondRemovalSuccessNotification?.value.title,
            text: fields.editGezinsbondSuccessNotification?.value.content,
            headingLevel: 'h3',
          });
        });
      }
    } catch {
      setNotification({
        notificationOptions: {
          variant: 'error',
          title: fields.editGezinsbondErrorNotification?.value.title,
          text: fields.editGezinsbondErrorNotification?.value.content,
          headingLevel: 'h3',
        },
        shouldScroll: true,
      });
    }
  };

  if (!isLoading && error !== undefined && data === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    !isCurrentAccountReader && (
      <Stack gap={10}>
        <Bucket title={fields.editGezinsbondTitle.value}>
          <Form onSubmit={handleSubmit(submitForm)}>
            <Bucket.Content>
              <Box>
                <RichText html={fields.editGezinsbondContent.value} />
              </Box>
              <Controller
                control={control}
                name="membershipNumber"
                render={({ field: { onChange, value, name } }) => (
                  <MembershipNumberInput
                    label={fields.editGezinsbondMembershipNumberFormField?.value?.label}
                    name={name}
                    placeholder="XXX-XXX-XXX"
                    error={formState?.errors?.membershipNumber?.message}
                    value={value}
                    onChange={onChange}
                  />
                )}
              />
              {membershipNumber?.length > 0 && (
                <AlertDialog
                  confirmText={fields.deleteDialogConfirmButtonText.value}
                  denyText={fields.deleteDialogCancelButtonText.value}
                  title={fields.deleteDialogTitle.value}
                  description={<RichText html={fields.deleteDialogContent.value} />}
                  trigger={
                    <TextLink emphasis="high" target={fields.editGezinsbondRemoveMembershipLink.value.target}>
                      {fields.editGezinsbondRemoveMembershipLink.value.text}
                    </TextLink>
                  }
                  onConfirm={submitClearMembershipNumber}
                  onDeny={() => {}}
                />
              )}
            </Bucket.Content>
            <Bucket.Footer>
              <Bucket.Actions>
                <Button type="submit" isLoading={isLoadingSubmit}>
                  {fields.editGezinsbondSaveButtonLink?.value?.text ?? 'Opslaan [not in sitecore]'}
                </Button>
                <Button action="secondary" onClick={() => push(fields.editGezinsbondCancelButtonLink.value.href)}>
                  {fields.editGezinsbondCancelButtonLink.value.text ?? 'Opslaan [not in sitecore]'}
                </Button>
              </Bucket.Actions>
            </Bucket.Footer>
          </Form>
        </Bucket>
      </Stack>
    )
  );
};

export default EditGezinsbond;
