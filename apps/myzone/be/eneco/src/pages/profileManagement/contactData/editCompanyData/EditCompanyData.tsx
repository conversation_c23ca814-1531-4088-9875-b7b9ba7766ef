import { useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useRouter } from 'next/router';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import { useRedirectAndNotify } from '@components-next/Notification/Notification';
import {
  CompanyFormatTypeDto,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetails,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { CompanyDataRendering } from '@sitecore/types/CompanyData';
import { Bucket, Button, Checkbox, Form, InputSelect, InputText } from '@sparky';
import { useTracking } from '@tracking';

import { CompanyNumberInput } from './CompanyNumberInput';
import DataErrorNotification from '../../../../components/DataErrorNotification';
import { useSetNotificationAndScrollToTop } from '../../../../hooks/useSetNotificationAndScrollToTop';

const EditCompanyData = () => {
  const { push } = useRouter();

  const { fields } = useContent<CompanyDataRendering>();

  const { selectedAccount, isCurrentAccountReader } = useSelfServiceAccount();
  const { data: session } = useSession();
  const { trackEventBE } = useTracking();
  const [toggleCompanyNumber, setToggleCompanyNumber] = useState(false);
  const setNotification = useSetNotificationAndScrollToTop();
  const redirectAndNotify = useRedirectAndNotify();
  const { mutate } = useSWRConfig();
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  const editCompanyDataSchema = yup.object({
    showCompanyNumber: yup.boolean(),
    name: yup
      .string()
      .required(fields.companyNameFormField?.value.requiredMessage)
      .max(100, fields.companyNameFormField?.value.validationMessage),
    formatType: yup.string<CompanyFormatTypeDto>().required(fields.companyTypeFormField?.value.requiredMessage),
    companyNumber: yup.string().when('showCompanyNumber', {
      is: false,
      then: schema =>
        schema
          .required(fields.companyNumberFormField?.value.requiredMessage ?? 'Dit is verplicht [not in sitecore]')
          .matches(
            /^\d{4}.\d{3}.\d{3}$/,
            fields.companyNumberFormField?.value.validationMessage ?? 'Niet het juiste patroon[not in sitecore]',
          ),
    }),
  });

  type FormValues = yup.InferType<typeof editCompanyDataSchema>;
  const resolver = yupResolver(editCompanyDataSchema);

  const {
    control,
    handleSubmit,
    register,
    getValues,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    resolver,
    defaultValues: { showCompanyNumber: false },
  });

  const { data, isLoading, error } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/contact/details`],
    session,
  );

  const contactData = unwrapData(data);

  useEffect(() => {
    if (!contactData?.companyData) {
      setValue('showCompanyNumber', false);
      setToggleCompanyNumber(true);
      setValue('companyNumber', contactData?.companyData?.companyNumber ?? '');
    } else {
      setValue('showCompanyNumber', !contactData?.companyData?.companyNumber);
      setToggleCompanyNumber(!!contactData?.companyData?.companyNumber);
      setValue('companyNumber', contactData?.companyData?.companyNumber ?? '');
    }
  }, [contactData, setValue]);

  const toggleShowCompanyNumber = () => {
    setToggleCompanyNumber(getValues('showCompanyNumber') ?? true);
  };
  if (contactData?.customerType !== 'SOHO') {
    return null;
  }

  const submitForm: SubmitHandler<FormValues> = async values => {
    try {
      setIsLoadingSubmit(true);
      const { response } = await putEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetails({
        path: {
          accountNumber: selectedAccount?.crmAccountNumber,
        },
        body: {
          name: values.name,
          formatType: values.formatType,
          vatType: getValues('showCompanyNumber') ? 'CompanyNumberRequested' : 'VatRequired',
          companyNumber: values.companyNumber || '',
        },
      });
      if (response.ok) {
        trackEventBE('company_data_submit', { status: 'success' });
        mutate(`/accounts/${selectedAccount.crmAccountNumber}/company/details`);
        redirectAndNotify({
          route: fields.actionLinksSaveCompanyDataLink.value.href,
          title: fields.successNotification?.value.title,
          text: fields.successNotification?.value.content ?? 'Je bedrijf is aangepast [not in sitecore]',
          variant: 'success',
        });
      }
    } catch {
      setIsLoadingSubmit(false);
      setNotification({
        notificationOptions: {
          variant: 'error',
          title: fields.failureNotification?.value.title,
          text: fields.failureNotification?.value.content,
          headingLevel: 'h3',
        },
        shouldScroll: true,
      });
      trackEventBE('company_data_submit', { status: 'failed', message: 'unexpected error occurred' });
    }
  };
  if (isCurrentAccountReader) {
    return;
  }

  if (!isLoading && error !== undefined && data === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Bucket title={fields.title.value}>
      <Form onSubmit={handleSubmit(submitForm)}>
        <Bucket.Content>
          <InputText
            label={fields.companyNameFormField?.value.label || '[not in sitecore]'}
            {...register('name')}
            error={errors.name?.message}
            placeholder={fields.companyNameFormField?.value?.placeholder}
            defaultValue={contactData?.companyData?.name || '[not in sitecore]'}
          />
          <InputSelect
            label={fields.companyTypeFormField?.value.label}
            defaultValue={contactData?.companyData?.formatType || ''}
            {...register('formatType')}
            options={fields.companyTypeFormField?.value.options}
            placeholder={fields.companyTypeFormField?.value.placeholder || 'Maak uw keuze [not in sitecore]'}
            isOptional={false}
          />
          {toggleCompanyNumber && (
            <Controller
              control={control}
              name="companyNumber"
              render={({ field: { onChange, value, name } }) => (
                <CompanyNumberInput
                  label={fields.companyNumberFormField?.value.label}
                  name={name}
                  placeholder="XXXX.XXX.XXX"
                  error={errors.companyNumber?.message}
                  value={contactData?.companyData?.companyNumber || ''}
                  onChange={onChange}
                />
              )}
            />
          )}
          <Checkbox
            {...register('showCompanyNumber')}
            label={fields.companyNumberNotKnownYetText?.value || 'Ondernemingsnummer in aanvraag [not in sitecore]'}
            onChange={toggleShowCompanyNumber}
            defaultChecked={contactData?.companyData?.vatType == 'CompanyNumberRequested'}
          />
          <Bucket.Actions>
            <Button type="submit" isLoading={isLoadingSubmit}>
              {fields.actionLinksSaveCompanyDataLink?.value.text}
            </Button>
            <Button onClick={() => push(fields.actionLinksCancelCompanyDataLink.value.href)} action={'secondary'}>
              {fields.actionLinksCancelCompanyDataLink?.value.text}
            </Button>
          </Bucket.Actions>
        </Bucket.Content>
      </Form>
    </Bucket>
  );
};

export default EditCompanyData;
