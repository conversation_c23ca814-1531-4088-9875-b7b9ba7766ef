/* eslint-disable dxp-rules/no-custom-styling */
import { PropsWithChildren } from 'react';

import { useApplication } from '@common/application';
import logger from '@common/log';
import RichText from '@components/RichText/RichText';
import {
  GenderDto,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddresses,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { ContactDataRendering } from '@sitecore/types/ContactData';
import { Accordion, Bucket, NotificationBox, Text, TextLink } from '@sparky';
import { styled } from '@sparky/stitches';

import DataErrorNotification from '../../../components/DataErrorNotification';
import { LoadingListSpinner } from '../../../components/Loading/LoadingListSpinner';
import ReadFormField from '../../../components/ReadFormField';
import useBusLabel from '../../../hooks/useBusLabel';
import { COUNTRIES_MAP } from '../../../utils/countries';
import { LANGUAGES_MAP } from '../../../utils/language';

const ContactData = () => {
  const { fields } = useContent<ContactDataRendering>();
  const { selectedAccount, isProspect, isCurrentAccountReader } = useSelfServiceAccount();
  const { data: session } = useSession();
  const { date } = useFormatter();
  const { language } = useApplication();
  const busLabel = useBusLabel();

  const { data, isLoading, error } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/contact/details`],
    session,
  );

  const contactData = data?.data?.data;

  const genderMap: { [key in GenderDto]: string } = {
    Male: fields.genderMaleText.value,
    Female: fields.genderFemaleText.value,
    Unspecified: '',
  };

  const { contactAddress } = contactData || {};
  let birthDate = contactData?.dateOfBirth;
  try {
    if (birthDate) birthDate = date.short(birthDate);
  } catch {
    logger.error('EUSxiq', `Unable to parse birthDate of account ${contactData?.accountNumber}`);
  }

  if (isProspect) {
    return null;
  }

  if (!isLoading && error !== undefined && data === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <>
      <Bucket title={fields.title.value}>
        <Bucket.Content>
          <ReadFormField
            label={fields.languageLabel.value}
            value={contactData?.language ? LANGUAGES_MAP[contactData.language][language] : undefined}
            isLoading={isLoading}
          />
          <ReadFormField
            label={fields.nameLabel.value}
            value={
              contactData?.gender
                ? `${genderMap[contactData.gender] || ''} ${contactData?.firstName} ${contactData?.lastName}`
                : `${contactData?.firstName || ''} ${contactData?.lastName || ''}`
            }
            isLoading={isLoading}
          />
          <ReadFormField label={fields.birthDateLabel.value} value={birthDate} isLoading={isLoading} />
          <ReadFormField
            label={fields.phoneNumberLabel.value}
            value={contactData?.telephoneNumber}
            isLoading={isLoading}
          />
          <ReadFormField
            label={fields.mobilePhoneNumberLabel.value}
            value={contactData?.mobileNumber}
            isLoading={isLoading}
          />
          <ReadFormField label={fields.emailAddressLabel.value} value={contactData?.email} isLoading={isLoading} />
          <ReadFormField
            label={fields.addressLabel.value}
            value={
              contactAddress
                ? `${contactAddress.street} ${contactAddress.houseNumber} ${
                    contactAddress.bus ? `${busLabel} ${contactAddress.bus},` : ''
                  } ${contactAddress.zipCode} ${contactAddress.municipality}, ${contactAddress.country ? COUNTRIES_MAP.find(c => c.value === contactAddress.country)?.label[language] : ''}`
                : null
            }
            isLoading={isLoading}
          />
          <NotificationBox
            isAlert={false}
            title={fields.moveHelpNotification?.value.title}
            text={<RichText html={fields.moveHelpNotification?.value.content} />}
          />
          {!isCurrentAccountReader && (
            <Bucket.Actions>
              <TextLink
                href={fields.editContactDataLink.value.href}
                target={fields.editContactDataLink.value.target}
                emphasis="high">
                {fields.editContactDataLink.value.text}
              </TextLink>
            </Bucket.Actions>
          )}
        </Bucket.Content>
      </Bucket>
      {isLoading && <LoadingListSpinner />}
      {contactData?.customerType === 'Residential' && (
        <>
          <Bucket title={fields.familyBondTitle.value}>
            <Bucket.Content>
              <ReadFormField label={fields.membershipLabel.value} value={contactData?.gezinsBondNumber}></ReadFormField>
              {!isCurrentAccountReader && (
                <Bucket.Actions>
                  <TextLink
                    href={fields.editFamilyBondLink.value.href}
                    target={fields.editFamilyBondLink.value.target}
                    emphasis="high">
                    {fields.editFamilyBondLink.value.text}
                  </TextLink>
                </Bucket.Actions>
              )}
            </Bucket.Content>
          </Bucket>
          <SwitchToB2CExpandable />
        </>
      )}
      {contactData?.customerType === 'SOHO' && (
        <Bucket title={fields.companyDataTitle.value}>
          <Bucket.Content>
            <TextWrapperUseMultipleLines>
              <ReadFormField label={fields.companyDataCompanyNameLabel.value} value={contactData.companyData?.name} />
            </TextWrapperUseMultipleLines>

            <ReadFormField
              label={fields.companyDataCompanyTypeLabel.value}
              value={contactData.companyData?.formatType}
              isLoading={isLoading}
            />
            {contactData.companyData?.vatType === 'CompanyNumberRequested' && (
              <ReadFormField
                label={fields.companyDataCompanyNumberLabel.value}
                value={fields.companyDataNumberRequestLabel?.value}
                isLoading={isLoading}
              />
            )}
            {contactData.companyData?.vatType !== 'CompanyNumberRequested' && (
              <ReadFormField
                label={fields.companyDataCompanyNumberLabel.value}
                value={contactData.companyData?.companyNumber}
                isLoading={isLoading}
              />
            )}
            {!isCurrentAccountReader && (
              <Bucket.Actions>
                <TextLink
                  emphasis="high"
                  href={fields.editCompanyDataLink.value.href}
                  target={fields.editCompanyDataLink.value.target}>
                  {fields.editCompanyDataLink.value.text}
                </TextLink>
              </Bucket.Actions>
            )}
          </Bucket.Content>
        </Bucket>
      )}
    </>
  );
};

const SwitchToB2CExpandable = () => {
  const { selectedAccount } = useSelfServiceAccount();
  const { data: session } = useSession();
  const { fields } = useContent<ContactDataRendering>();

  const { data, isLoading } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddresses,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses`],
    session,
  );

  const contractData = unwrapData(data);
  const hasContract = !!contractData?.addresses && contractData.addresses.length > 0;

  if (isLoading) {
    return <LoadingListSpinner />;
  }

  if (!hasContract) {
    return null;
  }

  return (
    <Accordion>
      <Accordion.Item heading={fields.changeCustomerTypeTitle.value}>
        <Text size={'BodyM'}>
          <RichText
            html={fields.changeCustomerTypeContent.value}
            replacements={{
              strong: (props: PropsWithChildren) => (
                <Text weight={'bold'} display={'inline'} whiteSpace={'nowrap'}>
                  {props.children}
                </Text>
              ),
            }}
          />
        </Text>
      </Accordion.Item>
    </Accordion>
  );
};
const TextWrapperUseMultipleLines = styled('div', {
  whiteSpace: 'normal',
  overflow: 'hidden',

  '& span': {
    display: 'inline',
    wordWrap: 'break-word',
    wordBreak: 'break-word',
  },
});
export default ContactData;
