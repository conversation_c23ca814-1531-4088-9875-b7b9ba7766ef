import { useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import { useApplication } from '@common/application';
import RichText from '@components/RichText/RichText';
import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethods,
  PaymentMethodByBillingAccountResponse,
  PaymentMethodDto,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethods,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { unwrapValidationError } from '@dc-be/utils/unwrapError';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { MyEnecoUpdateInvoicePaymentMethodRendering } from '@sitecore/types/MyEnecoUpdateInvoicePaymentMethod';
import {
  AlertDialog,
  Bucket,
  Button,
  Checkbox,
  Form,
  NotificationBox,
  RadioButton,
  RadioGroup,
  Stack,
  Text,
} from '@sparky';

import DataErrorNotification from '../../../components/DataErrorNotification';
import IbanNumberInput, { isValidFormattedIban, normalizeIban } from '../../../components/IbanNumberInput';
import { LoadingListSpinner } from '../../../components/Loading/LoadingListSpinner';
import { useRedirectAndNotifyBE } from '../../../hooks/useRedirectAndNotifyBE';

function UpdateInvoicePaymentMethod() {
  const { fields } = useContent<MyEnecoUpdateInvoicePaymentMethodRendering>();

  const { isCurrentAccountReader, selectedAccount } = useSelfServiceAccount();
  const { push } = useRouter();
  const { mutate } = useSWRConfig();
  const { data: session } = useSession();
  const redirectAndNotify = useRedirectAndNotifyBE();
  const { searchParams } = useApplication();
  const addressIdentifier = searchParams.get('address') || '';
  const [showPromotionWarning, setShowPromotionWarning] = useState(false);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  const {
    data: paymentMethodsData,
    isLoading,
    error,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethods,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/payment-methods`],
    session,
  );

  const data = unwrapData(paymentMethodsData);

  const UpdatePaymentSchema = yup.object({
    billingAccounts: yup.array().of(
      yup.object().shape({
        paymentMethod: yup
          .string()
          .oneOf(['banktransfer', 'directdebit'])
          .nonNullable()
          .required(fields.content.paymentMethodsList?.value?.requiredMessage ?? 'Required [not in sitecore]'),
        iban: yup
          .string()
          .nonNullable()
          .test(
            'iban-valid',
            fields.content.bankAccountNumberFormField?.value?.validationMessage ??
              'IBAN onjuist formaat [not in sitecore]',
            value => isValidFormattedIban(value || ''),
          )
          .required(fields.content.bankAccountNumberFormField?.value?.requiredMessage ?? 'Required [not in sitecore]'),
      }),
    ),
    directDebitAgreementCheckbox: yup
      .boolean()
      .test(
        'direct-debit-agreement',
        fields.content.directdebitAgreementCheckboxFormField?.value?.validationMessage ??
          'Checkbox moet aangevinkt zijn bij domicilie [not in sitecore]',
        function (value) {
          const billingAccountsParent = this.parent.billingAccounts;
          const billingAccounts = Array.isArray(billingAccountsParent) ? billingAccountsParent : [];
          const hasDirectDebitInSelectedPaymentMethods = billingAccounts.some(
            ba => (ba.paymentMethod?.toLocaleLowerCase() as PaymentMethodDto) === 'DirectDebit'.toLocaleLowerCase(),
          );
          if (hasDirectDebitInSelectedPaymentMethods) return !!value;

          return true;
        },
      ),
  });

  type FormValues = yup.InferType<typeof UpdatePaymentSchema>;
  const resolver = yupResolver(UpdatePaymentSchema);

  const form = useForm<FormValues>({
    mode: 'onBlur',
    resolver,
    defaultValues: { billingAccounts: [], directDebitAgreementCheckbox: false },
  });

  const {
    register,
    handleSubmit,
    control,
    setValue,
    setError,
    watch,
    getValues,
    trigger,
    formState: { errors },
  } = form;

  const billingAccounts = watch('billingAccounts');
  const shouldShowDirectDebitAgreementCheckbox = billingAccounts?.some(
    ba => (ba.paymentMethod?.toLocaleLowerCase() as PaymentMethodDto) === 'DirectDebit'.toLocaleLowerCase(),
  );

  const handleSubmitClick = async () => {
    await trigger(['billingAccounts', 'directDebitAgreementCheckbox']);
    if (Object.keys(errors).length) return;
    const billingAccounts = getValues('billingAccounts');
    const directDebitAgreementCheckbox = getValues('directDebitAgreementCheckbox');
    const billingAccountWithDirectDebit = billingAccounts?.find(ba => ba.paymentMethod === 'banktransfer');
    const billingAccountWithDirectDebitIndex = billingAccounts?.findIndex(ba => ba.paymentMethod === 'banktransfer');
    if (billingAccountWithDirectDebit && billingAccountWithDirectDebitIndex !== undefined) {
      const billingAccountWithEbilling = data?.items?.[billingAccountWithDirectDebitIndex];
      if (billingAccountWithEbilling?.hasDirectDebitRequirement) {
        setShowPromotionWarning(true);
        return;
      }
    }
    submitForm({ billingAccounts, directDebitAgreementCheckbox });
  };

  const submitForm: SubmitHandler<FormValues> = async ({ billingAccounts, directDebitAgreementCheckbox: _ }) => {
    setIsLoadingSubmit(true);
    const { response, error } =
      await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethods({
        path: { accountNumber: selectedAccount.crmAccountNumber, addressIdentifier },
        body: {
          items: billingAccounts?.map((ba, index) => ({
            directDebitConsentGiven: true,
            billingAccountNumber: data?.items?.[index].billingAccountNumber,
            iban: normalizeIban(ba.iban),
            newPaymentMethod: ba.paymentMethod.toLocaleLowerCase() === 'banktransfer' ? 'BankTransfer' : 'DirectDebit',
          })),
        },
      });
    try {
      if (response.ok) {
        await mutate(
          `/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/payment-methods`,
        ).then(() => {
          redirectAndNotify({
            route: fields.content.saveButtonLink.value.href,
            variant: 'success',
            title: fields.content.successNotification.value.title,
            text: <RichText html={fields.content.successNotification.value.content} />,
            headingLevel: 'h3',
          });
        });
      } else {
        setIsLoadingSubmit(false);
        const validationErrors = unwrapValidationError(error);
        if (validationErrors.length > 0) {
          if (validationErrors.includes('ChangePayment.InvalidIban'))
            setError('root', {
              message: fields.content.bankAccountNumberFormField.value.validationMessage,
            });
          else
            setError('root', {
              message: fields.content.errorNotification.value.content,
            });
        }
      }
    } catch {
      setIsLoadingSubmit(false);
      setError('root', {
        message: fields.content.errorNotification.value.content,
      });
    }
  };

  useEffect(() => {
    if (data && data?.items?.length) {
      setValue(
        'billingAccounts',
        data.items.map(ba => ({
          iban: ba.iban ?? '',
          paymentMethod: ba.currentPaymentMethod?.toLocaleLowerCase() as 'banktransfer' | 'directdebit',
        })),
      );
    }
  }, [data, setValue]);

  if (isCurrentAccountReader) {
    push(fields.content.cancelButtonLink.value.href);
    return null;
  }

  const isDisabledIbanInput = ({
    isCurrentAccountReader,
    billingAccount,
    billingAccountsFormIndex,
    billingAccountsForm,
  }: {
    isCurrentAccountReader: boolean;
    billingAccount: PaymentMethodByBillingAccountResponse;
    billingAccountsFormIndex: number;
    billingAccountsForm:
      | {
          paymentMethod: NonNullable<'banktransfer' | 'directdebit' | undefined>;
          iban: string;
        }[]
      | undefined;
  }) => {
    if (isCurrentAccountReader) return true;

    if (!billingAccountsForm || !billingAccount.iban) {
      return false;
    }

    const paymentMethodApi = billingAccount.currentPaymentMethod?.toLocaleLowerCase() as PaymentMethodDto;
    const paymentMethodForm = billingAccountsForm[
      billingAccountsFormIndex
    ]?.paymentMethod?.toLocaleLowerCase() as PaymentMethodDto;

    if (
      paymentMethodApi === 'BankTransfer'?.toLocaleLowerCase() &&
      paymentMethodForm === 'BankTransfer'?.toLocaleLowerCase()
    ) {
      return true;
    }
    return false;
  };

  if (!isLoading && error !== undefined && paymentMethodsData === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <FormProvider {...form}>
      <Form>
        <Bucket title={fields.content.title.value}>
          <Bucket.Content>
            <Stack gap={6}>
              {isLoading && <LoadingListSpinner />}
              {data &&
                data.items?.length &&
                data.items.map((billingAccount, index) => (
                  <div key={billingAccount.billingAccountNumber}>
                    <Stack gap={6}>
                      <label id={`billingAccounts.${index}.paymentMethod`}>
                        {billingAccount.meters?.map(meter => (
                          <Text weight={'bold'} size={'BodyM'} key={meter.ean}>
                            {`${fields.content.consumptionTypesList.value?.enum?.find(x => x.value?.toLocaleLowerCase() === meter.energyType?.toLocaleLowerCase())?.label} - ${fields.content.eanLabel.value} ${meter.ean}`}
                          </Text>
                        ))}
                      </label>
                      <Controller
                        control={control}
                        name={`billingAccounts.${index}.paymentMethod`}
                        render={({ field: { onChange, value } }) => (
                          <RadioGroup
                            name={`billingAccounts.${index}.paymentMethod`}
                            aria-labelledby={`billingAccounts-${index}-paymentMethod`}
                            direction="column"
                            value={value}
                            onValueChange={onChange}>
                            <RadioButton
                              value={fields.content.paymentMethodsList.value.options?.[0].value?.toLocaleLowerCase()}>
                              {fields.content.paymentMethodsList.value.options?.[0].label}
                            </RadioButton>
                            <RadioButton
                              value={fields.content.paymentMethodsList.value.options?.[1].value?.toLocaleLowerCase()}>
                              {fields.content.paymentMethodsList.value.options?.[1].label}
                            </RadioButton>
                          </RadioGroup>
                        )}
                      />
                      <IbanNumberInput
                        label={fields.content.bankAccountNumberFormField.value.label}
                        hint={fields.content.bankAccountNumberFormField.value.hint}
                        isDisabled={isDisabledIbanInput({
                          isCurrentAccountReader,
                          billingAccount,
                          billingAccountsForm: billingAccounts,
                          billingAccountsFormIndex: index,
                        })}
                        errorMessage={fields.content.bankAccountNumberFormField.value.validationMessage}
                        defaultValue={billingAccount.iban ?? ''}
                        showErrorMessage={!!errors.billingAccounts?.[index]?.iban?.message?.length}
                        {...register(`billingAccounts.${index}.iban`)}
                      />
                    </Stack>
                  </div>
                ))}

              {billingAccounts?.some(
                x => (x.paymentMethod?.toLocaleLowerCase() as PaymentMethodDto) === 'BankTransfer'.toLocaleLowerCase(),
              ) && <Text size="BodyS">{fields.content.refundAccountBankTransferNoticeLabel.value}</Text>}
              {billingAccounts?.some(
                x => (x.paymentMethod?.toLocaleLowerCase() as PaymentMethodDto) === 'DirectDebit'.toLocaleLowerCase(),
              ) && <Text size="BodyS">{fields.content.refundAccountDirectDebitNoticeLabel.value}</Text>}

              {shouldShowDirectDebitAgreementCheckbox && (
                <Checkbox
                  error={errors.directDebitAgreementCheckbox?.message}
                  label={fields.content.directdebitAgreementCheckboxFormField.value.label}
                  {...register('directDebitAgreementCheckbox')}
                />
              )}
              {errors.root && (
                <NotificationBox
                  isAlert
                  text={fields.content.errorNotification.value.content}
                  title={fields.content.errorNotification.value.title}></NotificationBox>
              )}
            </Stack>
          </Bucket.Content>
          <Bucket.Footer>
            <Bucket.Actions>
              <Button onClick={handleSubmitClick} isLoading={isLoadingSubmit}>
                {fields.content.saveButtonLink.value.text}
              </Button>
              <Button action="secondary" onClick={() => push(fields.content.cancelButtonLink.value.href)}>
                {fields.content.cancelButtonLink.value.text}
              </Button>
            </Bucket.Actions>
          </Bucket.Footer>
        </Bucket>
      </Form>
      <AlertDialog
        title={fields.promotion?.dialog.value.title}
        description={<RichText html={fields.promotion?.dialog.value.content} />}
        isOpen={showPromotionWarning}
        setOpen={setShowPromotionWarning}
        confirmText={fields.promotion?.dialog.value.submitButtonText || 'Doorgaan [not in sitecore]'}
        onConfirm={handleSubmit(submitForm)}
        onDeny={() => {}}
        denyText={fields.promotion?.dialog.value.cancelButtonText || 'Cancel [not in sitecore]'}
      />
    </FormProvider>
  );
}

export default UpdateInvoicePaymentMethod;
