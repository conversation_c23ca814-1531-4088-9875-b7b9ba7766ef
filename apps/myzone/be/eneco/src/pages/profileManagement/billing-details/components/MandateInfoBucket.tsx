import { format } from 'date-fns';

import { useApplication } from '@common/application';
import { getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethods } from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { MandateInformationRendering } from '@sitecore/types/MandateInformation';
import { Bucket } from '@sparky';

import DataErrorNotification from '../../../../components/DataErrorNotification';
import ReadFormField from '../../../../components/ReadFormField';
import { useAddressOptions } from '../../../../hooks/useAddressOptions';
import { getDateLocale } from '../../../../utils/date/get-locale';

function MandateInfoBucket() {
  const { fields } = useContent<MandateInformationRendering>();
  const { selectedAccount } = useSelfServiceAccount();
  const { data: session } = useSession();
  const { addressIdentifier } = useAddressOptions();
  const { locale } = useApplication();

  const {
    data: paymentMethodsData,
    isLoading,
    error,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethods,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier: addressIdentifier ?? '',
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/payment-methods`],
    session,
  );

  const data = unwrapData(paymentMethodsData);
  return data?.items?.map(billingAccount => {
    const isBillingAccountInvalid = [
      billingAccount.mandateId,
      billingAccount.signatureDate,
      billingAccount.iban,
      billingAccount.signatureDate,
    ].some(val => !val);

    if (isBillingAccountInvalid) return null;

    if (!isLoading && error !== undefined && paymentMethodsData === undefined) {
      return <DataErrorNotification></DataErrorNotification>;
    }

    return (
      <Bucket
        as="section"
        headingLevel="h3"
        title={fields.data?.title?.value ?? 'Domiciliëringsmandaat [not in sitecore]'}
        key={billingAccount?.mandateId}>
        <Bucket.Content>
          <ReadFormField
            label={fields.data?.mandateNumberLabel?.value ?? 'MandaatNummer [not in sitecore]'}
            value={billingAccount?.mandateId}
          />
          <ReadFormField
            label={fields.data?.signatureDateLabel?.value ?? 'Ondertekeningsdatum [not in sitecore]'}
            value={format(billingAccount.signatureDate!, 'dd/MM/yyyy', { locale: getDateLocale(locale) })}
          />
          <ReadFormField
            label={fields.data?.ibanLabel?.value ?? 'Rekeningnummer [not in sitecore]'}
            value={billingAccount?.iban}
          />
          <ReadFormField
            label={fields.data?.signatureLocationLabel?.value ?? 'Ondertekeningslocatie [not in sitecore]'}
            value={billingAccount?.signatureLocation}
          />
        </Bucket.Content>
      </Bucket>
    );
  });
}

export default MandateInfoBucket;
