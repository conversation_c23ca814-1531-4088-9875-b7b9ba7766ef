import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { MyEnecoInvoiceBillingDetailsRendering } from '@sitecore/types/MyEnecoInvoiceBillingDetails';
import { Stack } from '@sparky';

import { InvoiceDueDateBucket } from './components/InvoiceDueDateBucket';
import { InvoiceFrequencyBucket } from './components/InvoiceFrequencyBucket';
import { InvoicePaymentMethodBucket } from './components/InvoicePaymentMethodBucket';
import { AddressFilter } from '../../../components/AddressFilter';
import DataErrorNotification from '../../../components/DataErrorNotification';
import { useAddressOptions } from '../../../hooks/useAddressOptions';

const BillingDetails = () => {
  const { fields } = useContent<MyEnecoInvoiceBillingDetailsRendering>();
  const { isProspect } = useSelfServiceAccount();

  const { addressIdentifier, isLoading, error } = useAddressOptions();

  if (isProspect) {
    return null;
  }

  if (!isLoading && error != undefined && addressIdentifier === null) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Stack gap={6}>
      <AddressFilter label={fields.billingDetails.consumptionAddressLabel.value} />
      <InvoicePaymentMethodBucket
        isLoadingAddress={isLoading}
        fields={fields.paymentMethod}
        addressIdentifier={addressIdentifier}
      />
      <InvoiceDueDateBucket
        isLoadingAddress={isLoading}
        fields={fields.dueDateAdvanceInvoices}
        addressIdentifier={addressIdentifier}
      />
      <InvoiceFrequencyBucket
        isLoadingAddress={isLoading}
        billingDetails={fields.billingDetails}
        billingFrequency={fields.billingFrequency}
        addressIdentifier={addressIdentifier}
        fields={{ eanLabel: fields.billingDetails.eanLabel.value }}
      />
    </Stack>
  );
};

export default BillingDetails;
