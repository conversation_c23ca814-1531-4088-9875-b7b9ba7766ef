import { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import { useApplication } from '@common/application';
import RichText from '@components/RichText/RichText';
import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriod,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts,
  MonthlyInvoicePeriodDto,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriod,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import mapScEnumToMap from '@dc-be/utils/mapScEnumToMap';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { MyEnecoInvoiceEditDueDateRendering } from '@sitecore/types/MyEnecoInvoiceEditDueDate';
import { Bucket, Button, ButtonLink, Form, InputSelect, NotificationBox } from '@sparky';

import { filterMetersWithYearlyInvoiceFrequency } from './utils/filterMetersWithYearlyInvoiceFrequency';
import { getBillingAccountLabel } from './utils/getBillingAccountLabel';
import { groupMeterByBillingAccount } from './utils/groupMeterByBillingAccount';
import DataErrorNotification from '../../../components/DataErrorNotification';
import { useRedirectAndNotifyBE } from '../../../hooks/useRedirectAndNotifyBE';
import { addressSearchParam } from '../../../utils/addSearchParams';

const InvoiceEditDueDate: FC = () => {
  const {
    fields: { editDueDate: fields },
  } = useContent<MyEnecoInvoiceEditDueDateRendering>();

  const { data: session } = useSession();
  const { selectedAccount, isCurrentAccountReader } = useSelfServiceAccount();
  const [hasDefaultValue, setHasDefaultValue] = useState(false);
  const { mutate } = useSWRConfig();
  const redirectAndNotify = useRedirectAndNotifyBE();
  const { searchParams } = useApplication();
  const addressIdentifier = searchParams.get('address') || '';
  const { push } = useRouter();
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  const InvoiceDueDateSchema = yup.object({
    billingAccounts: yup.array().of(
      yup.object({
        billingAccountNumber: yup.string().required(),
        period: yup
          .string()
          .oneOf(['NoPreference', 'Between4And11', 'Between11And18', 'Between21And25', 'Between28And4'])
          .required(),
      }),
    ),
  });

  type FormValues = yup.InferType<typeof InvoiceDueDateSchema>;
  const resolver = yupResolver(InvoiceDueDateSchema);

  const form = useForm({ resolver });

  const {
    register,
    handleSubmit,
    setValue,
    setError,
    formState: { errors },
  } = form;

  const {
    data: invoicesData,
    isLoading: isLoadingInvoices,
    error: errorInvoices,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriod,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier: addressIdentifier ?? '',
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/invoices/invoice-period`],
    session,
  );

  const {
    data: invoiceFrequencyData,
    isLoading: isLoadingInvoiceFrequency,
    error: errorInvoiceFrequency,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier: addressIdentifier ?? '',
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/products`],
    session,
  );

  const billingData = unwrapData(invoicesData);
  const invoiceFrequency = unwrapData(invoiceFrequencyData);

  const billingDataFilteredOnlyYearlyMeters = filterMetersWithYearlyInvoiceFrequency(
    billingData?.meters,
    invoiceFrequency,
    false,
  );
  const metersPerBillingAccount = groupMeterByBillingAccount(billingDataFilteredOnlyYearlyMeters);

  useEffect(() => {
    if (metersPerBillingAccount && metersPerBillingAccount.length > 0 && !hasDefaultValue) {
      setValue(
        'billingAccounts',
        metersPerBillingAccount.map(([billingAccountNumber, meters]) => ({
          billingAccountNumber,
          period: meters[0].period,
        })),
      );
      setHasDefaultValue(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [metersPerBillingAccount]);

  if (invoiceFrequency?.activeProducts?.every(x => x.meterReadingMonth === null || x.meterReadingMonth === undefined)) {
    push(fields.cancelLink.value.href);
    return null;
  }

  const submitForm: SubmitHandler<FormValues> = async ({ billingAccounts }) => {
    if (!addressIdentifier) return;
    setIsLoadingSubmit(true);

    const { response } =
      await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriod({
        path: { accountNumber: selectedAccount.crmAccountNumber, addressIdentifier },
        body: {
          meters: billingAccounts?.map(({ billingAccountNumber, period }) => ({
            billingAccountNumber,
            period,
          })),
        },
      });

    if (response.ok) {
      await mutate(
        `/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/invoices/invoice-period`,
      );
      redirectAndNotify({
        route: fields.submitLink.value.href + addressSearchParam(addressIdentifier),
        text: <RichText html={fields.editSuccessNotification.value.content} />,
        title: fields.editSuccessNotification.value.title,
        variant: 'success',
      });
    } else {
      setIsLoadingSubmit(false);
      setError('root', {
        message: fields.editFailedNotification.value.content,
      });
    }
  };

  if (isCurrentAccountReader) {
    push(fields.cancelLink.value.href + addressSearchParam(addressIdentifier));
    return null;
  }

  const isLoading = isLoadingInvoiceFrequency || isLoadingInvoices;

  if (
    !isLoading &&
    ((errorInvoices !== undefined && invoicesData === undefined) ||
      (errorInvoiceFrequency !== undefined && invoiceFrequencyData === undefined))
  ) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Bucket title={fields?.title.value}>
      <FormProvider {...form}>
        <Form onSubmit={handleSubmit(submitForm)}>
          <Bucket.Content>
            <RichText html={fields.description.value} />
            {metersPerBillingAccount &&
              metersPerBillingAccount.map(([billingAccountNumber, meters], index) => {
                const invoiceRangeMap = mapScEnumToMap<MonthlyInvoicePeriodDto>(fields.invoiceRangeList.value);
                const value = invoiceRangeMap.get(meters[0].period) ?? `${meters[0].period} [not in Sitecore]`;
                const label = getBillingAccountLabel({
                  consumptionTypesList: fields.consumptionTypesList,
                  meters,
                  eanLabel: fields.eanLabel,
                });
                const options = invoiceRangeMap
                  .entries()
                  // @ts-ignore tsc doesn't know MapIterator
                  .flatMap(([value, label]) => (value ? [{ value, label, name: value }] : []))
                  .toArray();

                return (
                  <InputSelect
                    placeholder=""
                    key={billingAccountNumber}
                    label={label}
                    defaultValue={value}
                    options={options}
                    {...register(`billingAccounts.${index}.period`)}
                  />
                );
              })}
            <RichText html={fields.dueDateChangeInfoContent.value} />
            {errors.root && (
              <NotificationBox
                isAlert
                text={fields.editFailedNotification.value.content}
                title={fields.editFailedNotification.value.title}></NotificationBox>
            )}
          </Bucket.Content>
          <Bucket.Footer>
            <Bucket.Actions>
              <Button type="submit" isLoading={isLoadingSubmit}>
                {fields.submitLink.value.text}
              </Button>
              <ButtonLink
                action={'secondary'}
                href={fields.cancelLink.value.href + addressSearchParam(addressIdentifier)}>
                {fields.cancelLink.value.text}
              </ButtonLink>
            </Bucket.Actions>
          </Bucket.Footer>
        </Form>
      </FormProvider>
    </Bucket>
  );
};

export default InvoiceEditDueDate;
