import { FC } from 'react';

import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriod,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts,
  MonthlyInvoicePeriodDto,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import mapScEnumToMap from '@dc-be/utils/mapScEnumToMap';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { MyEnecoInvoiceBillingDetailsRendering } from '@sitecore/types/MyEnecoInvoiceBillingDetails';
import { Bucket, TextLink } from '@sparky';

import { BillingDetailsReadFormField } from './BillingDetailsReadFormField';
import DataErrorNotification from '../../../../components/DataErrorNotification';
import ReadFormField from '../../../../components/ReadFormField';
import { ADDRESS_PARAM } from '../../../../utils/addSearchParams';
import { filterMetersWithYearlyInvoiceFrequency } from '../utils/filterMetersWithYearlyInvoiceFrequency';
import { groupMeterByBillingAccount } from '../utils/groupMeterByBillingAccount';

export interface InvoiceDueDateBucketProps {
  fields: MyEnecoInvoiceBillingDetailsRendering['fields']['dueDateAdvanceInvoices'];
  addressIdentifier: string | null | undefined;
  isLoadingAddress: boolean;
}

export const InvoiceDueDateBucket: FC<InvoiceDueDateBucketProps> = ({
  fields,
  addressIdentifier,
  isLoadingAddress,
}) => {
  const { selectedAccount } = useSelfServiceAccount();
  const { data: session } = useSession();
  const { isCurrentAccountReader } = useSelfServiceAccount();

  const {
    data: invoicesData,
    isLoading: isLoadingInvoices,
    error: errorInvoices,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriod,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier: addressIdentifier ?? '',
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/invoices/invoice-period`],
    session,
  );

  const {
    data: invoiceFrequencyData,
    isLoading: isLoadingInvoiceFrequency,
    error: errorInvoiceFrequency,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier: addressIdentifier ?? '',
      },
    },
    addressIdentifier
      ? [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/products`]
      : null,
    session,
  );

  const isLoading = isLoadingInvoiceFrequency || isLoadingAddress || isLoadingInvoices;

  const billingData = unwrapData(invoicesData);
  const invoiceFrequency = unwrapData(invoiceFrequencyData);

  const billingDataFilteredOnlyYearlyMeters = filterMetersWithYearlyInvoiceFrequency(
    billingData?.meters,
    invoiceFrequency,
    true,
  );

  const metersPerBillingAccount = groupMeterByBillingAccount(billingDataFilteredOnlyYearlyMeters);

  const fetchingInvoicesError = errorInvoices !== undefined && invoicesData === undefined;
  const fetchingInvoiceFrequencyError = errorInvoiceFrequency !== undefined && invoiceFrequencyData === undefined;

  if (!isLoading && (fetchingInvoicesError || fetchingInvoiceFrequencyError)) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    metersPerBillingAccount &&
    metersPerBillingAccount.length > 0 && (
      <Bucket title={fields.title.value}>
        <Bucket.Content>
          {isLoading && <ReadFormField isLoading={isLoading} isLoadingLabel={isLoading} value={''} label={''} />}
          {metersPerBillingAccount.map(([billingAccountNumber, meters]) => {
            const invoiceRangeMap = mapScEnumToMap<MonthlyInvoicePeriodDto>(fields.invoiceRangeList.value);
            const value = invoiceRangeMap.get(meters[0].period) ?? `${meters[0].period} [not in Sitecore]`;

            return <BillingDetailsReadFormField key={billingAccountNumber} meters={meters} value={value} />;
          })}
          {!isCurrentAccountReader &&
            invoiceFrequency?.activeProducts &&
            invoiceFrequency?.activeProducts.length > 0 && (
              <TextLink emphasis="high" href={`${fields.updateLink.value.href}?${ADDRESS_PARAM}=${addressIdentifier}`}>
                {fields.updateLink.value.text}
              </TextLink>
            )}
        </Bucket.Content>
      </Bucket>
    )
  );
};
