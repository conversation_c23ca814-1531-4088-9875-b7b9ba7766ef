import { FC } from 'react';

import RichText from '@components/RichText/RichText';
import {
  EnergyTypeDto,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
  InvoiceFrequency,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import mapScEnumToMap from '@dc-be/utils/mapScEnumToMap';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { BillingDetails, BillingFrequency } from '@sitecore/types/MyEnecoInvoiceBillingDetails';
import { Bucket, NotificationBox, TextLink } from '@sparky';

import DataErrorNotification from '../../../../components/DataErrorNotification';
import ReadFormField from '../../../../components/ReadFormField';
import { ADDRESS_PARAM } from '../../../../utils/addSearchParams';
import { canUpdateMeterFrequency } from '../utils/canUpdateMeterFrequency';

interface InvoiceFrequencyBucketProps {
  billingDetails: BillingDetails;
  billingFrequency: BillingFrequency;
  addressIdentifier: string | null | undefined;
  fields: { eanLabel?: string };
  isLoadingAddress: boolean;
}

export const InvoiceFrequencyBucket: FC<InvoiceFrequencyBucketProps> = ({
  billingDetails,
  billingFrequency,
  addressIdentifier,
  fields,
  isLoadingAddress,
}) => {
  const { selectedAccount, isCurrentAccountReader } = useSelfServiceAccount();
  const { data: session } = useSession();

  const {
    isLoading: isLoadingServiceDeliveryData,
    data: serviceDeliveryData,
    error: errorServiceDeliveryData,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier: addressIdentifier ?? '',
      },
      query: {
        includeMetersFromTerminatedAccounts: true,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/service-delivery-points`],
    session,
  );

  const {
    isLoading: isLoadingProductsData,
    data: productsData,
    error: errorProductsData,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier: addressIdentifier ?? '',
      },
    },
    addressIdentifier
      ? [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/products`]
      : null,
    session,
  );

  const serviceDeliveryPoints = unwrapData(serviceDeliveryData)?.serviceDeliveryPoints;
  const products = unwrapData(productsData);

  const consumptionTypesList = billingDetails?.consumptionTypesList;
  const frequencyPeriodList = billingFrequency?.frequencyPeriodsList;
  const isLoading = isLoadingProductsData || isLoadingServiceDeliveryData || isLoadingAddress;

  const invoiceFrequencyCanBeChanged = serviceDeliveryPoints?.some(serviceDeliveryPoint =>
    canUpdateMeterFrequency(
      serviceDeliveryPoint.meterType,
      serviceDeliveryPoint?.invoiceFrequency?.changeRequestPending,
    ),
  );

  if (
    !isLoading &&
    ((errorServiceDeliveryData !== undefined && serviceDeliveryData === undefined) ||
      (errorProductsData !== undefined && productsData === undefined))
  ) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Bucket title={billingFrequency.title.value}>
      <Bucket.Content>
        {isLoading && <ReadFormField isLoading={isLoading} isLoadingLabel={isLoading} label={''} value={''} />}
        {serviceDeliveryPoints &&
          serviceDeliveryPoints.map(serviceDeliveryPoint => {
            const invoiceFrequencyMap = mapScEnumToMap<InvoiceFrequency>(frequencyPeriodList.value);
            const energyTypeMap = mapScEnumToMap<EnergyTypeDto>(consumptionTypesList.value);

            const ean = serviceDeliveryPoint.ean;
            const energyTypeFromMap = energyTypeMap.get(serviceDeliveryPoint?.type);
            const serviceDeliveryPointLabel = `${energyTypeFromMap} - ${fields.eanLabel} ${ean}`;
            const frequency = invoiceFrequencyMap.get(serviceDeliveryPoint?.invoiceFrequency?.current);

            return <ReadFormField key={ean} label={serviceDeliveryPointLabel} value={frequency} />;
          })}
        {!isCurrentAccountReader &&
          invoiceFrequencyCanBeChanged &&
          products?.activeProducts &&
          products?.activeProducts.length > 0 && (
            <TextLink
              emphasis="high"
              href={`${billingFrequency?.updateLink?.value.href}?${ADDRESS_PARAM}=${addressIdentifier}`}>
              {billingFrequency?.updateLink?.value.text}
            </TextLink>
          )}
        {!invoiceFrequencyCanBeChanged && !isLoading && (
          <NotificationBox
            isAlert={false}
            title={billingFrequency.infoNotification.value.title}
            text={<RichText html={billingFrequency.infoNotification.value.content} />}
            variant={billingFrequency.infoNotification.value.variant ?? 'info'}
          />
        )}
      </Bucket.Content>
    </Bucket>
  );
};
