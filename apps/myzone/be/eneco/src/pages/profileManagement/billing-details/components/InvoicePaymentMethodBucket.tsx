import { FC } from 'react';

import { getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethods } from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { MyEnecoInvoiceBillingDetailsRendering } from '@sitecore/types/MyEnecoInvoiceBillingDetails';
import { Bucket, TextLink } from '@sparky';

import { BillingDetailsReadFormField } from './BillingDetailsReadFormField';
import DataErrorNotification from '../../../../components/DataErrorNotification';
import ReadFormField from '../../../../components/ReadFormField';
import { useAddressOptions } from '../../../../hooks/useAddressOptions';
import { ADDRESS_PARAM } from '../../../../utils/addSearchParams';

type InvoicePaymentMethodBucketProps = {
  fields: MyEnecoInvoiceBillingDetailsRendering['fields']['paymentMethod'];
  addressIdentifier: string | null | undefined;
  isLoadingAddress: boolean;
};

export const InvoicePaymentMethodBucket: FC<InvoicePaymentMethodBucketProps> = ({
  fields,
  addressIdentifier,
  isLoadingAddress,
}) => {
  const { isCurrentAccountReader, selectedAccount } = useSelfServiceAccount();
  const { addressOptions } = useAddressOptions() || {};
  const selectedAddress = addressOptions?.find(option => option.value === addressIdentifier);
  const { data: session } = useSession();

  const {
    data: paymentMethodsData,
    isLoading: isLoadingPaymentMethods,
    error: paymentMethodsError,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethods,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier: selectedAddress?.value ?? '',
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${selectedAddress?.value}/payment-methods`],
    session,
  );

  const isLoading = isLoadingPaymentMethods || isLoadingAddress;
  const fetchingPaymentMethodError = paymentMethodsError !== undefined && paymentMethodsData === undefined;

  if (!isLoading && fetchingPaymentMethodError) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  const data = unwrapData(paymentMethodsData);

  return (
    <Bucket title={fields.title.value}>
      <Bucket.Content>
        {isLoading && <ReadFormField isLoading={isLoading} isLoadingLabel={isLoading} value={''} label={''} />}

        {data?.items &&
          data.items.length &&
          data.items.map(billingAccount => {
            if (!billingAccount.meters) return null;
            const paymentMethodLabel = fields.methodsList.value.enum.find(
              x => x.value?.toLocaleLowerCase() === billingAccount.currentPaymentMethod?.toLocaleLowerCase(),
            )?.label;
            return (
              <BillingDetailsReadFormField
                key={billingAccount.billingAccountNumber}
                meters={billingAccount.meters}
                value={paymentMethodLabel}
              />
            );
          })}
        {!isCurrentAccountReader && (
          <TextLink
            emphasis="high"
            href={`${fields.updateLink.value.href.replace(/\/$/, '')}?${ADDRESS_PARAM}=${selectedAddress?.value}`}>
            {fields.updateLink?.value?.text}
          </TextLink>
        )}
      </Bucket.Content>
    </Bucket>
  );
};
