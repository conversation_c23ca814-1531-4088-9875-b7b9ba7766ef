import { useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { format } from 'date-fns';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import { useApplication } from '@common/application';
import { emailValidation } from '@common/validation/emailRegex';
import RichText from '@components/RichText/RichText';
import {
  CustomerLanguageDto,
  GenderDto,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { unwrapValidationError } from '@dc-be/utils/unwrapError';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { EditContactDataRendering } from '@sitecore/types/EditContactData';
import { Bucket, Button, Form, Grid, InputEmail, InputSelect, InputText, NotificationBox } from '@sparky';
import { useTracking } from '@tracking';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { Input } from 'libs/sparky/src/components/Input/Input';

import DataErrorNotification from '../../../components/DataErrorNotification';
import PhoneNumberInputField, { phoneValidationScheme } from '../../../components/PhoneNumberInputField';
import { useRedirectAndNotifyBE } from '../../../hooks/useRedirectAndNotifyBE';
import AddressAutocomplete from '../../../utils/address-autocomplete/AddressAutocomplete';
import { getCountryOptions } from '../../../utils/countries';

const EditContactData = () => {
  const { fields } = useContent<EditContactDataRendering>();
  const { push } = useRouter();
  const { selectedAccount, isProspect } = useSelfServiceAccount();
  const { data: session } = useSession();
  const redirectAnNotify = useRedirectAndNotifyBE();
  const { mutate } = useSWRConfig();
  const { language } = useApplication();
  const { trackEventBE } = useTracking();
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  const { data, isLoading, error } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/contact/details`],
    session,
  );

  const contactData = unwrapData(data);

  const contactDataSchema = yup.object({
    language: yup.string<CustomerLanguageDto>().required(fields.languageFormField.value.requiredMessage),
    gender: yup.string<GenderDto>().required(fields.salutationFormField.value.requiredMessage),
    firstName: yup.string().required(fields.firstNameFormField.value.requiredMessage),
    lastName: yup.string().required(fields.lastNameFormField.value.requiredMessage),
    dateOfBirth: yup.date().typeError(fields.birthDateFormField.value.requiredMessage),
    phoneNumber: phoneValidationScheme({
      validationMessage: fields.phoneNumberFormField.value.validationMessage,
      isRequired: false,
    }),
    mobilePhoneNumber: phoneValidationScheme({
      validationMessage: fields.mobilePhoneNumberFormField.value.validationMessage,
      isRequired: false,
    }),
    email: emailValidation({
      emailMessage: fields?.emailAddressFormField?.value?.validationMessage,
      requiredMessage: fields?.emailAddressFormField?.value?.requiredMessage,
    }),
    zipCode: yup
      .string()
      .typeError(fields.zipCodeFormField.value.validationMessage)
      .required(fields.zipCodeFormField.value.requiredMessage),
    city: yup.string().required(fields.cityFormField.value.requiredMessage),
    streetName: yup.string().required(fields.streetFormField.value.requiredMessage),
    houseNumber: yup
      .number()
      .typeError(fields.houseNumberFormField.value.validationMessage)
      .required(fields.houseNumberFormField.value.requiredMessage),
    bus: yup.string(),
    country: yup
      .string()
      .required(fields.countryFormField?.value.requiredMessage || '[Not from Sitecore] Land is verplicht.'),
  });

  type FormValues = yup.InferType<typeof contactDataSchema>;

  const resolver = yupResolver(contactDataSchema);

  const form = useForm<FormValues>({
    resolver,
    values: {
      language: contactData?.language as CustomerLanguageDto,
      gender: contactData?.gender as GenderDto,
      firstName: contactData?.firstName ?? '',
      lastName: contactData?.lastName ?? '',
      phoneNumber: contactData?.telephoneNumber ?? '',
      mobilePhoneNumber: contactData?.mobileNumber ?? '',
      email: contactData?.email ?? '',
      dateOfBirth: contactData?.dateOfBirth ? new Date(contactData?.dateOfBirth) : undefined,
      zipCode: contactData?.contactAddress?.zipCode ?? '',
      city: contactData?.contactAddress?.municipality ?? '',
      streetName: contactData?.contactAddress?.street ?? '',
      houseNumber: contactData?.contactAddress?.houseNumber as unknown as number,
      bus: contactData?.contactAddress?.bus ?? '',
      country: contactData?.contactAddress?.country ?? '',
    },
  });

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    watch,
    formState: { errors },
  } = form;

  const { isCurrentAccountReader } = useSelfServiceAccount();
  const contactAddress = contactData?.contactAddress;
  const dateOfBirth = watch('dateOfBirth');
  const country = watch('country');

  useEffect(() => {
    const dateOfBirthDefaultValue = contactData?.dateOfBirth ? new Date(contactData?.dateOfBirth) : undefined;
    setValue('dateOfBirth', dateOfBirthDefaultValue);
  }, [contactData?.dateOfBirth, setValue]);

  const submitForm: SubmitHandler<FormValues> = async values => {
    setIsLoadingSubmit(true);
    const errZipCode = form.formState.errors.zipCode;
    if (errZipCode) return;

    await putEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails({
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
      body: {
        ...contactData,
        firstName: values.firstName,
        lastName: values.lastName,
        dateOfBirth: values.dateOfBirth?.toISOString(),
        telephoneNumber: values.phoneNumber,
        mobileNumber: values.mobilePhoneNumber,
        email: values.email,
        language: values.language,
        gender: values.gender,
        contactAddress: {
          zipCode: values.zipCode,
          municipality: values.city,
          street: values.streetName,
          country: values.country,
          houseNumber: values.houseNumber.toString(),
          bus: values.bus,
        },
      },
    }).then(result => {
      const { response, error } = result;
      const validationErrors = unwrapValidationError(error);

      if (response.ok) {
        trackEventBE('contact_data_submit', { status: 'success' });
        mutate(`/accounts/${selectedAccount.crmAccountNumber}/contact/details`);
        redirectAnNotify({
          route: fields.cancelLink.value.href,
          title: fields.successNotification?.value.title,
          text: fields.successNotification?.value.content,
          variant: 'success',
        });
      } else if (validationErrors.includes('AccountLinkingRequest.TargetCrmAccount.User.AlreadyLinked')) {
        setIsLoadingSubmit(false);
        setError('root', {
          message:
            fields.failedToUpdateAddressNotification?.value.title ??
            'Er ging iets mis bij het opslaan van je adres. [not in sitecore]',
        });
        trackEventBE('contact_data_submit', { status: 'failed', message: 'crm account already linked' });
      } else {
        setIsLoadingSubmit(false);
        setError('root', { message: fields.generalErrorNotification?.value.title });
        trackEventBE('contact_data_submit', { status: 'failed', message: 'unexpected error occurred' });
      }
    });
  };

  const languageFormFieldOptions = fields.languageFormField.value.options;
  const nl = languageFormFieldOptions.find(v => v.value === 'Dutch');
  const fr = languageFormFieldOptions.find(v => v.value === 'French');

  const languageOptions: Array<{ label: string; value: CustomerLanguageDto }> | undefined = nl &&
    fr && [
      { label: nl.label, value: 'Dutch' },
      { label: fr.label, value: 'French' },
    ];

  const genderFormFieldOptions = fields.salutationFormField.value.options;
  const male = genderFormFieldOptions.find(
    v => v.value?.toLocaleLowerCase() === 'male' || v.value?.toLocaleLowerCase() === 'dhr',
  );
  const female = genderFormFieldOptions.find(
    v => v.value?.toLocaleLowerCase() === 'female' || v.value?.toLocaleLowerCase() === 'mevr',
  );

  const genderOptions: Array<{ label: string; value: GenderDto }> | undefined = male &&
    female && [
      { label: male.label, value: 'Male' },
      { label: female.label, value: 'Female' },
    ];

  if (isProspect) {
    return null;
  }

  if (!isLoading && error !== undefined && data === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Bucket title={fields.title.value}>
      <FormProvider {...form}>
        <Form onSubmit={handleSubmit(submitForm)}>
          <Bucket.Content>
            {languageOptions && (
              <InputSelect
                label={fields.languageFormField.value.label}
                error={errors.language?.message}
                {...register('language')}
                options={languageOptions}
                placeholder=""
                isDisabled={isCurrentAccountReader}
              />
            )}
            {genderOptions && (
              <InputSelect
                label={fields.salutationFormField.value.label}
                error={errors.gender?.message}
                {...register('gender')}
                options={genderOptions}
                placeholder=""
                isDisabled={isCurrentAccountReader}
              />
            )}
            <InputText
              label={fields.firstNameFormField.value.label}
              hint={<RichText html={fields.firstNameFormField.value.hint} />}
              error={errors.firstName?.message}
              {...register('firstName')}
              isDisabled={isCurrentAccountReader}
            />
            <InputText
              label={fields.lastNameFormField.value.label}
              hint={<RichText html={fields.lastNameFormField.value.hint} />}
              error={errors.lastName?.message}
              {...register('lastName')}
              isDisabled={isCurrentAccountReader}
            />
            <InputText label={fields.customerNumberLabel.value} name="customerNumber" isDisabled />
            <Input
              label={fields.birthDateFormField.value.label}
              id="dateOfBirth"
              type="date"
              value={dateOfBirth ? format(dateOfBirth, 'yyyy-MM-dd') : ''}
              error={errors.dateOfBirth?.message}
              {...register('dateOfBirth')}
              autoComplete="off"
              max={new Date().toISOString().split('T')[0]}
            />
            <PhoneNumberInputField
              name="phoneNumber"
              label={fields.phoneNumberFormField.value.label}
              hint={<RichText html={fields.phoneNumberFormField.value.hint} />}
              placeholder={fields.phoneNumberFormField.value.placeholder}
              isDisabled={isCurrentAccountReader}
            />
            <PhoneNumberInputField
              name="mobilePhoneNumber"
              label={fields.mobilePhoneNumberFormField.value.label}
              hint={<RichText html={fields.mobilePhoneNumberFormField.value.hint} />}
              placeholder={fields.mobilePhoneNumberFormField.value.placeholder}
              isDisabled={isCurrentAccountReader}
            />
            <InputEmail
              label={fields.emailAddressFormField.value.label}
              hint={<RichText html={fields.emailAddressFormField.value.hint} />}
              error={errors.email?.message}
              autoComplete="email"
              isDisabled={isCurrentAccountReader}
              {...register('email')}
            />
            <AddressAutocomplete
              cityFormField={{
                ...fields.cityFormField.value,
                defaultValue: contactAddress?.municipality || '',
                yupProps: register('city'),
              }}
              zipCodeFormField={{
                ...fields.zipCodeFormField.value,
                defaultValue: contactAddress?.zipCode || '',
                yupProps: register('zipCode'),
              }}
              streetNameFormField={{
                ...fields.streetFormField.value,
                defaultValue: contactAddress?.street ?? '',
                yupProps: register('streetName'),
              }}
              onZipCodeCityChange={value => {
                setValue('zipCode', value.zipCode as string);
                setValue('city', value.city as string);
              }}
              onStreetNameChange={streetName => setValue('streetName', streetName)}
              streetNameNotFoundWarningTitle={
                fields.streetNameNotFoundNotification?.value?.title ?? 'Waarschuwing [not in sitecore]'
              }
              streetNameNotFoundWarningContent={
                fields.streetNameNotFoundNotification?.value?.content ??
                'Let op: je straatnaam is niet gevonden. Indien je straatnaam toch correct is kan je verder gaan met het formulier. [not in sitecore]'
              }
              zipCodeCityError={
                fields.zipCodeFormField?.value?.validationMessage && fields.cityFormField?.value?.validationMessage
                  ? `${fields.zipCodeFormField?.value?.validationMessage} ${fields.cityFormField?.value?.validationMessage}`
                  : "Combinatie van postcode en stad moet correct zijn' [not in sitecore]"
              }
              shouldValidateAddressInApi={country === 'BE'}
              isDisabled={isCurrentAccountReader}
            />

            <Grid columns={2} gap={6}>
              <InputText
                label={fields.houseNumberFormField.value.label}
                hint={<RichText html={fields.houseNumberFormField.value.hint} />}
                error={errors.houseNumber?.message}
                {...register('houseNumber')}
                isDisabled={isCurrentAccountReader}
              />
              <InputText
                label={fields.busFormField.value.label}
                hint={<RichText html={fields.busFormField.value.hint} />}
                error={errors.bus?.message}
                {...register('bus')}
                isDisabled={isCurrentAccountReader}
              />
            </Grid>
            <InputSelect
              label={fields.countryFormField?.value.label || '[Not from Sitecore] Land'}
              hint={<RichText html={fields.countryFormField?.value.hint} />}
              placeholder=""
              error={errors.country?.message}
              options={getCountryOptions(language)}
              {...register('country')}
              isDisabled={isCurrentAccountReader}
            />
            <NotificationBox
              isAlert={false}
              title={fields.moveHelpTitle.value}
              text={<RichText html={fields.moveHelpContent.value} />}
            />
            {errors['root'] && (
              <NotificationBox
                isAlert
                variant="error"
                title={fields.generalErrorNotification?.value.title}
                text={fields.generalErrorNotification?.value.content}
              />
            )}
          </Bucket.Content>
          <Bucket.Footer>
            <Bucket.Actions>
              {!isCurrentAccountReader && (
                <Button type="submit" isLoading={isLoadingSubmit}>
                  {fields.submitButtonText.value}
                </Button>
              )}
              <Button action="secondary" onClick={() => push(fields.cancelLink.value.href)}>
                {fields.cancelLink.value.text}
              </Button>
            </Bucket.Actions>
          </Bucket.Footer>
        </Form>
      </FormProvider>
    </Bucket>
  );
};

export default EditContactData;
