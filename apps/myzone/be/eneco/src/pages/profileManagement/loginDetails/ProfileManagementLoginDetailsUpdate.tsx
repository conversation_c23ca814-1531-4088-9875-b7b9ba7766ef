import { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { FormProvider, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import { emailValidation } from '@common/validation/emailRegex';
import RichText from '@components/RichText/RichText';
import ComponentError from '@components/ui/ComponentError/ComponentError';
import {
  getEnecoBeXapiSiteApiV1Profile,
  putEnecoBeXapiSiteApiV1ProfileLocal,
  PutEnecoBeXapiSiteApiV1ProfileLocalError,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useRouter } from '@dxp-next';
import { useLinkComponent } from '@link';
import { ProfileManagementLoginDetailsUpdateRendering } from '@sitecore/types/ProfileManagementLoginDetailsUpdate';
import {
  Box,
  Button,
  ButtonLink,
  Card,
  Divider,
  Form,
  Heading,
  InputPassword,
  InputText,
  Skeleton,
  Stack,
} from '@sparky';
import { useTracking } from '@tracking';

import {
  NOTIFICATION_LOCAL_LOGIN,
  NOTIFICATION_LOCAL_LOGIN_EMAIL,
  QUERY_PARAMETER_NOTIFICATION,
  QUERY_PARAMETER_SUCCESS,
} from './ProfileManagementLoginDetails';
import DataErrorNotification from '../../../components/DataErrorNotification';

interface FormFields {
  displayName: string;
  emailAddress: string;
  password: string;
}

const ProfileManagementLoginDetailsUpdate: FC<ProfileManagementLoginDetailsUpdateRendering> = ({ fields }) => {
  const formSchema = yup.object({
    displayName: yup
      .string()
      .required(fields.nameFormfield.value.requiredMessage)
      .min(1, fields.nameFormfield.value.requiredMessage)
      .max(25, fields.nameFormfield.value.validationMessage),
    emailAddress: emailValidation({
      requiredMessage: fields?.emailFormField?.value?.requiredMessage,
      emailMessage: fields?.emailFormField?.value?.validationMessage,
    }),
    password: yup.string(),
  });

  const formMethods = useForm<FormFields>({
    resolver: yupResolver(formSchema),
  });

  const {
    formState: { errors },
    setError,
    handleSubmit,
    register,
    setValue,
  } = formMethods;

  const { data: session } = useSession();

  const {
    data,
    isLoading: isGetProfileLoading,
    error: isGetProfileError,
  } = useAuthenticatedDCBE(getEnecoBeXapiSiteApiV1Profile, {}, ['session'], session);

  const getProfileData = unwrapData(data);

  useEffect(() => {
    if (!isGetProfileLoading && getProfileData && !isGetProfileError && !data?.error) {
      setValue('displayName', getProfileData.displayName || '');
      setValue('emailAddress', getProfileData.email || '');
    }
  }, [getProfileData, isGetProfileLoading, isGetProfileError]);

  const router = useRouter();
  const Link = useLinkComponent();
  const { mutate } = useSWRConfig();
  const { trackEventBE } = useTracking();

  const [isPutApiProfileLoading, setIsPutApiProfileLoading] = useState(false);
  const [putApiProfileError, setPutApiProfileError] = useState<PutEnecoBeXapiSiteApiV1ProfileLocalError | null>(null);

  const onSubmit = (data: FormFields) => {
    setIsPutApiProfileLoading(true);
    putEnecoBeXapiSiteApiV1ProfileLocal({
      body: {
        displayName: data.displayName,
        email: data.emailAddress,
        password: data.password,
      },
    })
      .catch(err => {
        setPutApiProfileError(err);
        setIsPutApiProfileLoading(false);
        throw new Error('unexpected behaviour: ' + err);
      })
      .then(({ error, response }) => {
        if (response.ok) {
          let emailAddressChanged = false;
          if (data.emailAddress !== getProfileData?.email) {
            emailAddressChanged = true;
          }

          const notifications = emailAddressChanged
            ? [NOTIFICATION_LOCAL_LOGIN, NOTIFICATION_LOCAL_LOGIN_EMAIL]
            : [NOTIFICATION_LOCAL_LOGIN];
          const redirectUrl = `${fields.actionSubmitSuccessRedirectLink.value.href}?${QUERY_PARAMETER_SUCCESS}=true&${QUERY_PARAMETER_NOTIFICATION}=${notifications.join(',')}`;

          trackEventBE('username_password_change', { status: 'success' });
          mutate('session');
          router.push(redirectUrl.toString());
        } else {
          // @ts-ignore Error type doesn't include Validation key
          const validationErrors = error?.errors?.errors?.Validation as string[];
          if (error && (validationErrors as string[]).length > 0) {
            if (validationErrors.indexOf('UpdateUser.PasswordRequirement') >= 0) {
              setError(
                'password',
                {
                  message: fields.passwordFormfield.value.validationMessage,
                  type: 'value',
                },
                { shouldFocus: true },
              );
              trackEventBE('username_password_change', { status: 'failed', message: 'invalid field validation' });
            }
            if (validationErrors.indexOf('CurrentUser.AccountLink.AliasTooLong') >= 0) {
              setError(
                'displayName',
                {
                  message: fields.nameFormfield.value.validationMessage,
                  type: 'value',
                },
                { shouldFocus: true },
              );
              trackEventBE('username_password_change', { status: 'failed', message: 'alias too long' });
            }
          } else {
            trackEventBE('username_password_change', { status: 'failed', message: 'unexpected error occurred' });
            throw new Error('unexpected behaviour:' + response);
          }
          setIsPutApiProfileLoading(false);
        }
      });
  };

  const showLoading = isGetProfileLoading || !session;
  const showGetProfileError = !showLoading && (isGetProfileError || data?.error);

  if (!showLoading && isGetProfileError !== undefined && data === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <FormProvider {...formMethods}>
      <Form onSubmit={handleSubmit(onSubmit)}>
        <Stack direction="column" gap="6">
          <Card>
            <Stack>
              <Box paddingY="5" paddingX="6">
                <Heading as="h3" size="XS">
                  {fields.pageTitle.value}
                </Heading>
              </Box>
              <Divider />
              <Box paddingY="6" paddingX="6">
                {showGetProfileError && <ComponentError />}
                {putApiProfileError && <ComponentError />}
                {!showGetProfileError && (
                  <Stack direction="column" gap="6">
                    {showLoading ? (
                      <FormSkeletons />
                    ) : (
                      <>
                        <InputText
                          label={fields.nameFormfield.value.label}
                          defaultValue={''}
                          error={errors?.displayName?.message}
                          {...register('displayName')}
                        />
                        <InputText
                          label={fields.emailFormField.value.label}
                          defaultValue={''}
                          error={errors?.emailAddress?.message}
                          hint={<RichText html={fields.emailFormField.value.hint} />}
                          {...register('emailAddress')}
                        />
                        <InputPassword
                          label={fields.passwordFormfield.value.label}
                          autoComplete="new-password"
                          error={errors?.password?.message}
                          hint={<RichText html={fields.passwordFormfield.value.hint} />}
                          {...register('password')}
                        />
                      </>
                    )}
                  </Stack>
                )}
              </Box>

              {!showGetProfileError && (
                <>
                  <Divider />

                  <Box paddingY="6" paddingX="6">
                    <Stack direction="row" gap="5">
                      <Box>
                        <Button type="submit" isLoading={isPutApiProfileLoading}>
                          {fields.actionSubmitLink.value.text}
                        </Button>
                      </Box>
                      <Box>
                        <Link linkType={'internal'} href={fields.actionCancelLink.value.href}>
                          <ButtonLink action="secondary">{fields.actionCancelLink.value.text}</ButtonLink>
                        </Link>
                      </Box>
                    </Stack>
                  </Box>
                </>
              )}
            </Stack>
          </Card>
        </Stack>
      </Form>
    </FormProvider>
  );
};

export default ProfileManagementLoginDetailsUpdate;

const FormSkeletons = () => {
  return (
    <>
      <Skeleton width="100%" height={50} />
      <Skeleton width="100%" height={50} />
      <Skeleton width="100%" height={50} />
    </>
  );
};
