/* eslint-disable no-prototype-builtins */
import { FC, useEffect, useState } from 'react';

import { useApplication } from '@common/application';
import RichText from '@components/RichText/RichText';
import ComponentError from '@components/ui/ComponentError/ComponentError';
import { getEnecoBeXapiSiteApiV1Profile } from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useContent } from '@sitecore/common';
import { ProfileManagementLoginDetailsRendering } from '@sitecore/types/ProfileManagementLoginDetails';
import { NotificationBox, Stack } from '@sparky';
import { useTracking } from '@tracking';

import { LocalLoginCard } from './components/LocalLoginCard';
import DataErrorNotification from '../../../components/DataErrorNotification';
import { ItsmeCard } from '../../../components/ItsmeCard';

export const QUERY_PARAMETER_SUCCESS = 'success';
export const QUERY_PARAMETER_NOTIFICATION = 'notification';
export const QUERY_PARAMETER_ERROR = 'error';

export const NOTIFICATION_LOCAL_LOGIN = 'localLoginUpdated';
export const NOTIFICATION_LOCAL_LOGIN_EMAIL = 'localLoginEmailUpdated';
export const NOTIFICATION_LOCAL_LOGIN_EMAIL_CONFIRMED = 'localLoginEmailConfirmed';

export const ERROR_ITSME_LINKED_TO_OTHER_ACCOUNT = 'OtherUserAlreadyLinked';

export const NOTIFICATION_ITSME_LINK = 'itsmeLoginLinked';
export const NOTIFICATION_ITSME_UNLINK = 'itsmeLoginUnlinked';

const ProfileManagementLoginDetails: FC<ProfileManagementLoginDetailsRendering> = () => {
  const { data: session } = useSession();

  const {
    data,
    isLoading: isGetProfileLoading,
    error: isGetProfileError,
  } = useAuthenticatedDCBE(getEnecoBeXapiSiteApiV1Profile, {}, ['session'], session);

  const getProfileData = unwrapData(data);

  const userDataInit: { displayName: string; email: string; hasLocalLogin: boolean; hasItsmeLogin: boolean } = {
    displayName: '',
    email: '',
    hasLocalLogin: false,
    hasItsmeLogin: false,
  };
  const [userData, setUserData] = useState(userDataInit);

  useEffect(() => {
    if (!isGetProfileLoading && getProfileData) {
      setUserData({
        displayName: getProfileData.displayName || '',
        email: getProfileData?.email || '',
        hasLocalLogin: getProfileData?.hasLocalLogin || false,
        hasItsmeLogin: getProfileData?.hasItsmeLogin || false,
      });
    }
  }, [getProfileData, isGetProfileLoading, isGetProfileError]);

  const itsmeDialogData = {
    queryParameterSuccess: QUERY_PARAMETER_SUCCESS,
    queryParameterNotification: QUERY_PARAMETER_NOTIFICATION,
    valueNotificationUnlink: NOTIFICATION_ITSME_UNLINK,
    valueNotificationLink: NOTIFICATION_ITSME_LINK,
  };

  const showLoading = isGetProfileLoading || !session;
  const showError = !showLoading && (isGetProfileError || data?.error);

  if (!showLoading && isGetProfileError != undefined && data === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Stack direction="column" gap="6">
      <NotificationBanners />
      {!showError && (
        <>
          <ItsmeCard data={userData} dialogData={itsmeDialogData}></ItsmeCard>
          <LocalLoginCard isLoading={showLoading} data={userData}></LocalLoginCard>
        </>
      )}
      {showError && <ComponentError />}
    </Stack>
  );
};

const NotificationBanners: FC = () => {
  const { fields } = useContent<ProfileManagementLoginDetailsRendering>();
  const { searchParams } = useApplication();
  const { trackEventBE } = useTracking();
  const queryParams = Object.fromEntries(searchParams);

  const showNotificationBanner =
    queryParams.hasOwnProperty(QUERY_PARAMETER_SUCCESS) && queryParams.hasOwnProperty(QUERY_PARAMETER_NOTIFICATION);
  const isNotificationBannerSuccess =
    queryParams.hasOwnProperty(QUERY_PARAMETER_SUCCESS) && queryParams[QUERY_PARAMETER_SUCCESS] === 'true';

  const includeLocalLoginUpdatedBanner =
    queryParams.hasOwnProperty(QUERY_PARAMETER_NOTIFICATION) &&
    queryParams[QUERY_PARAMETER_NOTIFICATION].includes(NOTIFICATION_LOCAL_LOGIN);
  const includeLocalLoginEmailUpdatedBanner =
    queryParams.hasOwnProperty(QUERY_PARAMETER_NOTIFICATION) &&
    queryParams[QUERY_PARAMETER_NOTIFICATION].includes(NOTIFICATION_LOCAL_LOGIN_EMAIL);
  const includeLocalLoginEmailConfirmedBanner =
    queryParams.hasOwnProperty(QUERY_PARAMETER_NOTIFICATION) &&
    queryParams[QUERY_PARAMETER_NOTIFICATION].includes(NOTIFICATION_LOCAL_LOGIN_EMAIL_CONFIRMED);

  const includeItsmeLinkedBanner =
    queryParams.hasOwnProperty(QUERY_PARAMETER_NOTIFICATION) &&
    queryParams[QUERY_PARAMETER_NOTIFICATION].includes(NOTIFICATION_ITSME_LINK);
  const includeItsmeUnlinkedBanner =
    queryParams.hasOwnProperty(QUERY_PARAMETER_NOTIFICATION) &&
    queryParams[QUERY_PARAMETER_NOTIFICATION].includes(NOTIFICATION_ITSME_UNLINK);

  const isItsmeLinkedToAnotherAccount =
    queryParams.hasOwnProperty(QUERY_PARAMETER_ERROR) &&
    queryParams[QUERY_PARAMETER_ERROR].includes(ERROR_ITSME_LINKED_TO_OTHER_ACCOUNT);

  const itsmeErrorTitle: string = isItsmeLinkedToAnotherAccount
    ? fields.ItsmeOtherUserAlreadyLinkedNotification?.value.title
    : fields.ItsmeLinkedFailedTitle.value;
  const itsmeErrorBody: string = isItsmeLinkedToAnotherAccount
    ? fields.ItsmeOtherUserAlreadyLinkedNotification?.value.content
    : fields.ItsmeLinkedFailedContent.value;

  useEffect(() => {
    if (isNotificationBannerSuccess) trackEventBE('itsme_linking', { status: 'success' });
    else if (isNotificationBannerSuccess === false) trackEventBE('itsme_linking', { status: 'failed' });
  }, [isNotificationBannerSuccess, trackEventBE]);

  const variant = isNotificationBannerSuccess ? 'success' : 'error';
  if (showNotificationBanner) {
    return (
      <Stack direction={'column'} gap={'2'}>
        {isNotificationBannerSuccess && includeLocalLoginUpdatedBanner && (
          <NotificationBox
            emphasis="low"
            title={fields.localLoginUpdatedSuccessNotification?.value.title}
            text={<RichText html={fields.localLoginUpdatedSuccessNotification?.value.content} />}
            variant={variant}
            isAlert={false}
          />
        )}
        {includeLocalLoginEmailUpdatedBanner && (
          <NotificationBox
            emphasis="low"
            title={
              isNotificationBannerSuccess
                ? fields.localLoginEmailUpdatedSuccessNotification?.value.title
                : fields.localLoginEmailUpdatedFailedNotification?.value.title
            }
            text={
              <RichText
                html={
                  isNotificationBannerSuccess
                    ? fields.localLoginUpdatedSuccessNotification?.value.content
                    : fields.localLoginUpdatedSuccessNotification?.value.content
                }
              />
            }
            variant="warning"
            isAlert={false}
          />
        )}
        {includeLocalLoginEmailConfirmedBanner && (
          <NotificationBox
            emphasis="low"
            title={
              isNotificationBannerSuccess
                ? fields.localLoginEmailConfirmedSuccessNotification?.value.title
                : fields.localLoginEmailConfirmedFailedNotification?.value.title
            }
            text={
              <RichText
                html={
                  isNotificationBannerSuccess
                    ? fields.localLoginEmailConfirmedSuccessNotification?.value.content
                    : fields.localLoginEmailConfirmedFailedNotification?.value.content
                }
              />
            }
            variant={variant}
            isAlert={false}
          />
        )}
        {includeItsmeLinkedBanner && (
          <NotificationBox
            emphasis="low"
            title={isNotificationBannerSuccess ? fields.ItsmeLinkedSuccessTitle.value : itsmeErrorTitle}
            text={
              <RichText html={isNotificationBannerSuccess ? fields.ItsmeLinkedSuccessContent.value : itsmeErrorBody} />
            }
            variant={variant}
            isAlert={false}
          />
        )}
        {includeItsmeUnlinkedBanner && (
          <NotificationBox
            emphasis="low"
            title={
              isNotificationBannerSuccess
                ? fields.ItsmeUnlinkedSuccessNotification?.value.title
                : fields.ItsmeUnlinkedFailedNotification?.value.title
            }
            text={
              <RichText
                html={
                  isNotificationBannerSuccess
                    ? fields.ItsmeUnlinkedSuccessNotification?.value.content
                    : fields.ItsmeUnlinkedFailedNotification?.value.content
                }
              />
            }
            variant={variant}
            isAlert={false}
          />
        )}
      </Stack>
    );
  }
  return null;
};

export default ProfileManagementLoginDetails;
