import { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransfer,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { MoveFileEditAddressRendering } from '@sitecore/types/MoveFileEditAddress';
import { Bucket, Button, ButtonLink, Checkbox, Form, NotificationBox } from '@sparky';
import { NotificationBoxProps } from '@sparky/types';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { Input } from 'libs/sparky/src/components/Input/Input';

import DataErrorNotification from '../../components/DataErrorNotification';
import ReadFormField from '../../components/ReadFormField';
import useBusLabel from '../../hooks/useBusLabel';
import { addressSearchParam } from '../../utils/addSearchParams';
import { isDateNotTooFarInPast, isDateNotTooFarInFuture } from '../../utils/date/move-date-validation';
import { useMove } from '../move/hooks/useMove';
import { addressFormatter } from '../move/overview/address-formatter';

const MoveFileEditAddress: FC = () => {
  const { fields } = useContent<MoveFileEditAddressRendering>();
  const { move, path, moveCacheKey, session, isLoading: isLoadingMove, errorMove } = useMove();
  const isNewAddress = fields?.settings.isNewAddressFlag.value;
  const [error, setError] = useState<NotificationBoxProps | null>();
  const [isApiLoading, setIsApiLoading] = useState(false);
  const { push } = useRouter();
  const busLabel = useBusLabel();
  const {
    data,
    isLoading,
    error: errorAccounts,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
    {
      path: {
        accountNumber: path.accountNumber,
        addressIdentifier: path.addressIdentifier,
      },
    },
    [`/accounts/${path.accountNumber}/delivery-addresses/${path.addressIdentifier}/service-delivery-points`],
    session,
  );

  const serviceDeliveryData = unwrapData(data);

  const { mutate } = useSWRConfig();

  const FormSchema = yup.object({
    keyTransferDateUnknown: yup.boolean(),
    keyTransferDate: yup
      .string()
      .test('is-required', fields.editAddress.keyTransferDateFormField.value.requiredMessage, function (value) {
        const keyTransferDateUnknown = this.parent.keyTransferDateUnknown;
        const reportedVacancy = this.parent.reportedVacancy;
        if (keyTransferDateUnknown && !reportedVacancy) return true;
        if (typeof value !== 'string') return false;
        const date = new Date(value);
        return !isNaN(date.getTime());
      })
      .test('is-date-too-far-in-past', fields.editAddress.datePast28DaysDialog.value.triggerText, (value, context) =>
        isDateNotTooFarInPast(value, context),
      )
      .test('is-date-too-far-in-future', fields.editAddress.dateFuture1YearDialog.value.triggerText, (value, context) =>
        isDateNotTooFarInFuture(value, context),
      ),
    reportedVacancy: yup.boolean(),
    vacancyDate: yup
      .string()
      .test('is-required', fields.editAddress.vacancyDateFormField.value.requiredMessage, (value, context) =>
        isRequired(value, context),
      )
      .test('is-in-range', fields.editAddress.vacancyDateFormField.value.validationMessage, (value, context) => {
        return isNewAddress
          ? isRequired(value, context) && isAfterKeyTransferDate(value, context)
          : isRequired(value, context) && isBetweenTodayAndKeyTransferDate(value, context);
      })
      .test(
        'is-date-too-far-in-future',
        fields.editAddress.vacancyDateFormField.value.validationMaxValueMessage,
        (value, context) =>
          isNewAddress
            ? isRequired(value, context) &&
              isAfterKeyTransferDate(value, context) &&
              isDateNotTooFarInFuture(value, context, '6m')
            : true,
      ),
  });

  type FormValues = yup.InferType<typeof FormSchema>;
  const resolver = yupResolver(FormSchema);

  const defaultDates: { keyTransferDate: string | undefined; vacancyDate: string | undefined } = {
    keyTransferDate: undefined,
    vacancyDate: undefined,
  };

  if (isNewAddress) {
    if (move?.newAddress?.deliveryDate) {
      defaultDates.keyTransferDate = format(move.newAddress.deliveryDate, 'yyyy-MM-dd');
    }
    if (move?.newAddress?.vacancy) {
      defaultDates.vacancyDate = format(move.newAddress.vacancy, 'yyyy-MM-dd');
    }
  } else {
    if (move?.oldAddress?.keyTransfer) {
      defaultDates.keyTransferDate = format(move.oldAddress.keyTransfer, 'yyyy-MM-dd');
    }
    if (move?.oldAddress?.vacancy) {
      defaultDates.vacancyDate = format(move.oldAddress.vacancy, 'yyyy-MM-dd');
    }
  }

  const form = useForm<FormValues>({
    resolver,
    mode: 'onSubmit',
    values: {
      keyTransferDate: defaultDates.keyTransferDate,
      keyTransferDateUnknown: !defaultDates.keyTransferDate,
      reportedVacancy: !!defaultDates.vacancyDate,
      vacancyDate: defaultDates.vacancyDate,
    },
  });

  const {
    register,
    formState: { errors, touchedFields },
    trigger,
    watch,
    setValue,
  } = form;

  const keyTransferDate = watch('keyTransferDate');
  const keyTransferDateUnknown = watch('keyTransferDateUnknown');
  const reportedVacancy = watch('reportedVacancy');

  useEffect(() => {
    if (touchedFields.vacancyDate) {
      trigger('vacancyDate');
    }
  }, [keyTransferDate, reportedVacancy]);

  useEffect(() => {
    if (keyTransferDateUnknown) {
      setValue('keyTransferDate', undefined);
    } else if (touchedFields.keyTransferDate) {
      trigger('keyTransferDate');
    }
  }, [keyTransferDateUnknown]);

  useEffect(() => {
    if (!reportedVacancy) {
      setValue('vacancyDate', undefined);
    } else if (touchedFields.keyTransferDate) {
      trigger('vacancyDate');
    }
  }, [reportedVacancy]);

  useEffect(() => {
    if (keyTransferDate) setValue('keyTransferDateUnknown', false);
  }, [keyTransferDate]);

  const submitForm = async ({ keyTransferDate, keyTransferDateUnknown, reportedVacancy, vacancyDate }: FormValues) => {
    try {
      const { response } =
        await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransfer(
          {
            path: {
              ...path,
              locationType: isNewAddress ? 'New' : 'Old',
            },
            body: {
              keyTransferDate: keyTransferDate || undefined,
              keyTransferDateUnknown,
              reportedVacancy,
              vacancyDate: vacancyDate || undefined,
            },
          },
        );
      if (!response.ok) {
        return setError({
          isAlert: true,
          title: 'error', // fields.editAddress.generalErrorNotification.value.title
          text: 'error', // fields.editAddress.generalErrorNotification.value.content,
          variant: 'error',
        });
      }
      await mutate(moveCacheKey);
      push(fields.editAddress.submitButtonLink.value.href + addressSearchParam(path.addressIdentifier));
    } catch {
      return setError({
        isAlert: true,
        title: 'error', // fields.editAddress.generalErrorNotification.value.title,
        text: 'error', // fields.editAddress.generalErrorNotification.value.content,
        variant: 'error',
      });
    } finally {
      setIsApiLoading(false);
    }
  };

  if (
    !isLoading &&
    !isLoadingMove &&
    ((errorMove !== undefined && move === undefined) || (errorAccounts !== undefined && data === undefined))
  ) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Bucket title={fields.editAddress.title.value}>
      <Form onSubmit={form.handleSubmit(submitForm)}>
        <Bucket.Content>
          <ReadFormField
            label={fields.editAddress.addressLabel.value}
            value={addressFormatter(
              isNewAddress ? move?.newAddress?.newAddress : move?.oldAddress?.oldAddress,
              busLabel,
            )}
          />
          <Input
            label={fields.editAddress.keyTransferDateFormField.value.label}
            id="keyExchangeDate"
            type="date"
            error={errors.keyTransferDate?.message}
            {...register('keyTransferDate')}
            autoComplete="off"
          />
          <Checkbox
            label={fields.editAddress.keyTransferUnknownFormField.value.label}
            hint={<RichText html={fields.editAddress.keyTransferUnknownFormField.value.hint} />}
            {...register('keyTransferDateUnknown')}
          />
          {serviceDeliveryData?.serviceDeliveryPoints?.some(
            serviceDeliveryPoint => serviceDeliveryPoint.invoiceFrequency?.current === 'Yearly',
          ) && (
            <>
              <Checkbox
                label={fields.editAddress.vacancyFormField.value.label}
                hint={<RichText html={fields.editAddress.vacancyFormField.value.hint} />}
                {...register('reportedVacancy')}
              />
              {reportedVacancy && (
                <Input
                  label={fields.editAddress.vacancyDateFormField.value.label}
                  id="vacancyDate"
                  type="date"
                  error={errors.vacancyDate?.message}
                  {...register('vacancyDate')}
                  autoComplete="off"
                  min={new Date().toISOString().split('T')[0]}
                />
              )}
            </>
          )}
          {error && <NotificationBox {...error} />}
        </Bucket.Content>
        <Bucket.Footer>
          <Bucket.Actions>
            <Button isLoading={isApiLoading} type="submit">
              {fields.editAddress.submitButtonLink.value.text}
            </Button>
            <ButtonLink
              action={'secondary'}
              href={fields.editAddress.cancelButtonLink.value.href + addressSearchParam(path.addressIdentifier)}>
              {fields.editAddress.cancelButtonLink.value.text}
            </ButtonLink>
          </Bucket.Actions>
        </Bucket.Footer>
      </Form>
    </Bucket>
  );
};

export default MoveFileEditAddress;

function isRequired(value: string | Date | undefined, context?: yup.TestContext<yup.AnyObject>) {
  const reportedVacancy = context?.parent.reportedVacancy;
  if (!reportedVacancy) return true;
  if (typeof value !== 'string') return false;
  const date = new Date(value);
  return !isNaN(date.getTime());
}

function isBetweenTodayAndKeyTransferDate(value: string | Date | undefined, context?: yup.TestContext<yup.AnyObject>) {
  const { keyTransferDate, reportedVacancy } = context?.parent || {};
  if (!reportedVacancy || !keyTransferDate) return true;
  if (typeof value !== 'string') return false;
  const keyTransferDateValue = new Date(keyTransferDate);
  const vacancyDateValue = new Date(value);
  return keyTransferDateValue >= vacancyDateValue;
}

function isAfterKeyTransferDate(value: string | Date | undefined, context?: yup.TestContext<yup.AnyObject>) {
  const { keyTransferDate, reportedVacancy } = context?.parent || {};
  if (!reportedVacancy || !keyTransferDate) return true;
  if (typeof value !== 'string') return false;
  const keyTransferDateValue = new Date(keyTransferDate);
  const vacancyDateValue = new Date(value);
  return keyTransferDateValue <= vacancyDateValue;
}
