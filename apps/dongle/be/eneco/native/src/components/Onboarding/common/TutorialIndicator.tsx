import React from 'react';

import { Carousel } from '@sparky';

import { useDongleOnboardingContext } from '../context/DongleOnboardingContext';

const TutorialIndicator: React.FC = () => {
  const { currentStep, setCurrentStep } = useDongleOnboardingContext();

  return (
    <Carousel.Indicator
      activeItem={currentStep}
      amount={5}
      hideArrows
      variant="dot"
      visibleItems={1}
      onNavigationClick={i => setCurrentStep(i)}
    />
  );
};

export default TutorialIndicator;
