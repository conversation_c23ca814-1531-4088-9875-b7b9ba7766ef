import { useCallback, useEffect, useState } from 'react';

import { PluginListenerHandle } from '@capacitor/core';
import { <PERSON><PERSON> } from 'smartmeterdongle';

import Logger from '@common/log';
import RichText from '@components/RichText/RichText';
import { useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
import { Bleed, Box, ButtonOverlay, Divider, Heading, Stack, Stretch, Text } from '@sparky';
import { SpinnerIcon, WifiIcon } from '@sparky/icons';

import DongleOnboardingLayout from '../../common/DongleOnboardingLayout';
import DongleOnboardingProgress from '../../common/DongleOnboardingProgress';
import { useDongleOnboardingContext } from '../../context/DongleOnboardingContext';
import { useDongleOnboardingHelper } from '../../utils/dongleOnboardingHelper';
import { Steps, WifiNetwork } from '../../utils/types';

const NetworkSelection = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const { setCurrentStep, dongleInfo, setDongleInfo } = useDongleOnboardingContext();
  const { closeDongleOnboarding } = useDongleOnboardingHelper();
  const [wifiList, setWifiList] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    let networkSearchListener: null | PluginListenerHandle = null;

    const retrieveWifiNetworks = async () => {
      networkSearchListener = await Dongle.addListener('wifiNetworkFound', (wifiNetwork: WifiNetwork) => {
        setWifiList(prev => [...prev, wifiNetwork.name]);

        return;
      });

      try {
        setIsSearching(true);
        Dongle.retrieveWifiNetworks();
      } catch (error) {
        Logger.error('MPVKOt', 'P1 dongle retrieving wifi networks failed', error);
      }
    };

    const stopRetrievingWifiNetworks = async () => {
      await Dongle.stopRetrievingWifiNetworks();
    };

    retrieveWifiNetworks();

    return () => {
      if (networkSearchListener) {
        // make sure dongle stops retrieving wifi networks other wise you might
        // not be able to reconnect until you reset/turn off the dongle
        stopRetrievingWifiNetworks();
        networkSearchListener.remove();
      }
    };
  }, []);

  const selectWifiNetwork = useCallback((ssid: string) => {
    setDongleInfo({ ...dongleInfo, wifiName: ssid });

    setIsSearching(false);
    setCurrentStep(Steps.NETWORK_CONNECTION);
  }, []);

  const handleBackButton = useCallback(() => {
    setCurrentStep(Steps.BLUETOOTH_PAIRING);
  }, []);

  return (
    <DongleOnboardingLayout
      showBackButton={true}
      headerPrevClick={handleBackButton}
      headerMiddleText={fields.connectDongleNavigationTitle.value}
      showDivider={true}
      showFooterPrimaryBtn={false}
      onClose={closeDongleOnboarding}>
      <Bleed top="6">
        <Stack gap="6">
          <DongleOnboardingProgress currentStep={2} />
          <Heading as="h3" size="XS">
            {fields.networkSelectionTitle.value}
          </Heading>
        </Stack>
      </Bleed>

      <Box paddingBottom="12">
        <Stack gap="6">
          <Text size="BodyM">
            <RichText html={fields.networkSelectionContent.value} />
          </Text>
          <Stack gap="6">
            <Box>
              <Divider />
              {wifiList.map((item: string, index) => (
                <ButtonOverlay key={`wifi-${index}`}>
                  <Stretch>
                    <ButtonOverlay.Button onClick={() => selectWifiNetwork(item)}>
                      <Box paddingTop="2">
                        <Stack gap="2" as="div">
                          <Stack direction="row" alignX="justify" alignY="center">
                            <Text size="BodyM" weight="bold">
                              {item}
                            </Text>
                            <WifiIcon size="small" />
                          </Stack>
                          <Divider />
                        </Stack>
                      </Box>
                    </ButtonOverlay.Button>
                  </Stretch>
                </ButtonOverlay>
              ))}
            </Box>

            {isSearching && (
              <Stack direction="row" gap="2" alignX="justify">
                <Text size="BodyM" color="textLowEmphasis">
                  {fields.networkSelectionSearchingText.value}
                </Text>
                <SpinnerIcon size="small" />
              </Stack>
            )}
          </Stack>
        </Stack>
      </Box>
    </DongleOnboardingLayout>
  );
};

export default NetworkSelection;
