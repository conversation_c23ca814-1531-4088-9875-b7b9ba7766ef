import RichText from '@components/RichText/RichText';
import { mapImage, useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
import { Box, Heading, Image, Stack } from '@sparky';

import DongleOnboardingLayout from '../common/DongleOnboardingLayout';
import { useDongleOnboardingHelper } from '../utils/dongleOnboardingHelper';

const Success = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const { closeDongleOnboarding } = useDongleOnboardingHelper();

  const imageProps = mapImage(fields.successImage);

  return (
    <DongleOnboardingLayout
      showBackButton={true}
      headerMiddleText=""
      showDivider={false}
      footerPrimaryBtnText={fields.successButtonLink.value.text}
      footerPrimaryBtnHref={fields.successButtonLink.value.href}
      onClose={closeDongleOnboarding}>
      <Box paddingTop="16">
        <Stack gap="8">
          {imageProps.src && (
            <Image
              src={imageProps.src}
              alt={imageProps.alt}
              aspectRatio={imageProps.aspectRatio}
              width="auto"
              height="250px"
              display="block"
              objectFit="contain"
            />
          )}

          <Heading as="h1" size="S">
            {fields.successTitle.value}
          </Heading>
        </Stack>
        <Box paddingY="4">
          <RichText html={fields.successContent.value} />
        </Box>
      </Box>
    </DongleOnboardingLayout>
  );
};

export default Success;
