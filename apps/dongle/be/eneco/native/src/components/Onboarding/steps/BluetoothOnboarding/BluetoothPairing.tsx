import { useEffect, useState } from 'react';

import { <PERSON><PERSON> } from 'smartmeterdongle';

import Logger from '@common/log';
import { useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
import { Bleed, Box, Heading, Stack, Stretch } from '@sparky';
import { CheckIcon, SpinnerIcon } from '@sparky/icons';

import DongleOnboardingLayout from '../../common/DongleOnboardingLayout';
import DongleOnboardingProgress from '../../common/DongleOnboardingProgress';
import { useDongleOnboardingContext } from '../../context/DongleOnboardingContext';
import { useDongleOnboardingHelper } from '../../utils/dongleOnboardingHelper';
import { Steps } from '../../utils/types';

const BluetoothPairing = () => {
  const { setCurrentStep, setDongleInfo } = useDongleOnboardingContext();
  const { closeDongleOnboarding } = useDongleOnboardingHelper();
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const [bluetoothConnected, setBluetoothConnected] = useState(false);

  useEffect(() => {
    const initDongle = async () => {
      try {
        const setupDongle = await Dongle.initialize();

        if (setupDongle.enabled) {
          const checkPermissions = await Dongle.checkPermissions();

          if (checkPermissions.bluetooth !== 'granted') {
            await Dongle.requestPermissions();
          }

          const connectDongle = await Dongle.connect();

          if (connectDongle.result) {
            const detailsDongle = await Dongle.getDongleDetails();
            setDongleInfo({
              macAddress: detailsDongle.id,
              firmwareVersion: detailsDongle.fw,
            });
            setBluetoothConnected(true);
            return;
          }
        }
      } catch (error) {
        Logger.error('Tymc4C', 'P1 dongle bluetooth pairing error', error);
      }

      setCurrentStep(Steps.BLUETOOTH_FAILED);
    };

    initDongle();
  }, []);

  return (
    <DongleOnboardingLayout
      showBackButton={true}
      showDivider={true}
      footerPrimaryBtnText={fields.bluetoothPairingProceedButtonLabel.value}
      footerPrimaryBtnClick={() => setCurrentStep(Steps.NETWORK_SELECTION)}
      showFooterPrimaryBtn={bluetoothConnected}
      headerMiddleText={fields.connectDongleNavigationTitle.value}
      headerPrevClick={() => setCurrentStep(Steps.BLUETOOTH_TUTORIAL)}
      onClose={closeDongleOnboarding}>
      <Bleed top="6">
        <Stack gap="6">
          <DongleOnboardingProgress currentStep={1} />
          <Heading as="h3" size="XS">
            {bluetoothConnected ? fields.bluetoothPairingSuccessLabel.value : fields.bluetoothPairingOngoingLabel.value}
          </Heading>
        </Stack>
      </Bleed>

      <Stretch height>
        <Stack alignX="center" alignY="center">
          {bluetoothConnected ? (
            <CheckIcon size="extraLarge" color="feedbackSuccess" />
          ) : (
            <Box paddingBottom="16">
              <SpinnerIcon size="large" />
            </Box>
          )}
        </Stack>
      </Stretch>
    </DongleOnboardingLayout>
  );
};

export default BluetoothPairing;
