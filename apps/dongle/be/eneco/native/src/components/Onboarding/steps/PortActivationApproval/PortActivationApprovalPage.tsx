import React from 'react';

import RichText from '@components/RichText/RichText';
import { mapImage, useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
import { Box, Heading, Image, Stack, Stretch } from '@sparky';

import DongleOnboardingLayout from '../../common/DongleOnboardingLayout';
import TutorialIndicator from '../../common/TutorialIndicator';
import { useDongleOnboardingContext } from '../../context/DongleOnboardingContext';
import { useDongleOnboardingHelper } from '../../utils/dongleOnboardingHelper';
import { Steps } from '../../utils/types';

const PortActivationApprovalPage = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const { setCurrentStep } = useDongleOnboardingContext();
  const { closeOpenPortInstructions } = useDongleOnboardingHelper();

  const imageProps = mapImage(fields.portActivationApprovalImage);

  return (
    <DongleOnboardingLayout
      showDivider={false}
      showBackButton
      showFooterSecondaryBtn
      headerPrevText={fields.headerPreviousText.value}
      headerPrevClick={() => setCurrentStep(Steps.WAITING_SCREEN)}
      footerPrimaryBtnText={fields.portActivationApprovalPrimaryButtonText.value}
      footerPrimaryBtnClick={() => setCurrentStep(Steps.PORT_ACTIVATION_SUCCESS)}
      onClose={closeOpenPortInstructions}>
      <Stretch height>
        <Stack alignX="center" gap="8">
          {imageProps.src && (
            <Image
              alt={imageProps.alt}
              src={imageProps.src}
              aspectRatio={imageProps.aspectRatio}
              width="100%"
              height="auto"
              display="block"
              objectFit="contain"
            />
          )}
          <TutorialIndicator />
          <Box>
            <Stack gap="3">
              <Stack.Item>
                <Heading as="h3" size="S">
                  {fields.portActivationApprovalTitle.value}
                </Heading>
              </Stack.Item>
              <Stack.Item>
                <RichText html={fields.portActivationApprovalContent.value} />
              </Stack.Item>
            </Stack>
          </Box>
        </Stack>
      </Stretch>
    </DongleOnboardingLayout>
  );
};

export default PortActivationApprovalPage;
