import React, { useState } from 'react';

import RichText from '@components/RichText/RichText';
import { mapImage, useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
// eslint-disable-next-line dxp-rules/no-dialog-from-sparky
import { Box, Button, Dialog, Heading, Image, Stack, Stretch, TextLink } from '@sparky';

import DongleOnboardingLayout from '../../common/DongleOnboardingLayout';
import TutorialIndicator from '../../common/TutorialIndicator';
import { useDongleOnboardingContext } from '../../context/DongleOnboardingContext';
import { useDongleOnboardingHelper } from '../../utils/dongleOnboardingHelper';
import { Steps } from '../../utils/types';

const IntroductionPage = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const { setCurrentStep } = useDongleOnboardingContext();
  const { closeOpenPortInstructions, isAfterPurchase } = useDongleOnboardingHelper();
  const [openDialog, setOpenDialog] = useState<boolean>(false);

  const imageProps = mapImage(fields.getStartedDongleImage);

  return (
    <DongleOnboardingLayout
      showBackButton={false}
      showDivider={false}
      onClose={closeOpenPortInstructions}
      showFooterSecondaryBtn
      footerPrimaryBtnText={fields.getStartedPrimaryButtonText.value}
      footerPrimaryBtnClick={() => setCurrentStep(Steps.PORT_ACTIVATION_INSTRUCTIONS)}
      footerSecondaryBtnText={fields.getStartedSecondaryButtonText.value}
      footerSecondaryBtnClick={() => setCurrentStep(Steps.PORT_ACTIVATION_SUCCESS)}>
      <Stretch height>
        <Stack alignX="center" gap="8">
          {imageProps.src && (
            <Image
              alt={imageProps.alt}
              src={imageProps.src}
              aspectRatio={imageProps.aspectRatio}
              width="100%"
              height="auto"
              display="block"
              objectFit="contain"
            />
          )}
          <TutorialIndicator />
          <Box>
            <Stack gap="3">
              <Stack.Item>
                <Heading as="h3" size="S">
                  {isAfterPurchase
                    ? fields.getStartedAfterPurchaseTitle.value
                    : fields.getStartedAfterDeliveryTitle.value}
                </Heading>
              </Stack.Item>
              <Stack.Item>
                <RichText
                  html={fields.getStartedContent.value}
                  replacements={{
                    a: props => (
                      <Dialog
                        title={fields.getStartedNotification.value.title}
                        trigger={<TextLink>{props.children}</TextLink>}
                        fullscreen
                        isOpen={openDialog}
                        setOpen={setOpenDialog}>
                        <Stack gap="4">
                          <RichText
                            html={fields?.getStartedNotification?.value.content}
                            replacements={{
                              button: props => (
                                <Button action="secondary" size="compact" onClick={() => setOpenDialog(false)}>
                                  {props.children}
                                </Button>
                              ),
                            }}
                          />
                        </Stack>
                      </Dialog>
                    ),
                  }}
                />
              </Stack.Item>
            </Stack>
          </Box>
        </Stack>
      </Stretch>
    </DongleOnboardingLayout>
  );
};
export default IntroductionPage;
