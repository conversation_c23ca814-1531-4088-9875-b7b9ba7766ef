import { FC } from 'react';

import { DongleOnboardingProvider, useDongleOnboardingContext } from './context/DongleOnboardingContext';
import BluetoothFailed from './steps/BluetoothOnboarding/BluetoothFailed';
import BluetoothPairing from './steps/BluetoothOnboarding/BluetoothPairing';
import BluetoothTutorial from './steps/BluetoothOnboarding/BluetoothTutorial';
import Error from './steps/Error';
import NetworkConnection from './steps/Network/NetworkConnection';
import NetworkSelection from './steps/Network/NetworkSelection';
import PortActivationApprovalPage from './steps/PortActivationApproval/PortActivationApprovalPage';
import InstructionPage from './steps/PortActivationInstruction/InstructionPage';
import IntroductionPage from './steps/PortActivationIntroduction/IntroductionPage';
import PortActivationSuccess from './steps/PortActivationSuccess/PortActivationSuccess';
import WaitingScreenPage from './steps/PortActivationWaitingScreen/WaitingScreenPage';
import Success from './steps/Success';
import { Steps } from './utils/types';

const DongleOnboardingFlow: FC = () => {
  return (
    <DongleOnboardingProvider>
      <DongleOnboarding />
    </DongleOnboardingProvider>
  );
};

const DongleOnboarding = () => {
  const { currentStep } = useDongleOnboardingContext();

  switch (currentStep) {
    case Steps.PORT_ACTIVATION_INTRODUCTION:
      return <IntroductionPage />;
    case Steps.PORT_ACTIVATION_INSTRUCTIONS:
      return <InstructionPage />;
    case Steps.WAITING_SCREEN:
      return <WaitingScreenPage />;
    case Steps.PORT_ACTIVATION_APPROVAL:
      return <PortActivationApprovalPage />;
    case Steps.PORT_ACTIVATION_SUCCESS:
      return <PortActivationSuccess />;
    case Steps.BLUETOOTH_TUTORIAL:
      return <BluetoothTutorial />;
    case Steps.BLUETOOTH_PAIRING:
      return <BluetoothPairing />;
    case Steps.BLUETOOTH_FAILED:
      return <BluetoothFailed />;
    case Steps.NETWORK_SELECTION:
      return <NetworkSelection />;
    case Steps.NETWORK_CONNECTION:
      return <NetworkConnection />;
    case Steps.SUCCESS:
      return <Success />;
    case Steps.ERROR:
    default:
      return <Error />;
  }
};

export default DongleOnboardingFlow;
