import React from 'react';

import { Insights_BelgiumExternalMandates_ExternalMandateBe } from '@monorepo-types/dc';
import ListTileItem from '@native-components/components/ListTileItem';
import { Card } from '@sparky';
import { DuplicateIcon, ElectricityIcon } from '@sparky/icons';

import { copyEanToClipboard } from '../utils/helpers';

type Props = {
  item: Insights_BelgiumExternalMandates_ExternalMandateBe;
};

const MandateItemTile: React.FC<Props> = ({ item }) => {
  return (
    <Card key={item.eanCode}>
      <ListTileItem
        categoryIcon={<ElectricityIcon color="iconElectricity" />}
        label="Electriciteit"
        lowerLabel={`EAN ${item.eanCode}`}
        actionIcon={<DuplicateIcon color="iconSecondary" />}
        onClick={() => copyEanToClipboard(item.eanCode)}
        hasDivider={false}
      />
    </Card>
  );
};

export default MandateItemTile;
