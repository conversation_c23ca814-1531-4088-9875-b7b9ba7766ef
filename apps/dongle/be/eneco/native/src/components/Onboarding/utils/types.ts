export enum Steps {
  PORT_ACTIVATION_INTRODUCTION,
  PORT_ACTIVATION_INSTRUCTIONS,
  WAITING_SCREEN,
  PORT_ACTIVATION_APPROVAL,
  PORT_ACTIVATION_SUCCESS,
  BLUETOOTH_TUTORIAL,
  BLUETOOTH_PAIRING,
  B<PERSON><PERSON>TOOTH_FAILED,
  NETWORK_SELECTION,
  NETWORK_CONNECTION,
  SUCCESS,
  ERROR,
}

export type DongleProps = {
  wifiName?: string;
  macAddress?: string;
  firmwareVersion?: string;
};

export type WifiNetwork = {
  name: string;
  wifiConnectionState: 'Connecting' | 'Succeeded' | 'AuthenticationFailed' | 'Timeout' | 'Failed';
};
