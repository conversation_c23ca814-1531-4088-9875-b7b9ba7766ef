import React, { useState } from 'react';

import RichText from '@components/RichText/RichText';
import { useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
// eslint-disable-next-line dxp-rules/no-dialog-from-sparky
import { Bleed, Button, Dialog, Heading, Stack, Stretch } from '@sparky';

import BluetoothOnboardingStepList from './BluetoothOnboardingStepList';
import DongleOnboardingLayout from '../../common/DongleOnboardingLayout';
import DongleOnboardingProgress from '../../common/DongleOnboardingProgress';
import { useDongleOnboardingContext } from '../../context/DongleOnboardingContext';
import { useDongleOnboardingHelper } from '../../utils/dongleOnboardingHelper';
import { Steps } from '../../utils/types';

const BluetoothTutorial = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const { setCurrentStep } = useDongleOnboardingContext();
  const [isOpen, setOpen] = useState(false);
  const { closeDongleOnboarding } = useDongleOnboardingHelper();

  return (
    <DongleOnboardingLayout
      showBackButton={true}
      showDivider={true}
      footerPrimaryBtnText={fields.connectDongleButtonLabel.value}
      footerPrimaryBtnClick={() => setCurrentStep(Steps.BLUETOOTH_PAIRING)}
      headerMiddleText={fields.connectDongleNavigationTitle.value}
      headerPrevClick={() => setCurrentStep(Steps.PORT_ACTIVATION_SUCCESS)}
      onClose={closeDongleOnboarding}>
      <Bleed top="6">
        <Stack gap="6">
          <DongleOnboardingProgress currentStep={1} />
          <Heading as="h3" size="XS">
            {fields.bluetoothTutorialTitle.value}
          </Heading>
          <RichText html={fields.bluetoothTutorialContent.value} />
          <BluetoothOnboardingStepList setOpen={setOpen} />
        </Stack>
      </Bleed>
      <Dialog
        onClose={() => setOpen(false)}
        setOpen={setOpen}
        trigger={null}
        isOpen={isOpen}
        title={fields.bluetoothTutorialDialog.value.title}
        description={<RichText html={fields.bluetoothTutorialDialog.value.content} />}>
        <Stretch>
          <Button size="compact" onClick={() => setOpen(false)} action="secondary">
            {fields.bluetoothTutorialDialog.value.submitButtonText}
          </Button>
        </Stretch>
      </Dialog>
    </DongleOnboardingLayout>
  );
};

export default BluetoothTutorial;
