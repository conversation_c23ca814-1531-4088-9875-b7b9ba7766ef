import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { useUsagesGetShortUrlAndExternalMandate } from '@dc/hooks';
import renderApp from '@jest-tools/renderApp';
import dongleOnboarding from '@mocks/sitecore/apps/dongle/dongleOnboarding';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/';

import DongleOnboardingFlow from './DongleOnboardingFlow';

const { fields } = dongleOnboarding;

jest.mock('@common/application', () => ({
  ...jest.requireActual('@common/application'),
  useApplication: jest.fn().mockReturnValue({
    locale: 'nl-BE',
    locales: ['nl-BE'],
    language: 'nl',
    languages: ['nl'],
    searchParams: new URLSearchParams(),
    isEditMode: false,
  }),
}));

// Mock the Dongle
jest.mock('smartmeterdongle', () => ({
  ...jest.requireActual('smartmeterdongle'),
  Dongle: {
    initialize: jest.fn().mockResolvedValue({
      enabled: true,
      permissions: {
        bluetooth: 'granted',
      },
    }),
    checkPermissions: jest.fn().mockResolvedValue({
      bluetooth: 'granted',
    }),
    requestPermissions: jest.fn().mockResolvedValue({
      bluetooth: 'granted',
    }),
    getDongleDetails: jest.fn().mockResolvedValue({
      id: '123456789',
      fw: '1.0.0',
    }),
    connect: jest.fn().mockResolvedValue({
      result: true,
      dongleId: '123456789',
    }),
    addListener: jest.fn().mockImplementation((event, callback) => {
      if (event === 'wifiNetworkFound') {
        // Simulate an event being triggered
        setTimeout(() => callback({ name: 'TestNetwork1' }), 100);
      }
      return {
        remove: jest.fn(),
      };
    }),
    retrieveWifiNetworks: jest.fn().mockResolvedValue(null),
    stopRetrievingWifiNetworks: jest.fn().mockResolvedValue(null),
  },
}));

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useUsagesGetShortUrlAndExternalMandate: jest.fn().mockReturnValue({
    send: jest.fn(),
    data: {
      status: 'unknown',
      hasUnmappedMandatesFromFluvius: false,
      externalMandates: [
        {
          eanCode: '541448860003032996',
          productType: 'electricity',
          status: 'requested',
          sources: ['SapSiebel', 'Fluvius'],
          dayUsageState: true,
          quarterHourUsageState: false,
        },
        {
          eanCode: '541448860003032992',
          productType: 'gas',
          status: 'requested',
          sources: ['SapSiebel'],
          dayUsageState: true,
          quarterHourUsageState: true,
        },
      ],
      shortUrl: {
        url: 'https://mijn.fluvius.be/verbruik/dienstverlener?id=950debc3a98b67b8bd80f3589899ba98068c556fbecc0839d35a2ba068ae7993',
        validTo: '2023-09-20 11:52:15',
      },
    },
  }) as unknown as typeof useUsagesGetShortUrlAndExternalMandate,
}));

describe('DongleOnboardingFlow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render', async () => {
    await renderApp(DongleOnboardingFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/p1-dongle-onboarding',
    });

    // Test text on the Introduction screen
    expect(screen.getByText(fields.getStartedPrimaryButtonText.value, { exact: true })).toBeInTheDocument();
  });

  it('should render the the correct page when proceeding through the onboarding flow', async () => {
    const user = userEvent.setup();

    renderApp(DongleOnboardingFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/p1-dongle-onboarding',
    });

    const getStartedButton = screen.getByRole('button', {
      name: fields.getStartedPrimaryButtonText.value,
    });
    await user.click(getStartedButton);

    expect(screen.getByText(fields.fluviusInstructionTitle.value, { exact: true })).toBeInTheDocument();

    const toFluviusButton = screen.getByRole('button', {
      name: fields.fluviusInstructionContinueText.value,
    });
    await user.click(toFluviusButton);

    expect(screen.getByText(fields.waitingScreenTitle.value, { exact: true })).toBeInTheDocument();

    const waitingScreenButton = screen.getByRole('button', {
      name: fields.waitingScreenPrimaryButtonText.value,
    });

    await user.click(waitingScreenButton);

    expect(screen.getByText(fields.portActivationApprovalTitle.value, { exact: true })).toBeInTheDocument();

    const portActivationApprovalButton = screen.getByRole('button', {
      name: fields.portActivationApprovalPrimaryButtonText.value,
    });
    await user.click(portActivationApprovalButton);

    expect(screen.getByText(fields.portActivationSuccessPrimaryButtonText.value)).toBeInTheDocument();
  });

  it('should render the electricity mandate tile in the PORT_ACTIVATION_INSTRUCTIONS page', async () => {
    const user = userEvent.setup();

    renderApp(DongleOnboardingFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/p1-dongle-onboarding',
    });

    const getStartedButton = screen.getByRole('button', {
      name: fields.getStartedPrimaryButtonText.value,
    });
    await user.click(getStartedButton);

    const electricityMandate = useUsagesGetShortUrlAndExternalMandate()?.data?.externalMandates?.find(
      mandate => mandate.productType === 'electricity',
    );

    expect(screen.getByText('EAN ' + electricityMandate?.eanCode)).toBeInTheDocument();
  });

  it('should be able to connect the dongle to bluetooth and wifi', async () => {
    const user = userEvent.setup();

    await renderApp(DongleOnboardingFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/p1-dongle-onboarding',
    });

    // Proceed to bluetooth introduction screen
    const getStartedButton = screen.getByRole('button', {
      name: fields.getStartedPrimaryButtonText.value,
    });
    expect(getStartedButton).toBeInTheDocument();

    await user.click(getStartedButton);

    const toFluviusButton = screen.getByRole('button', {
      name: fields.fluviusInstructionContinueText.value,
    });

    await user.click(toFluviusButton);

    const waitingScreenButton = screen.getByRole('button', {
      name: fields.waitingScreenPrimaryButtonText.value,
    });

    await user.click(waitingScreenButton);

    const portActivationApprovalButton = screen.getByRole('button', {
      name: fields.portActivationApprovalPrimaryButtonText.value,
    });

    await user.click(portActivationApprovalButton);

    const portActivationSuccessButton = screen.getByRole('button', {
      name: fields.portActivationSuccessPrimaryButtonText.value,
    });

    await user.click(portActivationSuccessButton);

    // TODO unsure if the current flow let you go to the bluetooth introduction screen directly..

    // User should see the bluetooth introduction screen
    expect(screen.getByText(fields.bluetoothTutorialTitle.value, { exact: true })).toBeInTheDocument();
    expect(screen.getByText(fields.bluetoothTutorialContent.value, { exact: true })).toBeInTheDocument();

    const popupLink = screen.getByRole('button', {
      name: 'P1-poort van je digitale meter',
    });
    expect(popupLink).toBeInTheDocument();

    await user.click(popupLink);

    // User should see the popup with the information about the P1 port
    expect(screen.getByText(fields.bluetoothTutorialDialog.value.title, { exact: true })).toBeInTheDocument();

    const understandButton = screen.getByRole('button', {
      name: fields.bluetoothTutorialDialog.value.submitButtonText as string,
    });
    expect(understandButton).toBeInTheDocument();

    await user.click(understandButton);

    // User should able to advance to the bluetooth pairing screen
    const connectDongleButton = screen.getByRole('button', {
      name: fields.connectDongleButtonLabel.value,
    });
    expect(connectDongleButton).toBeInTheDocument();

    await connectDongleButton.click();

    // User should see the bluetooth pairing screen
    expect(screen.getByText(fields.progressIndicatorConnectBluetoothLabel.value, { exact: true })).toBeInTheDocument();

    expect(screen.getByText(fields.bluetoothPairingOngoingLabel.value, { exact: true })).toBeInTheDocument();

    expect(await screen.findByText(fields.bluetoothPairingSuccessLabel.value, { exact: true })).toBeInTheDocument();

    const proceedButton = screen.getByRole('button', {
      name: fields.bluetoothPairingProceedButtonLabel.value,
    });
    expect(proceedButton).toBeInTheDocument();

    await user.click(proceedButton);

    // User should see the network selection screen
    expect(screen.getByText(fields.networkSelectionTitle.value, { exact: true })).toBeInTheDocument();

    expect(screen.getByText(fields.networkSelectionSearchingText.value, { exact: true })).toBeInTheDocument();

    const wifiButton = await screen.findByRole('button', {
      name: 'TestNetwork1',
    });
    expect(wifiButton).toBeInTheDocument();

    await user.click(wifiButton);

    // User should see the network connection screen
    const passwordInput = screen.getByLabelText('Wachtwoord voor TestNetwork1');
    expect(passwordInput).toBeInTheDocument();

    await user.type(passwordInput, 'password123');

    expect(screen.getByText(fields.networkConnectionConnectButtonText.value, { exact: true })).toBeInTheDocument();
  });
});
