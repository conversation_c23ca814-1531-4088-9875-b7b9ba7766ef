import RichText from '@components/RichText/RichText';
import NativeBottomNavigationWrapper from '@custom-components/native/NativeBottomNavigationWrapper';
import { useLinkComponent } from '@link';
import { useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
import { Box, Button, ButtonLink, Heading, Image, Stack } from '@sparky';

import DongleOnboardingLayout from '../common/DongleOnboardingLayout';
import { useDongleOnboardingContext } from '../context/DongleOnboardingContext';
import { useDongleOnboardingHelper } from '../utils/dongleOnboardingHelper';
import { Steps } from '../utils/types';

const Error = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const Link = useLinkComponent();
  const { setCurrentStep } = useDongleOnboardingContext();
  const { closeDongleOnboarding } = useDongleOnboardingHelper();

  return (
    <DongleOnboardingLayout
      showBackButton={false}
      headerMiddleText={fields.connectDongleNavigationTitle.value}
      showDivider={false}
      showFooterPrimaryBtn={false}
      onClose={closeDongleOnboarding}>
      <Box paddingTop="16">
        <Stack gap="8">
          <Image
            src="{imageProps.src}"
            alt="{imageProps.alt}"
            width="auto"
            height="250px"
            display="block"
            objectFit="contain"
          />

          <Heading as="h1" size="S">
            {fields.errorTitle.value}
          </Heading>
        </Stack>
        <Box paddingY="4">
          <RichText html={fields.errorContent.value} />
        </Box>
      </Box>
      <NativeBottomNavigationWrapper>
        <Stack direction="column" gap="4">
          <Button
            onClick={() => {
              setCurrentStep(Steps.BLUETOOTH_TUTORIAL);
            }}
            size="compact">
            {fields.errorRetryButtonText.value}
          </Button>

          <Link href={fields.errorReportProblemLink.value.href} linkType="internal">
            <ButtonLink size="compact" action="secondary">
              {fields.errorReportProblemLink.value.text}
            </ButtonLink>
          </Link>
        </Stack>
      </NativeBottomNavigationWrapper>
    </DongleOnboardingLayout>
  );
};

export default Error;
