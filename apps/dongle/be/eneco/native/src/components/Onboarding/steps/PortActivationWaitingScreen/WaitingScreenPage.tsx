import React, { useCallback } from 'react';

import { InsightsNativePaths } from '@native-components/constants/paths';
import { openSystemBrowser } from '@native-components/helpers/inAppBrowser';
import { useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
import { Heading, Stack, StepList, Stretch } from '@sparky';

import { useDongleOnboardingHelper } from '../..//utils/dongleOnboardingHelper';
import DongleOnboardingLayout from '../../common/DongleOnboardingLayout';
import MandateItemTile from '../../common/MandateItemTile';
import { useDongleOnboardingContext } from '../../context/DongleOnboardingContext';
import { Steps } from '../../utils/types';

const { FLUVIUS_PORT_CONFIG_URL } = InsightsNativePaths;

const WaitingScreenPage = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const { setCurrentStep, isLoadingMandateData, mandateData } = useDongleOnboardingContext();
  const { closeOpenPortInstructions } = useDongleOnboardingHelper();

  const openFluviusLink = useCallback(() => {
    openSystemBrowser(FLUVIUS_PORT_CONFIG_URL);
  }, []);

  return (
    <DongleOnboardingLayout
      showDivider={false}
      showBackButton
      showFooterSecondaryBtn
      headerPrevText={fields.headerPreviousText.value}
      headerPrevClick={() => setCurrentStep(Steps.PORT_ACTIVATION_INSTRUCTIONS)}
      footerSecondaryBtnText={fields.waitingScreenSecondaryButtonText.value}
      footerSecondaryBtnClick={openFluviusLink}
      footerPrimaryBtnText={fields.waitingScreenPrimaryButtonText.value}
      footerPrimaryBtnClick={() => setCurrentStep(Steps.PORT_ACTIVATION_APPROVAL)}
      onClose={closeOpenPortInstructions}>
      <Stretch height>
        <Stack gap="8">
          <Stack.Item>
            <Heading as="h3" size="S">
              {fields.waitingScreenTitle.value}
            </Heading>
          </Stack.Item>
          <Stack.Item>
            <StepList columnGap={4}>
              <StepList.Item>{fields.waitingScreenStepListOneText.value}</StepList.Item>
              <StepList.Item>{fields.waitingScreenStepListTwoText.value}</StepList.Item>
              <StepList.Item>{fields.waitingScreenStepListThreeText.value}</StepList.Item>
              <StepList.Item>{fields.waitingScreenStepListFourText.value}</StepList.Item>
              <StepList.Item>{fields.waitingScreenStepListFiveText.value}</StepList.Item>
              <StepList.Item>{fields.waitingScreenStepListSixText.value}</StepList.Item>
            </StepList>
          </Stack.Item>
        </Stack>
        <Stack.Item>
          {!isLoadingMandateData &&
            mandateData?.externalMandates?.map(item => {
              if (item.productType === 'electricity') {
                return <MandateItemTile key={item.eanCode} item={item} />;
              }
              return null;
            })}
        </Stack.Item>
      </Stretch>
    </DongleOnboardingLayout>
  );
};
export default WaitingScreenPage;
