import { useEffect, useState } from 'react';

import { PluginListenerHandle } from '@capacitor/core';
import { <PERSON><PERSON> } from 'smartmeterdongle';

import Logger from '@common/log';
import NativeBottomNavigationWrapper from '@custom-components/native/NativeBottomNavigationWrapper';
import { useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
import { Bleed, Box, Button, Heading, InputPassword, Stack } from '@sparky';

import DongleOnboardingLayout from '../../common/DongleOnboardingLayout';
import DongleOnboardingProgress from '../../common/DongleOnboardingProgress';
import { useDongleOnboardingContext } from '../../context/DongleOnboardingContext';
import { useDongleOnboardingHelper } from '../../utils/dongleOnboardingHelper';
import { Steps, WifiNetwork } from '../../utils/types';

const NetworkConnection = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  //const { send, isError, isSuccess } = useDongleAgreementPairDongle();
  const { setCurrentStep, dongleInfo } = useDongleOnboardingContext();
  const { closeDongleOnboarding } = useDongleOnboardingHelper();
  const [connectWifiFail, setConnectWifiFail] = useState(false);
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    let networkConnectionListener: null | PluginListenerHandle = null;

    const initNetworkConnection = async () => {
      networkConnectionListener = await Dongle.addListener('wifiConnectionStateChanged', (wifiNetwork: WifiNetwork) => {
        switch (wifiNetwork.wifiConnectionState) {
          case 'Connecting':
            break;
          case 'Succeeded':
            // TODO: Pair dongle endpoint implementation
            // send({
            //   dongleMacAddress: dongleInfo?.macAddress,
            //   dongleFirmwareVersion: dongleInfo?.firmwareVersion,
            //   dongleWifiName: dongleInfo?.wifiName,
            // });

            setIsLoading(false);

            setCurrentStep(Steps.SUCCESS);
            break;
          case 'AuthenticationFailed':
            setConnectWifiFail(true);
            setIsLoading(false);
            break;
          case 'Timeout':
          case 'Failed':
          default:
            setIsLoading(false);
            setCurrentStep(Steps.ERROR);
        }
      });
    };

    initNetworkConnection();

    return () => {
      if (networkConnectionListener) {
        networkConnectionListener.remove();
      }
    };
  }, []);

  // useEffect(() => {
  //   if (isError) {
  //     setIsLoading(false);
  //     setCurrentStep(Steps.ERROR);
  //   }

  //   if (isSuccess) {
  //     setIsLoading(false);
  //     setCurrentStep(Steps.SUCCESS);
  //   }
  // }, [isError, isSuccess]);

  const connectDongle = async () => {
    if (!dongleInfo?.wifiName) {
      setCurrentStep(Steps.ERROR);
      return;
    }

    try {
      setIsLoading(true);
      await Dongle.connectToWifi({
        ssid: dongleInfo.wifiName,
        password: password,
      });

      return;
    } catch (error) {
      Logger.error('FYa9Jj', 'P1 dongle wifi connection failed', error);
    }

    setCurrentStep(Steps.ERROR);
  };

  if (!dongleInfo?.wifiName) {
    return;
  }

  return (
    <DongleOnboardingLayout
      showBackButton={true}
      headerPrevClick={() => setCurrentStep(Steps.NETWORK_SELECTION)}
      headerMiddleText={fields.connectDongleNavigationTitle.value}
      showDivider={true}
      showFooterPrimaryBtn={false}
      onClose={closeDongleOnboarding}>
      <Bleed top="6">
        <Stack gap="6">
          <DongleOnboardingProgress currentStep={2} />
          <Heading as="h3" size="XS">
            {fields.networkConnectionTitle.value}
          </Heading>
        </Stack>
      </Bleed>
      <Box>
        <InputPassword
          name="networkPassword"
          label={fields.networkConnectionPasswordFormField.value.label.replace('{wifi}', dongleInfo.wifiName)}
          hint={fields.networkConnectionPasswordFormField.value.hint}
          autoComplete="one-time-code"
          error={connectWifiFail ? fields.networkConnectionPasswordFormField.value.validationMessage : undefined}
          onChange={event => setPassword(event.target.value)}
        />
      </Box>
      <NativeBottomNavigationWrapper>
        <Stack direction="column" gap="4">
          <Button onClick={connectDongle} size="compact" isLoading={isLoading}>
            {fields.networkConnectionConnectButtonText.value}
          </Button>

          <Button
            size="compact"
            action="secondary"
            onClick={() => {
              setCurrentStep(Steps.NETWORK_SELECTION);
            }}>
            {fields.networkConnectionOtherNetworkButtonText.value}
          </Button>
        </Stack>
      </NativeBottomNavigationWrapper>
    </DongleOnboardingLayout>
  );
};

export default NetworkConnection;
