import { useCallback, useMemo } from 'react';

import { useRouter } from '@dxp-next';
import { InsightsNativePaths } from '@native-components/constants/paths';

const { DASHBOARD_PATH } = InsightsNativePaths;

export const useDongleOnboardingHelper = () => {
  const { replace, push, query } = useRouter();

  const closeDongleOnboarding = useCallback(() => {
    const url = new URL(document.URL);
    const returnParam = url.searchParams.get('return');

    if (returnParam) {
      replace(decodeURIComponent(returnParam));
    } else {
      replace('/');
    }
  }, [replace]);

  const closeOpenPortInstructions = useCallback(() => {
    push(DASHBOARD_PATH);
  }, [push]);

  const isAfterPurchase = useMemo(() => query.afterpurchase === 'true', [query]);

  return { closeDongleOnboarding, closeOpenPortInstructions, isAfterPurchase };
};
