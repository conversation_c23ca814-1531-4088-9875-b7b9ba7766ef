import React, { useCallback } from 'react';

import RichText from '@components/RichText/RichText';
import { InsightsNativePaths } from '@native-components/constants/paths';
import { openSystemBrowser } from '@native-components/helpers/inAppBrowser';
import { mapImage, useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
import { Box, Heading, Image, Stack, Stretch } from '@sparky';

import DongleOnboardingLayout from '../../common/DongleOnboardingLayout';
import MandateItemTile from '../../common/MandateItemTile';
import TutorialIndicator from '../../common/TutorialIndicator';
import { useDongleOnboardingContext } from '../../context/DongleOnboardingContext';
import { useDongleOnboardingHelper } from '../../utils/dongleOnboardingHelper';
import { Steps } from '../../utils/types';

const { FLUVIUS_PORT_CONFIG_URL } = InsightsNativePaths;

const InstructionPage = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const { setCurrentStep, isLoadingMandateData, mandateData } = useDongleOnboardingContext();
  const { closeOpenPortInstructions } = useDongleOnboardingHelper();

  const imageProps = mapImage(fields.fluviusInstructionImage);

  const openFluviusLink = useCallback(() => {
    openSystemBrowser(FLUVIUS_PORT_CONFIG_URL);
    setCurrentStep(Steps.WAITING_SCREEN);
  }, [setCurrentStep]);

  return (
    <DongleOnboardingLayout
      showDivider={false}
      showBackButton
      showFooterSecondaryBtn
      headerPrevText={fields.headerPreviousText.value}
      headerPrevClick={() => setCurrentStep(Steps.PORT_ACTIVATION_INTRODUCTION)}
      footerPrimaryBtnText={fields.fluviusInstructionContinueText.value}
      footerPrimaryBtnClick={openFluviusLink}
      onClose={closeOpenPortInstructions}>
      <Stretch height>
        <Stack alignX="center" gap="8">
          {imageProps.src && (
            <Image
              alt={imageProps.alt}
              src={imageProps.src}
              aspectRatio={imageProps.aspectRatio}
              width="100%"
              height="auto"
              display="block"
              objectFit="contain"
            />
          )}
          <TutorialIndicator />
          <Box>
            <Stack gap="3">
              <Stack.Item>
                <Heading as="h3" size="S">
                  {fields.fluviusInstructionTitle.value}
                </Heading>
              </Stack.Item>
              <Stack.Item>
                <RichText html={fields.fluviusInstructionContent.value} />
              </Stack.Item>
            </Stack>
          </Box>
          {!isLoadingMandateData &&
            mandateData?.externalMandates?.map(item => {
              if (item.productType === 'electricity') {
                return <MandateItemTile key={item.eanCode} item={item} />;
              }
              return null;
            })}
        </Stack>
      </Stretch>
    </DongleOnboardingLayout>
  );
};

export default InstructionPage;
