import React, { useCallback } from 'react';

import RichText from '@components/RichText/RichText';
import { useRouter } from '@dxp-next';
import { InsightsNativePaths } from '@native-components/constants/paths';
import { mapImage, useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
import { Box, Heading, Image, Stack, Stretch } from '@sparky';

import DongleOnboardingLayout from '../../common/DongleOnboardingLayout';
import TutorialIndicator from '../../common/TutorialIndicator';
import { useDongleOnboardingContext } from '../../context/DongleOnboardingContext';
import { useDongleOnboardingHelper } from '../../utils/dongleOnboardingHelper';
import { Steps } from '../../utils/types';

const { DASHBOARD_PATH } = InsightsNativePaths;

const PortActivationSuccess = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const { setCurrentStep } = useDongleOnboardingContext();
  const { closeOpenPortInstructions, isAfterPurchase } = useDongleOnboardingHelper();
  const router = useRouter();

  const imageProps = mapImage(fields.portActivationSuccessImage);

  const onClickPrimaryBtn = useCallback(() => {
    // TODO show notification before continuing (other user story)
    if (isAfterPurchase) {
      router.push(DASHBOARD_PATH);
      return;
    }
    setCurrentStep(Steps.BLUETOOTH_TUTORIAL);
  }, [isAfterPurchase, router, setCurrentStep]);

  const primaryButtonText = isAfterPurchase
    ? fields.portActivationSuccessAfterPurchasePrimaryButtonText.value
    : fields.portActivationSuccessPrimaryButtonText.value;
  const secondaryButtonText = !isAfterPurchase ? fields.portActivationSuccessSecondaryButtonText.value : undefined;
  const content = isAfterPurchase
    ? fields.portActivationSuccessAfterPurchaseContent.value
    : fields.portActivationSuccessContent.value;

  const onClickSecondaryBtn = useCallback(() => {
    setCurrentStep(Steps.BLUETOOTH_TUTORIAL);
  }, [setCurrentStep]);

  return (
    <DongleOnboardingLayout
      showDivider={false}
      showBackButton
      showFooterSecondaryBtn
      headerPrevText={fields.headerPreviousText.value}
      headerPrevClick={() => setCurrentStep(Steps.PORT_ACTIVATION_APPROVAL)}
      footerPrimaryBtnText={primaryButtonText}
      footerPrimaryBtnClick={onClickPrimaryBtn}
      footerSecondaryBtnText={secondaryButtonText}
      footerSecondaryBtnClick={!isAfterPurchase ? onClickSecondaryBtn : undefined}
      onClose={closeOpenPortInstructions}>
      <Stretch height>
        <Stack alignX="center" gap="8">
          {imageProps.src && (
            <Image
              alt={imageProps.alt}
              src={imageProps.src}
              aspectRatio={imageProps.aspectRatio}
              width="100%"
              height="auto"
              display="block"
              objectFit="contain"
            />
          )}
          <TutorialIndicator />
          <Box>
            <Stack gap="3">
              <Stack.Item>
                <Heading as="h3" size="S">
                  {fields.portActivationSuccessTitle.value}
                </Heading>
              </Stack.Item>
              <Stack.Item>
                <RichText html={content} />
              </Stack.Item>
            </Stack>
          </Box>
        </Stack>
      </Stretch>
    </DongleOnboardingLayout>
  );
};

export default PortActivationSuccess;
