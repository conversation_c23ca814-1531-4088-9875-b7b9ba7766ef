// TODO: Bluetooth failure with guide on what the user should do.
import React, { useState } from 'react';

import { <PERSON><PERSON> } from 'smartmeterdongle';

import Logger from '@common/log';
import RichText from '@components/RichText/RichText';
import { useContent } from '@sitecore/common';
import { DongleOnboardingFlowRendering } from '@sitecore/types/DongleOnboardingFlow';
// eslint-disable-next-line dxp-rules/no-dialog-from-sparky
import { Bleed, Button, Dialog, Heading, Stack, Stretch, Text } from '@sparky';

import DongleOnboardingLayout from '../../common/DongleOnboardingLayout';
import DongleOnboardingProgress from '../../common/DongleOnboardingProgress';
import { useDongleOnboardingContext } from '../../context/DongleOnboardingContext';
import { useDongleOnboardingHelper } from '../../utils/dongleOnboardingHelper';
import { Steps } from '../../utils/types';

const BluetoothFailed = () => {
  const { fields } = useContent<DongleOnboardingFlowRendering>();
  const { setCurrentStep } = useDongleOnboardingContext();
  const [isOpen, setOpen] = useState(false);
  const { closeDongleOnboarding } = useDongleOnboardingHelper();

  const retryBluetoothConnection = async () => {
    try {
      await Dongle.disconnect();
      setCurrentStep(Steps.BLUETOOTH_PAIRING);
    } catch (error) {
      Logger.error('2frNxt', 'P1 dongle bluetooth disconnect failed', error);
    }
  };

  return (
    <DongleOnboardingLayout
      showBackButton={true}
      showDivider={true}
      showFooterPrimaryBtn={false}
      headerMiddleText={fields.connectDongleNavigationTitle.value}
      // TODO should point to other step in the future, page has not been created yet.
      headerPrevClick={() => setCurrentStep(Steps.PORT_ACTIVATION_SUCCESS)}
      onClose={closeDongleOnboarding}>
      <Bleed top="6">
        <Stack gap="6">
          <DongleOnboardingProgress currentStep={1} />
          <Heading as="h3" size="XS">
            {fields.bluetoothTutorialTitle.value}
          </Heading>

          {/* TODO: Bluetooth failure with guide on what the user should do. */}
          <Text>Aansluiten mislukt</Text>

          <Button onClick={() => retryBluetoothConnection()}>Probeer opnieuw</Button>
        </Stack>
      </Bleed>
      <Dialog
        onClose={() => setOpen(false)}
        setOpen={setOpen}
        trigger={null}
        isOpen={isOpen}
        title={fields.bluetoothTutorialDialog.value.title}
        description={<RichText html={fields.bluetoothTutorialDialog.value.content} />}>
        <Stretch>
          <Button size="compact" onClick={() => setOpen(false)} action="secondary">
            {fields.bluetoothTutorialDialog.value.submitButtonText}
          </Button>
        </Stretch>
      </Dialog>
    </DongleOnboardingLayout>
  );
};

export default BluetoothFailed;
