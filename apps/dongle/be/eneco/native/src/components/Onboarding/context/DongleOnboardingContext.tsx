import React, { PropsWithChildren, useContext, useState } from 'react';

import { Insights_BelgiumExternalMandates_ShortUrlWithMandateResponse } from '@monorepo-types/dc';
import { useSendGetShortUrlAndExternalMandate } from '@native-components/helpers/mandateStatusHooks';

import { DongleProps, Steps } from '../utils/types';

interface ContextProps {
  currentStep: Steps;
  setCurrentStep: (newValue: Steps) => void;
  dongleInfo: DongleProps | null;
  setDongleInfo: (dongleData: DongleProps) => void;
  isLoadingMandateData: boolean;
  mandateData: Insights_BelgiumExternalMandates_ShortUrlWithMandateResponse | undefined;
}

const DongleOnboardingContext = React.createContext<ContextProps | null>(null);

const DongleOnboardingProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [currentStep, setCurrentStep] = useState<Steps>(Steps.PORT_ACTIVATION_INTRODUCTION);
  const [dongleInfo, setDongleInfo] = useState<DongleProps | null>(null);
  const { isLoading: isLoadingMandateData, data: mandateData } = useSendGetShortUrlAndExternalMandate();

  return (
    <DongleOnboardingContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        dongleInfo,
        setDongleInfo,
        isLoadingMandateData,
        mandateData,
      }}>
      {children}
    </DongleOnboardingContext.Provider>
  );
};

const useDongleOnboardingContext = () => {
  const context = useContext(DongleOnboardingContext);

  if (context === null) {
    throw new Error('useDongleOnboardingContext must be within the DongleOnboardingProvider');
  }
  return context;
};

export { useDongleOnboardingContext, DongleOnboardingProvider, DongleOnboardingContext };
