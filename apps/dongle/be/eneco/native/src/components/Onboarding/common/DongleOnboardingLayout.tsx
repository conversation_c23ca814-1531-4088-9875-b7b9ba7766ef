import React from 'react';

import { InvisibleBox } from '@custom-components/native/hems/InvisibleBox';
import NativeBottomNavigationWrapper from '@custom-components/native/NativeBottomNavigationWrapper';
import { useLinkComponent } from '@link';
import {
  Bleed,
  Box,
  Button,
  ButtonLink,
  Divider,
  Heading,
  IconButton,
  NavLink,
  NotificationBox,
  Stack,
  Stretch,
} from '@sparky';
import { ChevronLeftIcon, CloseIcon } from '@sparky/icons';
import { NotificationBoxProps } from '@sparky/types';

interface FooterProps {
  footerPrimaryBtnText?: string;
  footerPrimaryBtnClick?: () => void;
  footerPrimaryBtnHref?: string;
  footerSecondaryBtnText?: string;
  footerSecondaryBtnClick?: () => void;
  footerSecondaryBtnHref?: string;
  showFooterPrimaryBtn?: boolean;
  showFooterSecondaryBtn?: boolean;
}

interface HeaderProps {
  headerPrevText?: string | undefined;
  headerPrevClick?: () => void;
  headerPrevHref?: string | undefined;
  headerMiddleText?: string;
}

type DongleOnboardingLayoutProps = {
  children: React.ReactNode;
  alert?: { variant?: NotificationBoxProps['variant']; text?: string };
  onClose?: () => void;
  isLoading?: boolean;
  headerMiddleText?: string;
  showFooterPrimaryBtn?: boolean;
  showFooterSecondaryBtn?: boolean;
  showDivider: boolean;
  showBackButton: boolean;
} & FooterProps &
  HeaderProps;

const DongleOnboardingLayout = ({
  children,
  headerPrevText,
  headerPrevClick,
  headerPrevHref,
  footerPrimaryBtnText,
  footerPrimaryBtnClick,
  footerPrimaryBtnHref,
  alert,
  onClose,
  isLoading = false,
  headerMiddleText,
  showFooterPrimaryBtn = true,
  footerSecondaryBtnText,
  footerSecondaryBtnClick,
  footerSecondaryBtnHref,
  showFooterSecondaryBtn = false,
  showDivider,
  showBackButton,
}: DongleOnboardingLayoutProps) => {
  const Link = useLinkComponent();

  const header = (
    <Stack>
      <Box paddingBottom="1">
        <Stack direction="row" alignY="center" alignX="justify">
          {showBackButton ? (
            <NavLink
              leftIcon={<ChevronLeftIcon />}
              {...(headerPrevClick && { onClick: headerPrevClick })}
              {...(headerPrevHref && { href: headerPrevHref })}>
              {headerPrevText}
            </NavLink>
          ) : (
            <InvisibleBox>
              <ChevronLeftIcon size="medium" />
            </InvisibleBox>
          )}
          <Heading as="h3" size="3XS" color="textLowEmphasis">
            {headerMiddleText}
          </Heading>

          {onClose && (
            <IconButton onClick={onClose} label="close" size="regular">
              <CloseIcon />
            </IconButton>
          )}
        </Stack>
      </Box>
      {showDivider && (
        <Bleed horizontal={6}>
          <Divider />
        </Bleed>
      )}
    </Stack>
  );

  const footer = (
    <NativeBottomNavigationWrapper>
      <Stack direction="column" gap="4">
        {showFooterSecondaryBtn ? (
          <>
            {alert?.text ? <NotificationBox isAlert={true} variant={alert.variant} text={alert.text} /> : null}

            {footerSecondaryBtnHref && !footerSecondaryBtnClick && (
              <Link href={footerSecondaryBtnHref} linkType="internal">
                <ButtonLink size="compact" action="secondary">
                  {footerSecondaryBtnText}
                </ButtonLink>
              </Link>
            )}
            {footerSecondaryBtnClick && !footerSecondaryBtnHref && (
              <Button onClick={footerSecondaryBtnClick} action="secondary" isLoading={isLoading} size="compact">
                {footerSecondaryBtnText}
              </Button>
            )}
          </>
        ) : null}
        {showFooterPrimaryBtn ? (
          <>
            {alert?.text && showFooterSecondaryBtn ? (
              <NotificationBox isAlert={true} variant={alert.variant} text={alert.text} />
            ) : null}

            {footerPrimaryBtnHref && !footerPrimaryBtnClick && (
              <Link href={footerPrimaryBtnHref} linkType="internal">
                <ButtonLink size="compact">{footerPrimaryBtnText}</ButtonLink>
              </Link>
            )}
            {footerPrimaryBtnClick && !footerPrimaryBtnHref && (
              <Button onClick={footerPrimaryBtnClick} isLoading={isLoading} size="compact">
                {footerPrimaryBtnText}
              </Button>
            )}
          </>
        ) : null}
      </Stack>
    </NativeBottomNavigationWrapper>
  );

  return (
    <Stretch height>
      <Stack direction="column" gap="8">
        {header}

        {children}

        {footer}
      </Stack>
    </Stretch>
  );
};
export default DongleOnboardingLayout;
