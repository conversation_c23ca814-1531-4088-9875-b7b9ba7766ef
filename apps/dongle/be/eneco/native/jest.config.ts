const esModules = ['@awesome-cordova-plugins'].join('|');
export default {
  displayName: 'dongle-be-eneco-native',
  preset: `${process.env.NX_WORKSPACE_ROOT}/config/jest`,
  coverageDirectory: `${process.env.NX_WORKSPACE_ROOT}/coverage/apps/dongle/be/eneco/native`,
  setupFiles: [`${process.env.NX_WORKSPACE_ROOT}/config/jest/setEnvVarsBE.ts`],
  setupFilesAfterEnv: [`${process.env.NX_WORKSPACE_ROOT}/config/jest/jest-setup.ts`],
  transformIgnorePatterns: [`/node_modules/(?!${esModules})`],
};
