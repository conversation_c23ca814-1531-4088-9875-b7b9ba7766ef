import { FC, useEffect, useRef, useState } from 'react';

import { AnimateHeightBox } from '@custom-components/AnimateHeightBox';
import { useTranslation } from '@i18n';
import { useContent } from '@sitecore/common';
import type { InspirationAdviceFilterRendering } from '@sitecore/types/InspirationAdviceFilter';
import { Box, Button, TextLink, Grid, PageGrid, Stack, Text, Stretch } from '@sparky';
import { useMediaQuery } from '@sparky/hooks';
import { SpinnerIcon } from '@sparky/icons';

import { AdviceCard } from './components/AdviceCard/AdviceCard';
import { FilterSentence } from './components/FilterSentence/FilterSentence';
import { TipCard } from './components/TipCard/TipCard';
import { useFilter } from './InspirationAdviceFilter.context';
import { filterAdviceItemsObject } from './utils/filterAdviceItemsObject';

const cardIterationCountDesktop = 9;
const cardIterationCountMobile = 4;

export const InspirationAdviceFilterView: FC = () => {
  const isDesktop = useMediaQuery('md');

  const {
    fields: { cardsList },
  } = useContent<InspirationAdviceFilterRendering>();
  const { t } = useTranslation();
  const { filterTags } = useFilter();
  const [isAnimating, setIsAnimating] = useState(false);
  const [cardsGridHeight, setCardsGridHeight] = useState<number | undefined>();
  const [cardIterationCount, setCardIterationCount] = useState(cardIterationCountDesktop);
  const [visibleItemsCount, setVisibleItemsCount] = useState(cardIterationCount);

  const inspirationAdviceContainer = useRef<HTMLDivElement>(null);
  const adviceCardGrid = useRef<HTMLDivElement>(null);
  const prevVisibleItemsCount = useRef(visibleItemsCount);
  const firstNewResultRef = useRef<HTMLLIElement>(null);

  const { filteredAdviceCards, maxAmtOfCards } = filterAdviceItemsObject(cardsList, filterTags, visibleItemsCount);
  const allAdviceItemsAreVisible = visibleItemsCount >= maxAmtOfCards;

  const handleButtonClick = () => {
    const updatedVisibleItemsCount = Math.min(cardsList.length, visibleItemsCount + cardIterationCount);

    if (!allAdviceItemsAreVisible) {
      setIsAnimating(true);
      setVisibleItemsCount(updatedVisibleItemsCount);
      prevVisibleItemsCount.current = visibleItemsCount;

      setTimeout(() => {
        setIsAnimating(false);
      }, 500);
    }
  };

  useEffect(() => {
    if (visibleItemsCount && adviceCardGrid.current) {
      setCardsGridHeight(adviceCardGrid.current.clientHeight + 20); // Adding extra height to prevent cutoff card shadow
    }
  }, [visibleItemsCount]);

  useEffect(() => {
    setCardIterationCount(
      typeof isDesktop === 'undefined' || isDesktop ? cardIterationCountDesktop : cardIterationCountMobile,
    );
  }, [isDesktop]);

  useEffect(() => {
    setVisibleItemsCount(cardIterationCount);
  }, [cardIterationCount]);

  useEffect(() => {
    if (!isAnimating && firstNewResultRef.current) firstNewResultRef.current?.focus();
  }, [isAnimating]);

  return (
    <Box paddingBottom="12" backgroundColor="backgroundSecondary" ref={inspirationAdviceContainer}>
      <PageGrid>
        <PageGrid.Item gridColumn="1/-1">
          <FilterSentence />
        </PageGrid.Item>
        <PageGrid.Item gridColumn="1/-1">
          <AnimateHeightBox newHeight={cardsGridHeight} isAnimating={isAnimating}>
            <Grid
              as="ul"
              columns={{ initial: '1', md: '2' }}
              flow="dense"
              gap="10"
              gridTemplateColumns={{ xl: 'repeat(3, 1fr)' }}
              ref={adviceCardGrid}>
              {filteredAdviceCards.map((value, index) => {
                const cardIsLoading = index < prevVisibleItemsCount.current ? false : isAnimating;
                if (!value.fields.title?.value || !value.fields.text?.value) return null;

                return (
                  <li
                    ref={index === prevVisibleItemsCount.current ? firstNewResultRef : undefined}
                    key={value.id}
                    tabIndex={0}>
                    {'focalImage' in value.fields && value.fields.focalImage !== undefined ? (
                      <Grid.Item as="div">
                        <AdviceCard
                          heading={value.fields.title.value}
                          link={value.fields.link}
                          image={value.fields.focalImage}
                          isLoading={cardIsLoading}
                          text={value.fields.text.value}
                        />
                      </Grid.Item>
                    ) : (
                      <Grid.Item as="div">
                        <TipCard
                          heading={value.fields.title.value}
                          link={value.fields.link}
                          isLoading={cardIsLoading}
                          text={value.fields.text.value}
                        />
                      </Grid.Item>
                    )}
                  </li>
                );
              })}
            </Grid>
          </AnimateHeightBox>
          <Box paddingTop="12">
            <Stack alignX="center" gap="3">
              <Text weight="bold" color="textLowEmphasis">
                {filteredAdviceCards.length} van {maxAmtOfCards}
              </Text>
              {isAnimating ? (
                <Stack alignX="center">
                  <SpinnerIcon color="iconSecondary" size="large" />
                </Stack>
              ) : !allAdviceItemsAreVisible ? (
                <Stretch width={{ initial: true, md: false }}>
                  <Button onClick={() => handleButtonClick()}>{t('generalLabels.loadMore')}</Button>
                </Stretch>
              ) : null}
              {visibleItemsCount !== cardIterationCount ? (
                <TextLink
                  onClick={() =>
                    window.scrollTo({
                      top: inspirationAdviceContainer.current?.offsetTop,
                      behavior: 'smooth',
                    })
                  }>
                  {t('generalLabels.toTop')}
                </TextLink>
              ) : null}
            </Stack>
          </Box>
        </PageGrid.Item>
      </PageGrid>
    </Box>
  );
};
