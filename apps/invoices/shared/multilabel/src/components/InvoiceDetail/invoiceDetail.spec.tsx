import { screen } from '@testing-library/react';

import renderApp from '@jest-tools/renderApp';
import selfserviceMock from '@mocks/sitecore/containers/nl/eneco/selfservice';

import InvoiceDetail from './InvoiceDetail';

describe('InvoiceDetail', () => {
  it('should render invoice detail', async () => {
    renderApp(InvoiceDetail, {
      mock: selfserviceMock,
      scope: 'nl-eneco-selfservice',
      path: '/mijn-eneco/notas/nota/38498325/',
    });

    const d = new Date();
    const date = new Date('2021-3-14');
    const dateText = date.toLocaleDateString('nl-NL', {
      day: 'numeric',
      month: 'numeric',
      year: 'numeric',
    });
    const dueDate = new Date(d.getFullYear(), d.getMonth() + 1, d.getDate());
    const dueDateText = dueDate.toLocaleDateString('nl-NL');
    const dueDateTextLong = dueDate.toLocaleDateString('nl-NL', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });

    //Buttons
    expect(await screen.findByRole('button', { name: 'Nu betalen' })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Annuleren' })).toBeInTheDocument();

    // Dynamic copy
    expect(screen.getByText(/Voorschotnota Maart 2021/i)).toBeInTheDocument();
    expect(screen.getByText(dateText)).toBeInTheDocument();
    expect(screen.getByText(dueDateText)).toBeInTheDocument();
    expect(screen.getByText(`Vervaldatum: ${dueDateTextLong}`)).toBeInTheDocument();
    expect(screen.getAllByText(/123456/).length).toEqual(2);
    expect(screen.getAllByText(/87,00/).length).toEqual(2);

    //Links
    expect(screen.getByText(/Gedeelte betalen/)).toBeInTheDocument();
    expect(screen.getByText(/Betalingsregeling aanvragen/)).toBeInTheDocument();
  });

  it('should render invoice detail, but not show pay links or pay buttons when onlinePayment is not possible', async () => {
    renderApp(InvoiceDetail, {
      mock: selfserviceMock,
      scope: 'nl-eneco-selfservice',
      path: '/mijn-eneco/notas/nota/3849832426/',
      customerId: '10007', //Online payment is not possible (on both invoices)
    });

    expect(await screen.findByText(/Voorschotnota Februari 2022/i)).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Annuleren' })).toBeInTheDocument();
    expect(screen.queryByText(/Betalen via iDeal/i)).not.toBeInTheDocument();
    expect(screen.queryAllByRole('button', { name: /Nu betalen/i }).length).toEqual(0);
    expect(screen.queryByText(/Gedeelte betalen/)).not.toBeInTheDocument();
  });

  it('should not render betalingsregeling link when prop is set to false', async () => {
    renderApp(InvoiceDetail, {
      mock: selfserviceMock,
      scope: 'nl-eneco-selfservice',
      path: '/mijn-eneco/notas/nota/3849832632/',
      customerId: '10008',
    });

    expect(await screen.findByRole('link', { name: 'Annuleren' })).toBeInTheDocument();
    expect(screen.queryByText(/Betalingsregeling aanvragen/)).not.toBeInTheDocument();
  });
});
