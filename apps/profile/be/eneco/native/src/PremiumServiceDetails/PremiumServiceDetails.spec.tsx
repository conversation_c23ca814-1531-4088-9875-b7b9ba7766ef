import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { useCustomerGetCustomerProfileBe, useSubscriptionsGetCustomerInfo } from '@dc/hooks';
import renderApp from '@jest-tools/renderApp';
import userWithDongle from '@mocks/dc/customers/profile/be/userWithDongle';
import userWithDongleError from '@mocks/dc/customers/profile/be/userWithDongleError';
import { userWithDongleOutOfSupply } from '@mocks/dc/customers/profile/be/userWithDongleOutOfSupply';
import { getCustomerInfoResponse as activeSubscriptionWithDiscount } from '@mocks/dc/subscriptions/getCustomerInfo/activeWithDiscount';
import { getCustomerInfoResponse as cancelledSubscription } from '@mocks/dc/subscriptions/getCustomerInfo/cancelledSubscription';
import { getCustomerInfoResponse as nonRenewingSubscription } from '@mocks/dc/subscriptions/getCustomerInfo/nonRenewingSubscription';
import premiumServiceDetails from '@mocks/sitecore/apps/profile/be/eneco/native/premiumServiceDetails';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights';

import PremiumServiceDetails from './PremiumServiceDetails';

const { fields } = premiumServiceDetails;

jest.mock('@common/application', () => ({
  ...jest.requireActual('@common/application'),
  useApplication: jest.fn().mockReturnValue({
    locale: 'nl-BE',
    locales: ['nl-BE'],
    language: 'nl',
    languages: ['nl'],
    searchParams: new URLSearchParams(),
    isEditMode: false,
  }),
}));

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useSubscriptionsGetCustomerInfo: jest.fn() as unknown as typeof useSubscriptionsGetCustomerInfo,
  useCustomerGetCustomerProfileBe: jest.fn() as unknown as typeof useCustomerGetCustomerProfileBe,
}));

describe('PremiumServiceDetails', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it('should render', () => {
    (useSubscriptionsGetCustomerInfo as jest.Mock).mockReturnValue({
      data: {},
      isLoading: false,
    });

    renderApp(PremiumServiceDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/profile/premium/smart-insights',
    });
  });

  it('should render the title of the acive subscription with all additional info', async () => {
    const user = userEvent.setup();
    (useSubscriptionsGetCustomerInfo as jest.Mock).mockReturnValue({
      data: activeSubscriptionWithDiscount,
      isLoading: false,
    });
    (useCustomerGetCustomerProfileBe as jest.Mock).mockReturnValue({
      data: userWithDongleOutOfSupply,
    });

    renderApp(PremiumServiceDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/profile/premium/smart-insights',
    });

    expect(
      await screen.findByText(cancelledSubscription.subscriptions![0].planName!, { exact: true }),
    ).toBeInTheDocument();
    expect(
      await screen.findByText(fields.statusLabelList.value.enum.find(val => val.name === 'active')!.label, {
        exact: true,
      }),
    ).toBeInTheDocument();
    expect(await screen.findByText(fields.descriptionText.value, { exact: true })).toBeInTheDocument();
    expect(await screen.findByText('€ 3,00', { exact: true })).toBeInTheDocument();
    expect(await screen.findByText(fields.manageSubscriptionButtonText.value, { exact: true })).toBeInTheDocument();
    expect(await screen.findByText('Bekijk')).toBeInTheDocument();
    expect(await screen.findByText(fields.noEndDateLabel.value)).toBeInTheDocument();
    expect(await screen.findByText(fields.descriptionText.value, { exact: true })).toBeInTheDocument();
    expect(
      await screen.findByText(cancelledSubscription.subscriptions![0].subscriptionId!, { exact: true }),
    ).toBeInTheDocument();
    expect(await screen.findByText('12 maanden gratis', { exact: true })).toBeInTheDocument();
    expect(
      await screen.findByText(
        fields.dongleStatusLabelList.value.enum.find(val => val.name === 'ReadyForSupply')!.label,
        { exact: true },
      ),
    ).toBeInTheDocument();

    const openDialogButton = await screen.findByRole('button', { name: 'Bekijk' });

    await user.click(openDialogButton);

    expect(await screen.findByText('Eindigt op 9 april 2026', { exact: true })).toBeInTheDocument();
    expect(await screen.findByText('Sluiten', { exact: true })).toBeInTheDocument();
  });

  it('should render the correct status for a cancelled subscription', async () => {
    (useSubscriptionsGetCustomerInfo as jest.Mock).mockReturnValue({
      data: cancelledSubscription,
      isLoading: false,
    });

    renderApp(PremiumServiceDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/profile/premium/smart-insights',
    });

    expect(
      await screen.findByText(cancelledSubscription.subscriptions![0].planName!, { exact: true }),
    ).toBeInTheDocument();
    expect(
      await screen.findByText(fields.statusLabelList.value.enum.find(val => val.name === 'cancelled')!.label, {
        exact: true,
      }),
    ).toBeInTheDocument();
  });

  it('should render the correct status for a non renewing subscription', async () => {
    (useSubscriptionsGetCustomerInfo as jest.Mock).mockReturnValue({
      data: nonRenewingSubscription,
      isLoading: false,
    });

    renderApp(PremiumServiceDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/profile/premium/smart-insights',
    });

    expect(
      await screen.findByText(cancelledSubscription.subscriptions![0].planName!, { exact: true }),
    ).toBeInTheDocument();
    expect(
      await screen.findByText(fields.statusLabelList.value.enum.find(val => val.name === 'nonrenewing')!.label, {
        exact: true,
      }),
    ).toBeInTheDocument();
  });

  it('should render correct when the dongle is connected and working correctly (status: InSupply)', async () => {
    (useSubscriptionsGetCustomerInfo as jest.Mock).mockReturnValue({
      data: cancelledSubscription,
      isLoading: false,
    });
    (useCustomerGetCustomerProfileBe as jest.Mock).mockReturnValue({
      data: userWithDongle().data,
    });

    renderApp(PremiumServiceDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/profile/premium/smart-insights',
    });

    expect(
      await screen.findByText(cancelledSubscription.subscriptions![0].planName!, { exact: true }),
    ).toBeInTheDocument();
    expect(
      await screen.findByText(fields.statusLabelList.value.enum.find(val => val.name === 'cancelled')!.label, {
        exact: true,
      }),
    ).toBeInTheDocument();
    expect(
      await screen.findByText(fields.dongleStatusLabelList.value.enum.find(val => val.name === 'InSupply')!.label, {
        exact: true,
      }),
    ).toBeInTheDocument();
  });

  it('should render correct when the dongle is connected and not working properly (status: Error)', async () => {
    (useSubscriptionsGetCustomerInfo as jest.Mock).mockReturnValue({
      data: cancelledSubscription,
      isLoading: false,
    });
    (useCustomerGetCustomerProfileBe as jest.Mock).mockReturnValue({
      data: userWithDongleError().data,
    });

    renderApp(PremiumServiceDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/profile/premium/smart-insights',
    });

    expect(
      await screen.findByText(cancelledSubscription.subscriptions![0].planName!, { exact: true }),
    ).toBeInTheDocument();
    expect(
      await screen.findByText(fields.statusLabelList.value.enum.find(val => val.name === 'cancelled')!.label, {
        exact: true,
      }),
    ).toBeInTheDocument();
    expect(
      await screen.findByText(fields.dongleStatusLabelList.value.enum.find(val => val.name === 'Error')!.label, {
        exact: true,
      }),
    ).toBeInTheDocument();
  });

  it('should render correct when the dongle has been paired correctly and transmits data (status: InSupply)', async () => {
    (useSubscriptionsGetCustomerInfo as jest.Mock).mockReturnValue({
      data: cancelledSubscription,
      isLoading: false,
    });
    (useCustomerGetCustomerProfileBe as jest.Mock).mockReturnValue({
      data: userWithDongleOutOfSupply,
    });

    renderApp(PremiumServiceDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/profile/premium/smart-insights',
    });

    expect(
      await screen.findByText(cancelledSubscription.subscriptions![0].planName!, { exact: true }),
    ).toBeInTheDocument();
    expect(
      await screen.findByText(fields.statusLabelList.value.enum.find(val => val.name === 'cancelled')!.label, {
        exact: true,
      }),
    ).toBeInTheDocument();
    expect(
      await screen.findByText(fields.dongleStatusLabelList.value.enum.find(val => val.name === 'OutOfSupply')!.label, {
        exact: true,
      }),
    ).toBeInTheDocument();
  });
});
