import {
  DC_Belgium_Client_Models_SubscriptionInfoStatus,
  DC_Belgium_Client_Models_DongleAgreementStatusResponseModel,
} from '@monorepo-types/dc';
import { BadgeColor } from '@native-components/components/ListTileItemExtended';
import { NameLabelListField } from '@sitecore/types/PremiumServiceDetails';

export function getBadgeBySubscriptionStatus(
  subscriptionStatus: DC_Belgium_Client_Models_SubscriptionInfoStatus,
  labelList: NameLabelListField,
): { label: string; color: BadgeColor } {
  switch (subscriptionStatus) {
    case 'Active':
      return {
        label: getLabelByStatus(labelList, subscriptionStatus),
        color: 'success',
      };
    case 'NonRenewing':
      return {
        label: getLabelByStatus(labelList, subscriptionStatus),
        color: 'warning',
      };
    case 'Cancelled':
      return {
        label: getLabelByStatus(labelList, subscriptionStatus),
        color: 'error',
      };
    case 'Unknown':
      return {
        label: getLabelByStatus(labelList, subscriptionStatus),
        color: 'error',
      };
    default:
      return { label: getLabelByStatus(labelList, 'Unknown'), color: 'error' };
  }
}

export function getBadgeByDongleStatus(
  dongleStatus: DC_Belgium_Client_Models_DongleAgreementStatusResponseModel,
  labelList: NameLabelListField,
): { label: string; color: BadgeColor } {
  switch (dongleStatus) {
    case 'Pairing':
      return {
        label: getLabelByStatus(labelList, dongleStatus),
        color: 'error',
      };
    case 'InSupply':
      return {
        label: getLabelByStatus(labelList, dongleStatus),
        color: 'success',
      };
    case 'OutOfSupply':
      return {
        label: getLabelByStatus(labelList, dongleStatus),
        color: 'error',
      };
    case 'Error':
      return {
        label: getLabelByStatus(labelList, dongleStatus),
        color: 'warning',
      };
    case 'ReadyForSupply':
      return {
        label: getLabelByStatus(labelList, dongleStatus),
        color: 'error',
      };
    case 'Unknown':
      return {
        label: getLabelByStatus(labelList, dongleStatus),
        color: 'error',
      };
    default:
      return {
        label: getLabelByStatus(labelList, 'Error'),
        color: 'error',
      };
  }
}

function getLabelByStatus(labelList: NameLabelListField, status: string) {
  return labelList.value.enum.find(label => label.name.toLowerCase() === status.toLowerCase())?.label || '';
}
