import React from 'react';

import CardSkeleton from '@apps/profile/nl/multilabel/src/Privacy/CardSkeleton/CardSkeleton';
import { useCustomerGetCustomerProfileBe } from '@dc/hooks';
import ListTileItem from '@native-components/components/ListTileItem';
import { useContent } from '@sitecore/common';
import { PremiumServiceDetailsRendering } from '@sitecore/types/PremiumServiceDetails';
import { Card, Stack, Text, Box } from '@sparky';
import { DongleIcon } from '@sparky/icons';

import { getBadgeByDongleStatus } from '../utils/badgeHelper';

const DongleConnection: React.FC = () => {
  const { fields } = useContent<PremiumServiceDetailsRendering>();
  const { data, isLoading } = useCustomerGetCustomerProfileBe();

  if (isLoading) {
    return <CardSkeleton label={fields.dongleConnectionLabel.value} />;
  }

  if (!data || !data.dongleInfoResponse) {
    return null;
  }

  return (
    <Box>
      <Stack gap="2">
        <Text weight="bold">{fields.dongleConnectionLabel.value}</Text>
        <Card>
          <ListTileItem
            label={fields.dongleConnectionLink.value.text}
            categoryIcon={<DongleIcon />}
            href={fields.dongleConnectionLink.value.href}
            badge={getBadgeByDongleStatus(data.dongleInfoResponse.status, fields.dongleStatusLabelList)}
            hasDivider={false}
          />
        </Card>
      </Stack>
    </Box>
  );
};

export default DongleConnection;
