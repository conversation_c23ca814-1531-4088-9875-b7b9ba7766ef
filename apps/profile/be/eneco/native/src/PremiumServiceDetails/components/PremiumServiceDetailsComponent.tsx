import { useMemo, useState } from 'react';

import CardSkeleton from '@apps/profile/nl/multilabel/src/Privacy/CardSkeleton/CardSkeleton';
import env from '@common/env';
import NativeBottomNavigationWrapper from '@custom-components/native/NativeBottomNavigationWrapper';
import { useSubscriptionsGetCustomerInfo } from '@dc/hooks';
import { Link } from '@dxp-next';
import { openSystemBrowser } from '@native-components/helpers/inAppBrowser';
import { useContent } from '@sitecore/common';
import { PremiumServiceDetailsRendering } from '@sitecore/types/PremiumServiceDetails';
import { Button, AlertDialog, Heading, Stack, Stretch, Text, ButtonLink } from '@sparky';

import DetailsOverview from './DetailsOverview';
import DiscountsOverview from './DiscountsOverview';
import DongleConnection from './DongleConnection';
import SubscriptionPrice from './SubscriptionPrice';

const PremiumServiceDetailsComponent = () => {
  const { fields } = useContent<PremiumServiceDetailsRendering>();
  const { data, isLoading } = useSubscriptionsGetCustomerInfo();
  const [isPartnerDialogOpen, setPartnerDialogOpen] = useState(false);

  const subscription = useMemo(() => {
    return data?.subscriptions?.find(sub => sub.serviceType! === fields.featureOption.value);
  }, [data]);

  const subscriptionCancelled = useMemo(
    () => subscription?.status === 'Cancelled' || subscription?.status === 'NonRenewing',
    [subscription],
  );

  if (isLoading || !data || !subscription) {
    return <CardSkeleton label="" />;
  }

  return (
    <Stretch>
      <Stack>
        <Stack.Item grow>
          <Stack gap="6">
            <Heading as="h2" size="M">
              {subscription.planName}
            </Heading>
            <SubscriptionPrice fields={fields} subscription={subscription} />
            <Text>{fields.descriptionText.value}</Text>
            <DetailsOverview subscription={subscription} fields={fields} />
            <DiscountsOverview subscription={subscription} discounts={subscription.details} />
            <DongleConnection />
          </Stack>

          <AlertDialog
            isOpen={isPartnerDialogOpen}
            trigger={isPartnerDialogOpen}
            title={fields.partnerPortalDialog.value.title}
            description={fields.partnerPortalDialog.value.content}
            onClose={() => setPartnerDialogOpen(false)}
            setOpen={setPartnerDialogOpen}
            onConfirm={() => {
              openSystemBrowser(env('CHARGEBEE_PORTAL'));
            }}
            confirmText={fields.partnerPortalDialog.value.submitButtonText ?? ''}
            onDeny={() => setPartnerDialogOpen(false)}
            denyText={fields.partnerPortalDialog.value.cancelButtonText ?? ''}
          />
        </Stack.Item>

        <Stack.Item>
          <NativeBottomNavigationWrapper showDivider={false} backgroundColor={'backgroundSecondary'}>
            {subscriptionCancelled ? (
              <Link linkType="internal" href={fields.undoCancellationLink.value.href}>
                <ButtonLink action="secondary" size="compact">
                  {fields.undoCancellationLink.value.text}
                </ButtonLink>
              </Link>
            ) : (
              <Button action="secondary" onClick={() => setPartnerDialogOpen(true)}>
                {fields.manageSubscriptionButtonText.value}
              </Button>
            )}
          </NativeBottomNavigationWrapper>
        </Stack.Item>
      </Stack>
    </Stretch>
  );
};

export default PremiumServiceDetailsComponent;
