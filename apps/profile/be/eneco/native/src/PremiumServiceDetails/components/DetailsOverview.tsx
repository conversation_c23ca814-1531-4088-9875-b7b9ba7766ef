import React from 'react';

import { useFormatter } from '@i18n';
import { Subscriptions_SubscriptionInfoResponseModel } from '@monorepo-types/dc';
import ListTileItem from '@native-components/components/ListTileItem';
import { Fields } from '@sitecore/types/PremiumServiceDetails';
import { Card, Stack, Text } from '@sparky';

import { getBadgeBySubscriptionStatus } from '../utils/badgeHelper';

interface DetailsOverviewProps {
  fields: Fields;
  subscription: Subscriptions_SubscriptionInfoResponseModel;
}

const DetailsOverview: React.FC<DetailsOverviewProps> = ({ fields, subscription }) => {
  const { date } = useFormatter();

  const badge = getBadgeBySubscriptionStatus(subscription.status, fields.statusLabelList);

  return (
    <Stack gap="4">
      <Text weight="bold">{fields.detailsLabel.value}</Text>
      <Card>
        <ListTileItem label={fields.statusText.value} badge={badge} />
        {subscription.startDate && (
          <ListTileItem label={fields.startDateLabel.value} lowerLabel={date.long(subscription?.startDate)} />
        )}
        <ListTileItem
          label={fields.endDateLabel.value}
          lowerLabel={subscription.endDate ? date.long(subscription?.endDate) : fields.noEndDateLabel.value}
        />
        {subscription.subscriptionId && (
          <ListTileItem
            label={fields.subscriptionIdLabel.value}
            lowerLabel={subscription.subscriptionId}
            hasDivider={false}
          />
        )}
      </Card>
    </Stack>
  );
};

export default DetailsOverview;
