import React, { FC } from 'react';

import { I18nProvider, useTranslation } from '@i18n';
import { NotificationBox } from '@sparky';

import { PriceChangeStatus } from '../../helpers/dc';

type OfferChangeNotificationProps = {
  companyName: string;
  priceChange: PriceChangeStatus;
};
const OfferChangeNotification: FC<OfferChangeNotificationProps> = ({ companyName, priceChange }) => {
  const { t } = useTranslation();
  if (priceChange === 'decreased') {
    return (
      <NotificationBox
        isAlert={false}
        title={t('priceDecrease.title', { companyName })}
        text={t('priceDecrease.description', { companyName })}
      />
    );
  }
  if (priceChange === 'increased') {
    return (
      <NotificationBox
        isAlert={false}
        variant="warning"
        title={t('priceIncrease.title', { companyName })}
        text={t('priceIncrease.description', { companyName })}
      />
    );
  }
  return (
    <NotificationBox
      isAlert={false}
      title={t('priceUnchanged.title', { companyName })}
      text={t('priceUnchanged.description', { companyName })}
    />
  );
};

export const OfferChangeNotificationWithTranslation: FC<OfferChangeNotificationProps> = props => {
  return (
    <I18nProvider dictionary={locale => import(`./content/${locale}.json`)}>
      <OfferChangeNotification {...props} />
    </I18nProvider>
  );
};
