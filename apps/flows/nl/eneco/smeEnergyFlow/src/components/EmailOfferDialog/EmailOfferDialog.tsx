import React, { useEffect, useState } from 'react';

import { useFeatureValue } from '@growthbook/growthbook-react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useActor, useMachine } from '@xstate/react';
import { Form<PERSON>rovider, SubmitHandler, useForm } from 'react-hook-form';
import { useDebounce } from 'react-use';
import * as yup from 'yup';

import { emailValidation } from '@common/validation/emailRegex';
import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { useProductsPublicSendOfferByMail } from '@dc/hooks/ProductsPublic';
import { useShoppingBasketPublicPatchBasket } from '@dc/hooks/ShoppingBasketPublic';
import useDC from '@dc/useDC';
import { useFlowContext } from '@eneco/flows/src/hooks';
import { generateBasketIdCookieName } from '@eneco/flows/src/utils/FlowUtils';
import {
  DC_Domain_Models_Products_ProductType,
  DC_Domain_Models_Products_ShoppingBasket_BasketOffer,
} from '@monorepo-types/dc';
import { mapImage, useLayoutData } from '@sitecore/common';
import { Fields } from '@sitecore/types/manual/smeFlowExperiment/SMEOfferStep';
import { FlowsNewEnergyOfferStep } from '@sitecore/types/SMEOfferStep';
import {
  Box,
  Button,
  Checkbox,
  Form,
  Heading,
  Image,
  InputCombobox,
  InputEmail,
  NotificationBox,
  Stack,
  Text,
} from '@sparky';

import {
  constructTextualDataKey,
  getTitle,
  getDurationInMonth,
  getCostDetails,
  generateReturnFromEmailParam,
} from '../../helpers';
import { chamberOfCommerceSearchMachine } from '../../machines';
import { Offer, Product } from '../../types';

type FormValues = { C1: boolean; C3: boolean; companyName: string; emailAddress: string };

interface Props {
  textualData?: Fields;
  availableOffers: Offer[];
}

const getReturnUrl = (params: Record<string, string>) => {
  if (typeof window === 'undefined') return '';

  const url = new URL(window.location.href);
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.set(key, value);
  });
  return `${url.protocol}//${url.host}${url.pathname}${url.search}`;
};

export const EMAIL_OFFER_FEATURE_ID = '586666|sme-send-offer';

export const EmailOfferDialogComponent = ({ textualData, availableOffers }: Props) => {
  const { flowService } = useFlowContext();
  const { businessUnit, label } = useDC();
  const [flowState, sendToFlowMachine] = useActor(flowService);

  const {
    context: flowContext,
    context: { energyType },
  } = flowState;
  const [isOpen, setIsOpen] = useState(false);
  const { send: sendOfferByEmail, isSuccess, isLoading, isError } = useProductsPublicSendOfferByMail();
  const {
    send: updateShoppingBasket,
    isLoading: isUpdatingShoppingBasket,
    isError: isUpdateShoppingBasketError,
  } = useShoppingBasketPublicPatchBasket();

  const getProduct = (product: Product) => {
    return {
      code: product.code,
      type: product.type,
      costDetails: getCostDetails(product),
      productName: product.title,
      durationInMonths: product.contractSpan ? getDurationInMonth(product.contractSpan) : 0,
    };
  };

  const getProducts = (offer: Offer) => {
    const products = offer.products.map(getProduct);

    if (offer.costDiscounts.productDetails) {
      const costDetails = offer.costDiscounts.productDetails.costDetails?.find(item => item.type === 'oneOffCosts');
      if (costDetails) {
        const cashbackProduct = {
          type: 'cashback' as DC_Domain_Models_Products_ProductType,
          productName: offer.costDiscounts.productDetails.description ?? '',
          code: offer.costDiscounts.productDetails.code ?? '',
          costDetails: [
            {
              type: 'oneOffCosts',
              costs: costDetails.costs,
            },
          ],
          durationInMonths: 0,
        };
        products.push(cashbackProduct);
      }
    }

    return products;
  };

  const offerDetails = (offer: Offer) => {
    const textualDataKey = constructTextualDataKey(offer.contractSpan, energyType);
    const offerName = getTitle(textualDataKey, textualData?.productData?.productDataList as FlowsNewEnergyOfferStep);

    return {
      offerName,
      durationInMonths: offer.contractSpan ? getDurationInMonth(offer.contractSpan[0]) : 0,
      costDetails: getCostDetails(offer),
      products: getProducts(offer),
    };
  };

  const {
    context: {
      companyName,
      basketId,
      emailAddress,
      isPersonalDetailsEditProhibited,
      machines: { chamberOfCommerceSearchMachineState } = {},
    },
  } = flowState;

  const [chamberOfCommerceSearchState, sendTochamberOfCommerceSearchMachine] = useMachine(
    chamberOfCommerceSearchMachine,
    {
      state: chamberOfCommerceSearchMachineState ?? chamberOfCommerceSearchMachine.initialState,
    },
  );

  const { context: { results } = {} } = chamberOfCommerceSearchState;
  const companyNameOptions = [...new Set(results?.map(result => result?.name ?? ''))];

  const formSchema = yup.object({
    companyName: yup.string().required(textualData?.emailOffer?.companyNameFormField?.value?.requiredMessage),
    emailAddress: emailValidation({
      emailMessage: textualData?.emailOffer?.emailAddressFormField?.value?.validationMessage,
      requiredMessage: textualData?.emailOffer?.emailAddressFormField?.value?.requiredMessage,
    }),
  });

  const formMethods = useForm<FormValues>({
    mode: 'onSubmit',
    defaultValues: {
      emailAddress: emailAddress,
      companyName: companyName,
      C1: false,
      C3: false,
    },
    resolver: yupResolver(formSchema),
  });

  const {
    handleSubmit,
    register,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = formMethods;

  const watchCompanyName = watch('companyName');

  useDebounce(
    () => {
      if (!watchCompanyName) return;
      const isCompanyNameInOptions = companyNameOptions.includes(watchCompanyName);
      if (watchCompanyName && !isCompanyNameInOptions) {
        sendTochamberOfCommerceSearchMachine({
          type: 'GET_COMPANY_OPTIONS',
          config: { businessUnit, label },
          values: {
            companyName: watchCompanyName,
          },
        });
      }
    },
    500,
    [watchCompanyName],
  );
  const {
    route: { fields },
  } = useLayoutData();

  const handleSendOfferViaEmail = async (
    offers: DC_Domain_Models_Products_ShoppingBasket_BasketOffer[],
    companyName: string,
    emailAddress: string,
  ) => {
    const basketProperties = {
      usages: {
        electricityHigh: flowContext.usageElectricityHigh,
        electricityLow: flowContext.usageElectricityLow,
        electricityRedelivery: flowContext.solarPanelsOutput,
        gas: flowContext.usageGas,
      },
      orderInfo: {
        deliveryAddress: {
          street: flowContext.street,
          houseNumber: flowContext.houseNumber,
          houseNumberSuffix: flowContext.houseNumberSuffix,
          postalCode: flowContext.postalCode,
          city: flowContext.city,
          type: 'Delivery',
          hasResidentialPurpose: flowContext.hasResidencePurpose,
        },
        organisation: {
          kvkName: companyName,
        },
      },
      offers,
    };

    await updateShoppingBasket({
      data: basketProperties,
      id: basketId,
    });

    const returnParams = {
      [generateBasketIdCookieName(fields?.flowName?.value?.toString() || 'smeenergyflow')]: basketId || '',
      companyName,
      ...generateReturnFromEmailParam(),
    };

    await sendOfferByEmail({
      data: {
        emailAddress: emailAddress,
        shoppingBasketId: basketId,
        url: getReturnUrl(returnParams),
      },
    });
  };

  const submitForm: SubmitHandler<FormValues> = ({ companyName, emailAddress, C1, C3 }) => {
    const offers = availableOffers.map(offerDetails) as DC_Domain_Models_Products_ShoppingBasket_BasketOffer[];
    handleSendOfferViaEmail(offers, companyName, emailAddress);
    sendToFlowMachine({
      type: 'UPDATE_VALUES',
      values: {
        emailAddress,
        companyName,
        C1,
        C3,
      },
    });
  };

  useEffect(() => {
    !isOpen && reset();
  }, [isOpen, reset]);

  return (
    <TrackedDialog
      onClose={() => {
        setIsOpen(false);
      }}
      setOpen={() => setIsOpen(true)}
      isOpen={isOpen}
      title={!isSuccess && textualData?.emailOffer?.emailDialog?.value?.title}
      trigger={<Button action="secondary">{textualData?.emailOffer?.emailDialog?.value?.triggerText}</Button>}>
      {isSuccess ? (
        <SubmissionSuccessContent textualData={textualData} onClose={() => setIsOpen(false)} availableOffers={[]} />
      ) : (
        <FormProvider {...formMethods}>
          <Form
            onSubmit={e => {
              e.preventDefault();
              e.stopPropagation();
              handleSubmit(submitForm)(e);
            }}>
            <Stack direction="column" gap="6">
              {(isError || isUpdateShoppingBasketError) && (
                <NotificationBox
                  isAlert={false}
                  variant={textualData?.emailOffer?.errorEmailNotification?.value?.variant}
                  title={textualData?.emailOffer?.errorEmailNotification?.value?.title}
                  text={<RichText html={textualData?.emailOffer?.errorEmailNotification?.value.content} />}
                />
              )}
              <Stack.Item>
                <InputCombobox
                  {...register('companyName')}
                  defaultValue={companyName}
                  onChange={value => {
                    const newValue = value.trim();
                    if (newValue !== watchCompanyName) {
                      setValue('companyName', newValue);
                    }
                  }}
                  label={textualData?.emailOffer?.companyNameFormField?.value?.label ?? ''}
                  placeholder={textualData?.emailOffer?.companyNameFormField?.value?.placeholder}
                  hint={textualData?.emailOffer?.companyNameFormField?.value?.hint}
                  error={errors?.companyName?.message}
                  options={
                    chamberOfCommerceSearchState.matches('FETCHING')
                      ? [textualData?.emailOffer?.companySearchLoadingText?.value ?? '']
                      : companyNameOptions
                  }
                />
              </Stack.Item>
              <Stack.Item>
                <InputEmail
                  {...register('emailAddress')}
                  label={textualData?.emailOffer?.emailAddressFormField?.value?.label ?? ''}
                  placeholder={textualData?.emailOffer?.emailAddressFormField?.value?.placeholder}
                  hint={textualData?.emailOffer?.emailAddressFormField?.value?.hint}
                  autoComplete="email"
                  error={errors?.emailAddress?.message}
                />
              </Stack.Item>
              {!isPersonalDetailsEditProhibited && (
                <Stack.Item>
                  <Stack direction="column" gap="2">
                    <Checkbox
                      label={textualData?.emailOffer?.productOffersFirstPartyCheckbox?.value?.label ?? ''}
                      {...register('C1')}
                    />
                    <Checkbox
                      label={textualData?.emailOffer?.productOffersThirdPartyCheckbox?.value?.label ?? ''}
                      {...register('C3')}
                    />
                  </Stack>
                </Stack.Item>
              )}
              <Stack.Item>
                <Button type="submit" isLoading={isLoading || isUpdatingShoppingBasket}>
                  {textualData?.emailOffer?.emailDialog?.value?.submitButtonText}
                </Button>
              </Stack.Item>
            </Stack>
          </Form>
        </FormProvider>
      )}
    </TrackedDialog>
  );
};

export const EmailOfferDialog = (props: Props) => {
  const enabled = useFeatureValue(EMAIL_OFFER_FEATURE_ID, false);

  return enabled ? <EmailOfferDialogComponent {...props} /> : null;
};

interface SubmissionSuccessContentProps extends Props {
  onClose: () => void;
}
const SubmissionSuccessContent = ({ textualData, onClose }: SubmissionSuccessContentProps) => {
  const imageProps = mapImage(textualData?.emailOffer?.thankYouImage);

  return (
    <Box>
      <Stack gap="2">
        {imageProps?.src && (
          <Stack alignX="center">
            <Image src={imageProps.src} alt={imageProps?.alt ?? ''} height="auto" objectFit="cover" />
          </Stack>
        )}
        <Heading as="h3" size="M">
          {textualData?.emailOffer?.thankYouTitle?.value}
        </Heading>
        <Text size="BodyL">{textualData?.emailOffer?.thankYouContent?.value}</Text>
      </Stack>
      <Box paddingTop="6">
        <Button onClick={onClose} action="primary">
          {textualData?.emailOffer?.emailSentCloseButtonText?.value}
        </Button>
      </Box>
    </Box>
  );
};
