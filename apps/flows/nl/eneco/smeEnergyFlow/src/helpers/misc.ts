import { DC_Domain_Models_Products_DiscountType } from '@monorepo-types/dc';

import {
  ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_HIGH,
  ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_LOW,
  ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_GAS,
  ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_WARMTH,
  ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_WATER,
} from './constants';
import { EnergyType, Offer } from '../types';

export const energyTypeIncludes = (energyType: EnergyType, type: EnergyType): boolean =>
  RegExp(`${type}`, 'i').test(energyType);

export const checkOfferIncludesDynamicPricingProduct = (offer?: Offer): boolean =>
  !!offer?.products.find(
    product => (product.type === 'gas' || product.type === 'electricity') && product?.contractSpan?.duration === 0,
  );

export const checkOfferIncludesNonDynamicPricingProduct = (offer?: Offer): boolean =>
  !!offer?.products.find(
    product => (product.type === 'gas' || product.type === 'electricity') && product?.contractSpan?.duration !== 0,
  );

export const checkOffersIncludesDynamicPricingProduct = (offers: Offer[]): boolean => {
  return !!offers.find(offer => checkOfferIncludesDynamicPricingProduct(offer));
};

export const getMaxUsageValue = (type: 'ELECTRICITY_HIGH' | 'ELECTRICITY_LOW' | 'GAS' | 'WARMTH' | 'WATER'): number => {
  if (type === 'ELECTRICITY_HIGH') {
    return ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_HIGH;
  } else if (type === 'ELECTRICITY_LOW') {
    return ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_LOW;
  } else if (type === 'GAS') {
    return ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_GAS;
  } else if (type === 'WARMTH') {
    return ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_WARMTH;
  } else if (type === 'WATER') {
    return ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_WATER;
  } else {
    return 0;
  }
};

export const checkAvailabilityOfferType = (
  offers: Offer[],
  offerType?: DC_Domain_Models_Products_DiscountType,
): DC_Domain_Models_Products_DiscountType | undefined => offers.find(offer => offer.type === offerType)?.type;

export const selectDefaultOfferType = (offers: Offer[]): DC_Domain_Models_Products_DiscountType | undefined =>
  (offers.find(offer => offer.costDiscounts?.cashback || offer.mostPopularChoice) ?? offers[0])?.type;

export const multiplyByFloorArea = (usage: number, floorArea: number) => Math.round(usage * floorArea);

export const RETURN_SOURCE_PARAM = 'returnSource';
const RETURN_SOURCE_VALUE = 'email';

export function isReturnFromEmail(): boolean {
  const urlToCheck = typeof window !== 'undefined' ? window.location.href : '';

  const urlObj = new URL(urlToCheck);

  const returnSource = urlObj.searchParams.get(RETURN_SOURCE_PARAM);

  return returnSource === RETURN_SOURCE_VALUE;
}

export function generateReturnFromEmailParam() {
  return {
    [RETURN_SOURCE_PARAM]: RETURN_SOURCE_VALUE,
  };
}
