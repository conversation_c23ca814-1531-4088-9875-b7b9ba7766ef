import { capitalizeFirstLetter } from '@common/string';
import { DetailsIconType, FlowContext } from '@eneco/flows/types';
import {
  DC_Domain_Models_Products_CostDetailType,
  DC_Domain_Models_Products_DiscountType,
  DC_Domain_Models_Products_ProductType,
  Products_Offers_DiscountResponse,
  Products_Offers_DiscountProduct,
  Products_Offers_MpcCombination,
  Products_Offers_ProductCombination,
  Products_Offers_ProductCostDetail,
  Products_Offers_PublicOffer,
  Products_VatModel,
  Extensions_Products_DiscountProductDetail,
} from '@monorepo-types/dc';

import { EnergyType, Product, Offer, Price, OfferTermsAndConditions, OfferContractSpan, CostDetails } from '../types';

const mergeOfferCosts = (costs1?: Products_VatModel, costs2?: Products_VatModel): Products_VatModel | undefined => {
  return Object.fromEntries(
    Object.keys(costs1 ?? {}).map(key => [
      key,
      (costs1?.[key as keyof Products_VatModel] ?? 0) + (costs2?.[key as keyof Products_VatModel] ?? 0),
    ]),
  ) as unknown as Products_VatModel;
};

const getOfferCostDetailsDetails = (products: Product[] = []): Record<string, unknown> =>
  products.reduce((acc, product) => Object.assign(acc, { ...product.costDetails }), {});

export const getCrossSellRentalDeviceProductAvailability = (offer?: Products_Offers_PublicOffer): boolean =>
  !!offer?.offers?.find(o => o.offerType === 'rentalDevices');

export const getUpsellProductAvailability = (
  productType: DC_Domain_Models_Products_ProductType,
  flowContext: FlowContext,
  offer?: Products_Offers_PublicOffer,
): boolean => getUpsellProductPrice(productType, flowContext, offer) !== 0;

const getOfferContractSpanFromProducts = (products: Product[]): OfferContractSpan[] =>
  products.map(product => {
    return product.contractSpan
      ? {
          ...product.contractSpan,
          // Threat "warmthEkv" and "bronwarmte" as regular "warmth" product
          type: product.type === 'warmthEkv' || product.type === 'bronwarmte' ? 'warmth' : product.type,
        }
      : {
          duration: 0,
          byPeriod: 'D',
          type: 'unknown',
        };
  });
export const getMPCCodeFromProducts = (
  offer: Products_Offers_DiscountResponse,
  productTypes: DC_Domain_Models_Products_ProductType[],
  hasUpsellGas: boolean,
  hasUpsellElectricity: boolean,
) => {
  let upsellGasProduct = 0;
  let downsellGasProduct = 0;
  let upsellElectricityProduct = 0;
  let downsellElectricityProduct = 0;

  // Get possible combinations
  const products =
    offer.products?.filter(product => {
      return productTypes.includes(product.productType);
    }) ?? [];

  // NOTE: would be alot easier and cleaner if there was a downsellMarketingProductCode available
  // Grab combinations
  offer.productCombinations?.forEach(combination => {
    combination.mpcCombinations?.forEach(mpc => {
      // Loop over products vs MPC codes to get details on possible upsells
      products.forEach(product => {
        if (product.productType === 'gas') {
          product?.discountProductDetails?.forEach((detail, index) => {
            if (detail.code === mpc.marketingProductCode) {
              detail.upsellMarketingProductCode !== null ? (downsellGasProduct = index) : (upsellGasProduct = index);
            }
          });
        }

        if (product.productType === 'electricity') {
          product?.discountProductDetails?.forEach((detail, index) => {
            if (detail.code === mpc.marketingProductCode) {
              detail.upsellMarketingProductCode !== null
                ? (downsellElectricityProduct = index)
                : (upsellElectricityProduct = index);
            }
          });
        }
      });
    });
  });

  //get the correct combinationMPCs based on hasUpsellGas / hasUpsellElectricity
  const productCodes = products.map(product => {
    if (product?.discountProductDetails?.length === 1) return product.discountProductDetails[0].code;

    if (product.productType === 'gas') {
      return product?.discountProductDetails?.[hasUpsellGas ? upsellGasProduct : downsellGasProduct].code;
    } else if (product.productType === 'electricity') {
      return product?.discountProductDetails?.[
        hasUpsellElectricity ? upsellElectricityProduct : downsellElectricityProduct
      ].code;
    } else if (product.productType === 'ketelComfort') {
      return product?.discountProductDetails?.[0].code;
    }

    return undefined;
  });

  // cashback
  // check if cashback product is present in the offer products array
  if (offer.products?.find(product => product.productType === 'cashback')) {
    offer?.productCombinations?.some(combination => {
      const MPCList = combination?.mpcCombinations?.map(mpc => mpc.marketingProductCode) ?? [];

      // productCodes.length + extra cashback product
      if (MPCList.length === productCodes.length + 1) {
        // select correct "product combination" based on the earliers selected "productCodes"
        if (productCodes.every(productCode => MPCList.includes(productCode))) {
          // find cashback product and add its marketingProductCode to the "productCodes"
          const marketingProductCode = combination.mpcCombinations?.find(
            combination => combination.productType === 'cashback',
          )?.marketingProductCode;

          marketingProductCode && productCodes.push(marketingProductCode);

          return true;
        }
      }

      return false;
    });
  }

  // grab appropriate productCombination based "productCodes"
  const productCombination = offer?.productCombinations?.find(combination => {
    const MPCList = combination?.mpcCombinations?.map(mpc => mpc.marketingProductCode) ?? [];

    if (MPCList.length === productCodes.length) {
      if (MPCList.every(MPCListItem => productCodes.includes(MPCListItem))) {
        return combination;
      }
    }

    return undefined;
  });

  // return proper productCombination
  return productCombination;
};

const selectOfferByOfferType = (
  offers: Products_Offers_DiscountResponse[],
  offerType: DC_Domain_Models_Products_DiscountType,
): Products_Offers_DiscountResponse => offers.find(offer => offer.offerType === offerType) ?? offers[0];

const selectCostDetailsByType = (
  costDetailsType: DC_Domain_Models_Products_CostDetailType,
  costDetails?: Products_Offers_ProductCostDetail[] | null,
): Products_VatModel | undefined => (costDetails ?? []).find(i => i.type === costDetailsType)?.costs;

export const selectProductCombinationIndex = (
  product: Products_Offers_DiscountProduct,
  productCombination?: Products_Offers_ProductCombination,
): number => {
  const individualCodes = productCombination?.mpcCombinations?.map(mpc => mpc.marketingProductCode) ?? [];
  return Math.max(product.discountProductDetails?.findIndex(item => individualCodes.includes(item.code)) ?? 0, 0);
};

export const getEnergyTypesFromProductTypes = (
  productCombinations: Array<Array<DC_Domain_Models_Products_ProductType>>,
): EnergyType[] => {
  const energyTypes: EnergyType[] = [];

  productCombinations?.forEach(productCombination => {
    // Threat "warmthEkv" as regular "warmth" product
    const warmthEkvIndex = productCombination.indexOf('warmthEkv');

    if (warmthEkvIndex !== -1) {
      productCombination[warmthEkvIndex] = 'warmth';
    }

    // Remove "warmtewisselaar" product since it is not an "energy" type product
    const warmtewisselaarIndex = productCombination.indexOf('warmtewisselaar');
    if (warmtewisselaarIndex !== -1) {
      productCombination.splice(warmtewisselaarIndex, 1);
    }

    // Product Combination Check: ELECTRICITY && GAS && SOMETHING
    if (productCombination.includes(EnergyType.Electricity) && productCombination.includes(EnergyType.Gas)) {
      // ELECTRICITY && GAS && WARMTH && TAPWATER
      if (productCombination.includes(EnergyType.Warmth) && productCombination.includes(EnergyType.TapWater)) {
        energyTypes.unshift(EnergyType.ElectricityAndGasAndWarmthAndTapWater);
      }
      // ELECTRICITY && GAS && WARMTH
      else if (productCombination.includes(EnergyType.Warmth)) {
        energyTypes.unshift(EnergyType.ElectricityAndGasAndWarmth);
      }
      // ELECTRICITY && GAS
      else {
        energyTypes.unshift(EnergyType.ElectricityAndGas);
      }
    }
    // Product Combination Check: ELECTRICITY && WARMTH && SOMETHING
    else if (productCombination.includes(EnergyType.Electricity) && productCombination.includes(EnergyType.Warmth)) {
      // ELECTRICITY && WARMTH && TAPWATER
      if (productCombination.includes(EnergyType.TapWater)) {
        energyTypes.unshift(EnergyType.ElectricityAndWarmthAndTapWater);
        // ELECTRICITY && WARMTH
      } else {
        energyTypes.unshift(EnergyType.ElectricityAndWarmth);
      }
    }
    // Procuct Combination Check: WARMTH && TAPWATER
    else if (productCombination.includes(EnergyType.Warmth) && productCombination.includes(EnergyType.TapWater)) {
      // WARMTH && TAPWATER
      energyTypes.unshift(EnergyType.WarmthAndTapWater);
    }
    // Product Combination Check: ELECTRICITY && GEOTHERMALHEATING (bronwarmte)
    else if (productCombination.includes(EnergyType.Electricity) && productCombination.includes('bronwarmte')) {
      energyTypes.unshift(EnergyType.ElectricityAndGeothermalHeating);
    }
    // Single Product Choice Check:
    else if (productCombination.length === 1) {
      const capitalizedProductType = capitalizeFirstLetter(productCombination[0]);

      if (productCombination.includes('bronwarmte')) {
        energyTypes.unshift(EnergyType.GeothermalHeating);
      } else if (capitalizedProductType in EnergyType) {
        energyTypes.unshift(EnergyType[capitalizedProductType as keyof typeof EnergyType]);
      }
    }
  });

  return energyTypes;
};

export const getProductTypesFromEnergyType = (
  energyType?: EnergyType,
): Array<DC_Domain_Models_Products_ProductType> => {
  switch (energyType) {
    case EnergyType.Electricity:
      return ['electricity'];
    case EnergyType.ElectricityAndGas:
      return ['electricity', 'gas'];
    case EnergyType.ElectricityAndGasAndWarmth:
      return ['electricity', 'gas', 'warmth', 'warmthEkv', 'warmtewisselaar'];
    case EnergyType.ElectricityAndGasAndWarmthAndTapWater:
      return ['electricity', 'gas', 'warmth', 'warmthEkv', 'tapwater', 'warmtewisselaar'];
    case EnergyType.ElectricityAndWarmth:
      return ['electricity', 'warmth', 'warmthEkv', 'warmtewisselaar'];
    case EnergyType.ElectricityAndWarmthAndTapWater:
      return ['electricity', 'warmth', 'warmthEkv', 'tapwater', 'warmtewisselaar'];
    case EnergyType.ElectricityAndGeothermalHeating:
      return ['electricity', 'bronwarmte', 'warmth'];
    case EnergyType.Gas:
      return ['gas'];
    case EnergyType.Warmth:
      return ['warmth', 'warmthEkv', 'warmtewisselaar'];
    case EnergyType.WarmthAndTapWater:
      return ['warmth', 'warmthEkv', 'tapwater', 'warmtewisselaar'];
    case EnergyType.GeothermalHeating:
      return ['bronwarmte', 'warmth'];
    default:
      return [];
  }
};

export const getProductIconTypeFromProductType = (
  productType: DC_Domain_Models_Products_ProductType,
): DetailsIconType => {
  switch (productType) {
    case 'bronwarmte':
    case 'warmth':
    case 'warmthEkv':
    case 'warmtewisselaar':
      return 'heating';

    case 'gas':
      return 'gas';

    case 'electricity':
      return 'electricity';

    case 'tapwater':
      return 'water';

    case 'huurapparaat':
      return 'boiler';

    default:
      return 'other';
  }
};

export const getProductCategoryFromProductType = (
  productType: DC_Domain_Models_Products_ProductType,
): 'commodity' | 'non-commodity' => {
  if (
    productType === 'gas' ||
    productType === 'electricity' ||
    productType === 'warmth' ||
    productType === 'warmthEkv' ||
    productType === 'tapwater'
  ) {
    return 'commodity';
  }

  return 'non-commodity';
};

const getProducts = (
  offer: Products_Offers_DiscountResponse,
  energyType: EnergyType,
  hasUpsellGas: boolean,
  hasUpsellElectricity: boolean,
  hasCrossSellRentalDevice: boolean,
): Product[] => {
  const selectedProductTypes = hasCrossSellRentalDevice
    ? (['huurapparaat'] as DC_Domain_Models_Products_ProductType[])
    : getProductTypesFromEnergyType(energyType);

  const selectedProductCombination = getMPCCodeFromProducts(
    offer,
    selectedProductTypes,
    hasUpsellGas,
    hasUpsellElectricity,
  );

  return (
    offer.products?.filter(product => {
      return selectedProductTypes.includes(product.productType);
    }) ?? []
  ).map(product => {
    const selectedProductIndex = selectProductCombinationIndex(product, selectedProductCombination);

    const selectedProductMonthlyCosts = product.discountProductDetails?.[selectedProductIndex]?.costDetails?.find(
      cost => cost.type === 'monthlyCosts',
    )?.costs;

    const selectedProductYearlyCosts = product.discountProductDetails?.[selectedProductIndex]?.costDetails?.find(
      cost => cost.type === 'yearlyCosts',
    )?.costs;

    const selectedProductDiscountDetails = product.discountProductDetails?.[selectedProductIndex];

    return {
      type: product.productType,

      code: product.discountProductDetails?.[selectedProductIndex].code ?? '',
      productCombinationCode: selectedProductCombination?.discountCode ?? undefined,

      variant: offer.offerType,

      title: product.discountProductDetails?.[selectedProductIndex].description ?? undefined,

      contractSpan: product.discountProductDetails?.[selectedProductIndex].contractSpan,

      costsMonthly: selectedProductMonthlyCosts,
      costsYearly: selectedProductYearlyCosts,

      costDetails: {
        [`${product.productType}CostDetails`]: selectedProductDiscountDetails?.productRateDetails?.reduce(
          (acc, item) =>
            Object.assign(acc, {
              [item.type]: {
                price: {
                  vat: item.vat,
                  vatExcluded: item.vatExcluded,
                  vatIncluded: item.vatIncluded,
                },
                components: item.components,
              },
            }),
          {},
        ),

        [`${product.productType}EnergyTaxDetails`]: selectedProductDiscountDetails?.energyTaxDetails?.tariffs?.reduce(
          (acc, item) =>
            Object.assign(acc, {
              [`bracketNr${item.bracketNr}`]: {
                active: item.usage > 0,
                usageMax: item.bracketUsageMax,
                usageMin: item.bracketUsageMin,
                price: {
                  vatAmount: item.vatAmount,
                  vatExcluded: item.vatExcluded,
                  vatIncluded: item.vatIncluded,
                },
              },
            }),
          {},
        ),

        [`${product.productType}Total`]: selectedProductDiscountDetails?.costDetails?.reduce(
          (acc, item) => Object.assign(acc, { [item.type ?? '']: item.costs }),
          {},
        ),
      },

      contractStartDate: product.discountProductDetails?.[selectedProductIndex].contractStartDate ?? undefined,
      contractEndDate: product.discountProductDetails?.[selectedProductIndex].contractEndDate ?? undefined,
    };
  });
};

const getCashbackProductDetails = (
  discountProduct: Products_Offers_DiscountProduct,
  selectedProductCombination?: Products_Offers_ProductCombination,
): Extensions_Products_DiscountProductDetail | undefined => {
  const cashbackProducts = discountProduct?.discountProductDetails;

  const cashbackMarketingProductCode = selectedProductCombination?.mpcCombinations?.find(
    (c: Products_Offers_MpcCombination) => c.productType === 'cashback',
  )?.marketingProductCode;

  const cashbackDetailIndex = cashbackProducts?.findIndex(c => c.code === cashbackMarketingProductCode) ?? 0;

  return discountProduct.discountProductDetails?.[cashbackDetailIndex];
};

const getCashbackValue = (productDetails: Extensions_Products_DiscountProductDetail): Price | undefined => {
  const costDetails = productDetails.costDetails?.find(
    c => c.type === 'cashBackDirect' || c.type === 'cashBackOnYearNote',
  );

  return {
    value: Number(costDetails?.value),
    type: costDetails?.type ?? 'unKnown',
  };
};

export const getUpsellProductPrice = (
  productType: DC_Domain_Models_Products_ProductType,
  flowContext: FlowContext,
  offer?: Products_Offers_PublicOffer,
): number => {
  const { isSME } = flowContext;

  const costsType = isSME ? 'vatExcluded' : 'vatIncluded';

  const gasProductPriceWithUpsellGas =
    getOffer(
      {
        ...flowContext,
        ...{ hasUpsellGas: productType === 'gas', hasUpsellElectricity: productType === 'electricity' },
      },
      offer,
    )?.products.find(product => {
      return product.type === productType;
    })?.costsMonthly?.[costsType] ?? 0;

  const gasProductPriceWithoutUpsellGas =
    getOffer({ ...flowContext, ...{ hasUpsellGas: false, hasUpsellElectricity: false } }, offer)?.products.find(
      product => {
        return product.type === productType;
      },
    )?.costsMonthly?.[costsType] ?? 0;

  return gasProductPriceWithUpsellGas - gasProductPriceWithoutUpsellGas;
};

export const getOffer = (flowContext: FlowContext, offer?: Products_Offers_PublicOffer): Offer | undefined => {
  const {
    offerType,
    energyType,
    hasUpsellGas = false,
    hasUpsellElectricity = false,
    hasCrossSellRentalDevice = false,
  } = flowContext;

  if (offer?.offers && offerType && energyType) {
    const selectedOffer = selectOfferByOfferType(offer.offers, offerType);

    const selectedProductTypes = getProductTypesFromEnergyType(energyType);
    const selectedProductCombination = getMPCCodeFromProducts(
      selectedOffer,
      selectedProductTypes,
      hasUpsellGas,
      hasUpsellElectricity,
    );

    const selectedProducts = getProducts(selectedOffer, energyType, hasUpsellGas, hasUpsellElectricity, false);

    let selectedProductCombinationMonthlyCosts = selectCostDetailsByType(
      'monthlyCosts',
      selectedProductCombination?.costDetails,
    );
    let selectedProductCombinationYearlyCosts = selectCostDetailsByType(
      'yearlyCosts',
      selectedProductCombination?.costDetails,
    );
    let selectedProductCombinationYearlyPromotionCosts = selectCostDetailsByType(
      'yearlyCostsPromotion',
      selectedProductCombination?.costDetails,
    );

    const selectedCashbackProduct = selectedOffer.products?.find(product => product.productType === 'cashback');
    const cashbackProductDetails =
      selectedCashbackProduct && getCashbackProductDetails(selectedCashbackProduct, selectedProductCombination);
    const selectedCashbackProductCosts = cashbackProductDetails && getCashbackValue(cashbackProductDetails);

    // conditional cross-sell rental device logic that will add rental device to the list of products
    // and add it's costs to the offer's costsMonthly / costsYearly / costsYearlyPromotion categories
    if (hasCrossSellRentalDevice) {
      const selectedRentalDeviceOffer = selectOfferByOfferType(offer.offers, 'rentalDevices');
      const selectedRentalDeviceProduct = getProducts(
        selectedRentalDeviceOffer,
        energyType,
        hasUpsellGas,
        hasUpsellElectricity,
        hasCrossSellRentalDevice,
      )[0];

      selectedProductCombinationMonthlyCosts = mergeOfferCosts(
        selectedProductCombinationMonthlyCosts,
        selectedRentalDeviceProduct?.costsMonthly,
      );

      selectedProductCombinationYearlyCosts = mergeOfferCosts(
        selectedProductCombinationYearlyCosts,
        selectedRentalDeviceProduct?.costsYearly,
      );

      selectedProductCombinationYearlyPromotionCosts = mergeOfferCosts(
        selectedProductCombinationYearlyPromotionCosts,
        selectedRentalDeviceProduct?.costsYearly,
      );

      selectedRentalDeviceProduct && selectedProducts.push(selectedRentalDeviceProduct);
    }

    return {
      type: offerType,

      products: selectedProducts,

      mostPopularChoice: selectedOffer.mostPopularChoice,

      costsMonthly: selectedProductCombinationMonthlyCosts,
      costsYearly: selectedProductCombinationYearlyCosts,

      ...(selectedProductCombinationYearlyCosts?.vatExcluded !==
        selectedProductCombinationYearlyPromotionCosts?.vatExcluded && {
        costsYearlyPromotion: selectedProductCombinationYearlyPromotionCosts,
      }),

      costDetails: {
        ...getOfferCostDetailsDetails(selectedProducts),

        ...(selectedCashbackProductCosts && {
          cashback: {
            value: selectedCashbackProductCosts.value ?? 0,
            type: selectedCashbackProductCosts.type,
          },
        }),
      },
      costDiscounts: {
        ...(selectedCashbackProductCosts && {
          productDetails: cashbackProductDetails,
          cashback: {
            value: selectedCashbackProductCosts.value ?? 0,
            type: selectedCashbackProductCosts.type,
          },
        }),
      },

      contractSpan: getOfferContractSpanFromProducts(selectedProducts),

      contractStartDate: selectedProducts?.find(product => product.contractStartDate)?.contractStartDate,
      contractEndDate: selectedProducts?.find(product => product.contractEndDate)?.contractEndDate,

      usages: offer.usages,
    };
  }

  return;
};

export const getOffers = (flowContext: FlowContext, offer?: Products_Offers_PublicOffer): Offer[] =>
  ['H', 'D', 'W', 'M', 'Y']
    .map(period =>
      (offer?.offers ?? [])
        // filter out non-commodity offers since we would like this function to return only
        // commodity related offers for the presentation purposes
        .filter(o => o.offerType !== 'rentalDevices')
        .filter(o => o.contractSpan?.byPeriod === period)
        .sort((a, b) => (a.contractSpan?.duration ?? 0) - (b.contractSpan?.duration ?? 0))
        .map(o => o.offerType && getOffer({ ...flowContext, ...{ offerType: o.offerType } }, offer)),
    )
    .flatMap(o => o) as Offer[];

export const getOfferTermsAndConditions = (
  flowContext: FlowContext,
  offer?: Products_Offers_PublicOffer,
): OfferTermsAndConditions[] | undefined => {
  const { offerType, energyType, hasUpsellGas = false, hasUpsellElectricity = false } = flowContext;

  if (offer?.offers && offerType && energyType) {
    const salesConditions: OfferTermsAndConditions[] = [];

    const selectedOffer = selectOfferByOfferType(offer.offers, offerType);
    const selectedProductTypes = getProductTypesFromEnergyType(energyType);
    const selectedProductCombination = getMPCCodeFromProducts(
      selectedOffer,
      selectedProductTypes,
      hasUpsellGas,
      hasUpsellElectricity,
    );

    const mpcCodes = selectedProductCombination?.mpcCombinations?.map(p => p.marketingProductCode);

    selectedOffer?.products?.forEach(p => {
      p.discountProductDetails
        ?.filter(d => mpcCodes?.includes(d.code))
        .map(p =>
          p.labels
            ?.filter(({ key }) => key?.includes('generaltermsandconditions-ismkb.'))
            .map(({ value, url }) => salesConditions.push({ title: `${value}`, href: `${url}` })),
        );
    });

    selectedProductCombination?.labels
      ?.filter(({ key }) => key?.includes('termsandconditions.'))
      .map(({ value, url }) => salesConditions.push({ title: `${value}`, href: `${url}` }));

    selectedOffer?.labels
      ?.filter(({ key }) => key?.includes('generaltermsandconditions-ismkb.'))
      .map(({ value, url }) => salesConditions.push({ title: `${value}`, href: `${url}` }));

    return salesConditions;
  }

  return;
};

const findMonthlyCosts = (item: { type: string }) => item.type === 'monthlyCosts';
export type PriceChangeStatus = 'increased' | 'decreased' | 'unchanged';

export function getMonthlyPriceChange(oldPrices: CostDetails[], newPrices: CostDetails[]): PriceChangeStatus {
  for (let i = 0; i < oldPrices.length; i++) {
    const oldMonthlyCosts = oldPrices[i].find(findMonthlyCosts);
    const newMonthlyCosts = newPrices[i].find(findMonthlyCosts);

    if (oldMonthlyCosts.costs.vatExcluded > newMonthlyCosts.costs.vatExcluded) {
      return 'decreased';
    }
    if (oldMonthlyCosts.costs.vatExcluded < newMonthlyCosts.costs.vatExcluded) {
      return 'increased';
    }
  }

  return 'unchanged';
}
