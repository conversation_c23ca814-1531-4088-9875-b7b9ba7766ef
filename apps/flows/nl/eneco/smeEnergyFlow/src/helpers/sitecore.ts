import { capitalizeFirstLetter } from '@common/string';
import { FlowContext } from '@eneco/flows/types';
import {
  DC_Domain_Models_Products_CostDetailType,
  DC_Domain_Models_Products_ProductType,
  Products_Offers_ContractSpan,
} from '@monorepo-types/dc';
import { ProductTypeLabels, CostDetailsExplanation, ParagraphField } from '@sitecore/types/OverviewStep';
import { FlowsNewEnergyOfferStep, PromotionTexts } from '@sitecore/types/SMEOfferStep';

import { checkOfferIncludesDynamicPricingProduct, checkOfferIncludesNonDynamicPricingProduct } from './misc';
import { EnergyType, Offer, OfferContractSpan, Product } from '../types';

type ProductTypeLabelsUnion = Partial<ProductTypeLabels>;

export const constructTextualDataKey = (contractSpan?: OfferContractSpan[], energyType?: EnergyType): string => {
  const getKeyPrefix = (productType: DC_Domain_Models_Products_ProductType, contractSpans?: OfferContractSpan[]) => {
    const contactSpan = contractSpans?.find(span => span.type === productType);
    return `${contactSpan?.duration}-${contactSpan?.byPeriod?.toLowerCase()}-${contactSpan?.type}`;
  };

  switch (energyType) {
    case EnergyType.Gas:
      return `${getKeyPrefix('gas', contractSpan)}`;

    case EnergyType.Electricity:
      return `${getKeyPrefix('electricity', contractSpan)}`;

    case EnergyType.ElectricityAndGas:
    case EnergyType.ElectricityAndGasAndWarmth:
    case EnergyType.ElectricityAndGasAndWarmthAndTapWater:
      return `${getKeyPrefix('electricity', contractSpan)}-${getKeyPrefix('gas', contractSpan)}`;

    case EnergyType.ElectricityAndWarmth:
    case EnergyType.ElectricityAndWarmthAndTapWater:
    case EnergyType.ElectricityAndGeothermalHeating:
      return `${getKeyPrefix('electricity', contractSpan)}-${getKeyPrefix('warmth', contractSpan)}`;

    default:
      return '';
  }
};

export const getProductTitleFromProductType = (
  productType: DC_Domain_Models_Products_ProductType,
  textualData?: ProductTypeLabelsUnion,
): string => {
  switch (productType) {
    case 'electricity':
      return textualData?.electricityProductLabel?.value ?? '';

    case 'gas':
      return textualData?.gasProductLabel?.value ?? '';

    case 'warmth':
    case 'warmthEkv':
      return textualData?.warmthProductLabel?.value ?? '';

    case 'warmtewisselaar':
      return textualData?.warmthExchangeSetLabel?.value ?? '';

    case 'tapwater':
      return textualData?.waterProductLabel?.value ?? '';

    default:
      return capitalizeFirstLetter(productType);
  }
};

export const getTitle = (key: string, textualData?: FlowsNewEnergyOfferStep): string | undefined =>
  textualData?.filter(i => i.fields.data?.id.value === key)[0]?.fields?.data?.name?.value;

export const getPromotionText = (key: string, textualData?: FlowsNewEnergyOfferStep): string | undefined =>
  textualData?.filter(i => i.fields.data?.id.value === key)[0]?.fields?.data?.promotionText?.value;

export const getCashbackTexts = (
  type?: DC_Domain_Models_Products_CostDetailType,
  textualData?: PromotionTexts,
): { title: string; text: string; label: string; trigger: string } | undefined => {
  // check for a supported cost details type
  if (type === 'cashBackDirect' || type === 'cashBackOnYearNote') {
    // replacement needed due to spelling differences between DC and Sitecore
    const cashbackType = type?.replace('cashBack', 'cashback') ?? '';

    return {
      title: textualData?.[`${cashbackType}RibbonContent` as keyof PromotionTexts]?.value ?? '',
      text: textualData?.[`${cashbackType}ExplanationContent` as keyof PromotionTexts]?.value ?? '',
      label: textualData?.[`${cashbackType}LabelContent` as keyof PromotionTexts]?.value ?? '',

      trigger: textualData?.cashbackExplanationTriggerText?.value ?? '',
    };
  }

  return;
};

export const getUsps = (key: string, textualData?: FlowsNewEnergyOfferStep): string[] => {
  const usps: string[] = [];
  const item = textualData?.filter(i => i.fields.data?.id.value === key)?.[0];

  item?.fields?.data?.usp1Text?.value && usps.push(item.fields.data.usp1Text.value);
  item?.fields?.data?.usp2Text?.value && usps.push(item.fields.data.usp2Text.value);
  item?.fields?.data?.usp3Text?.value && usps.push(item.fields.data.usp3Text.value);
  item?.fields?.data?.usp4Text?.value && usps.push(item.fields.data.usp4Text.value);

  return usps;
};

export const getPriceDetailsExplanationTexts = (
  flowContext: FlowContext,
  offer?: Offer,
  textualData?: CostDetailsExplanation,
): string => {
  const { hasSolarPanels } = flowContext;

  const isDynamicPricingContract =
    checkOfferIncludesDynamicPricingProduct(offer) && !checkOfferIncludesNonDynamicPricingProduct(offer);
  const isHybridContract =
    checkOfferIncludesDynamicPricingProduct(offer) && checkOfferIncludesNonDynamicPricingProduct(offer);
  const isRegularContract = !isDynamicPricingContract && !isHybridContract;

  return Object.keys(textualData || {})
    .sort(elem => (elem === 'lowHighTariffDifferenceParagraph' ? -1 : 0))
    .reduce((acc, elem, index) => {
      // exclude simple TextField values
      if (typeof textualData?.[elem as keyof CostDetailsExplanation]?.value === 'string') {
        return acc;
      }

      // exclude redeliveryParagraph or redeliveryDynamicPricingParagraph  based on "hasSolarPanels" value
      if (
        !hasSolarPanels &&
        (elem === 'redeliveryParagraph' ||
          elem === 'redeliveryDynamicPricingParagraph' ||
          elem === 'redeliveryHybridParagraph')
      ) {
        return acc;
      }

      // exclude certain ParagraphField based on a current "offerType"
      if (isDynamicPricingContract || isHybridContract) {
        if (
          elem === 'monthlyFeesParagraph' ||
          elem === 'redeliveryParagraph' ||
          elem === 'lowHighTariffDifferenceParagraph'
        ) {
          return acc;
        }

        if (isDynamicPricingContract) {
          if (elem === 'monthlyFeesHybridParagraph' || elem === 'redeliveryHybridParagraph') {
            return acc;
          }
        }

        if (isHybridContract) {
          if (elem === 'monthlyFeesDynamicPricingProductParagraph' || elem === 'redeliveryDynamicPricingParagraph') {
            return acc;
          }
        }
      }

      if (isRegularContract) {
        if (
          elem === 'monthlyFeesDynamicPricingProductParagraph' ||
          elem === 'redeliveryDynamicPricingParagraph' ||
          elem === 'monthlyFeesHybridParagraph' ||
          elem === 'redeliveryHybridParagraph'
        ) {
          return acc;
        }
      }

      const paragraph = (textualData?.[elem as keyof CostDetailsExplanation] as ParagraphField)?.value;

      // exclude empty ParagraphField values
      if (!paragraph?.title || !paragraph?.content) {
        return acc;
      }

      // add new paragraph
      acc += `<p><strong>${paragraph?.title}</strong><br/>${paragraph?.content}</p>`;

      // add additional line break if necessary
      acc += `${index + 1 < Object.keys(textualData || {}).length ? '<br/>' : ''}`;

      return acc;
    }, '');
};

export const getDurationInMonth = (contractSpan: Products_Offers_ContractSpan) => {
  if (contractSpan.byPeriod === 'Y') {
    return contractSpan.duration * 12;
  }

  return contractSpan.duration;
};

export const getCostDetails = (item: Product | Offer) => {
  const costs = {
    monthlyCosts: item.costsMonthly,
    yearlyCosts: item.costsYearly,
  };
  return Object.entries(costs).map(([type, value]) => {
    return {
      type,
      costs: value,
    };
  });
};
