export {
  energyTypeIncludes,
  checkOffersIncludesDynamicPricingProduct,
  checkOfferIncludesDynamicPricingProduct,
  checkOfferIncludesNonDynamicPricingProduct,
  getMaxUsageValue,
  checkAvailabilityOfferType,
  selectDefaultOfferType,
  multiplyByFloorArea,
  isReturnFromEmail,
  generateReturnFromEmailParam,
} from './misc';

export { formatInitials, addLeadingZero } from './format';

export {
  getOffer,
  getOffers,
  getEnergyTypesFromProductTypes,
  getMPCCodeFromProducts,
  getUpsellProductAvailability,
  getCrossSellRentalDeviceProductAvailability,
  getProductIconTypeFromProductType,
  getProductTypesFromEnergyType,
  getProductCategoryFromProductType,
  getOfferTermsAndConditions,
  getUpsellProductPrice,
  getMonthlyPriceChange,
} from './dc';
export type { PriceChangeStatus } from './dc';

export {
  constructTextualDataKey,
  getUsps,
  getTitle,
  getPromotionText,
  getCashbackTexts,
  getProductTitleFromProductType,
  getPriceDetailsExplanationTexts,
  getDurationInMonth,
  getCostDetails,
} from './sitecore';

export {
  ENECO_SME_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_DEFAULT,
  ENECO_SME_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX,
  ENECO_SME_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN,
  CONTRACT_CONSIDERATION_PERIOD_IN_DAYS,
  SOLAR_PANEL_OUTPUT,
  ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_HIGH,
  ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_LOW,
  ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_GAS,
  ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_WARMTH,
  ENECO_SME_ENERGY_FLOW_MAX_USAGE_VALUE_WATER,
} from './constants';
