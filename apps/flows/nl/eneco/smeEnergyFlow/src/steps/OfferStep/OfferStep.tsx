import React, { FC, useEffect, useMemo, useState } from 'react';

import { FeaturesReady } from '@growthbook/growthbook-react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useActor, useMachine } from '@xstate/react';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import useDC from '@dc/useDC';
import { ExperimentProvider } from '@dxp-experiments';
import { Header, Layout, Price } from '@eneco/flows';
import { useFlowContext } from '@eneco/flows/hooks';
import { useFormatter } from '@i18n';
import { DC_Domain_Models_Products_DiscountType } from '@monorepo-types/dc';
import { usePlaceholderContent } from '@sitecore/common';
import { SMEOfferStepRendering } from '@sitecore/types/manual/smeFlowExperiment/SMEOfferStep';
import { Box, Button, Divider, RadioGroup, Stack, Text, TextLink } from '@sparky';
import { useMediaQuery } from '@sparky/hooks';

import {
  EmailOfferDialog,
  EMAIL_OFFER_FEATURE_ID,
  Error,
  OverviewConsumption,
  PriceDetailsOverview,
  SMEOfferCard,
} from '../../components';
import {
  getOffers,
  getOffer,
  getUpsellProductAvailability,
  checkAvailabilityOfferType,
  selectDefaultOfferType,
  checkOffersIncludesDynamicPricingProduct,
  getCrossSellRentalDeviceProductAvailability,
} from '../../helpers';
import { useFlowTracking, useOfferChangeNotification } from '../../hooks';
import { offerMachine } from '../../machines';

interface FormFields {
  offerType?: DC_Domain_Models_Products_DiscountType;
}

export const OfferStep: FC = () => {
  const { flowService } = useFlowContext();

  const [flowState, sendToFlowMachine] = useActor(flowService);

  const {
    context: flowContext,
    context: {
      offerType,
      houseNumber,
      houseNumberSuffix,
      street,
      city,
      hasDoubleMeter,
      hasSolarPanels,
      hasUpsellGas,
      hasCrossSellRentalDevice,
      usageElectricityHigh,
      usageElectricityLow,
      usageGas,
      usageWarmth,
      usageWater,
      solarPanelsOutput,
      energyType,
      isSME,
      machines,
      machines: { offerMachineState } = {},
    },
  } = flowState;

  const [offerState, sendToOfferMachine] = useMachine(offerMachine, {
    state: offerMachineState ?? offerMachine.initialState,
  });

  const { context: { offer } = {} } = offerState;

  const { SmeOfferStep: { fields: textualData } = {} } = usePlaceholderContent<{
    SmeOfferStep: SMEOfferStepRendering;
  }>();

  const availableOffers = getOffers({ ...flowContext, hasCrossSellRentalDevice: false }, offer).splice(0, 2);

  const formSchema = yup.object({
    offerType: yup.string().required(),
  });

  const {
    handleSubmit,
    control,
    getValues,
    setValue,
    watch,
    formState: { errors, isSubmitSuccessful },
  } = useForm<FormFields>({
    mode: 'onSubmit',
    resolver: yupResolver(formSchema),
    defaultValues: {
      // For this step we won't be using the "defaultValue" straight away since the earlier
      // selected "offerType" might no longer be available due to flow context changes
    },
  });

  const { businessUnit, label } = useDC();
  const { trackViewItem, trackSelectItem, trackAddToCart } = useFlowTracking();

  const { format, address, currency } = useFormatter();

  const [isLoading, setIsLoading] = useState(true);
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [isFailed, setIsFailed] = useState(false);

  const [isNotFound, setIsNotFound] = useState(false);
  const [isNotAvailable, setIsNotAvailable] = useState(false);

  const [isInitialOfferSelection, setInitialOfferSelection] = useState(!offerType);

  const isSmallBreakpoint = !useMediaQuery('lg');

  const [shouldShowMoreInfoDialog, setShouldShowMoreInfoDialog] = useState(false);

  const selectedOfferType = watch('offerType');

  const selectedOffer = getOffer({ ...flowContext, ...{ offerType: selectedOfferType } }, offer);
  const costsType = isSME ? 'vatExcluded' : 'vatIncluded';

  useEffect(() => {
    sendToOfferMachine({
      type: 'GET_OFFER',
      config: {
        businessUnit,
        label,
      },
      values: { ...flowContext },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps -- lifecycle componentDidMount
  }, []);

  useEffect(() => {
    if (selectedOfferType) {
      isInitialOfferSelection && setInitialOfferSelection(false);
      selectedOffer && trackSelectItem(selectedOffer, offer, isInitialOfferSelection);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedOfferType, selectedOffer]);

  useEffect(() => {
    if (isSubmitSuccessful) {
      const isRentalDeviceCrossSellProductAvailable = getCrossSellRentalDeviceProductAvailability(offer);

      const isGasUpsellProductAvailable = getUpsellProductAvailability(
        'gas',
        { ...flowContext, ...{ offerType: selectedOfferType } },
        offer,
      );

      selectedOffer && trackAddToCart(selectedOffer, offer);

      sendToFlowMachine({
        type: 'NEXT_PAGE',
        values: {
          ...getValues(),
          isRentalDeviceCrossSellProductAvailable,
          isGasUpsellProductAvailable,
          hasCrossSellRentalDevice: hasCrossSellRentalDevice && isRentalDeviceCrossSellProductAvailable,
          hasUpsellGas: hasUpsellGas && isGasUpsellProductAvailable,
          machines: { ...machines, ...{ offerMachineState: offerState } },
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSubmitSuccessful]);

  useEffect(() => {
    if (!offerState.matches('IDLE')) {
      setIsLoading(offerState.matches('FETCHING'));
      setIsFailed(!offerState.matches('FETCHING') && !offerState.matches('SUCCESS'));
      setIsSuccessful(offerState.matches('SUCCESS'));

      setIsNotFound(offerState.matches('ERROR_NOT_FOUND'));
      setIsNotAvailable(offerState.matches('ERROR_NOT_AVAILABLE'));
    }

    if (offerState.matches('SUCCESS')) {
      setValue(
        'offerType',
        checkAvailabilityOfferType(availableOffers, offerType) ?? selectDefaultOfferType(availableOffers),
      );

      setShouldShowMoreInfoDialog(
        !!(
          checkOffersIncludesDynamicPricingProduct(availableOffers) &&
          textualData?.content?.moreInfoDialog?.value?.triggerText
        ),
      );

      availableOffers.forEach(availableOffer => {
        trackViewItem(availableOffer, offer);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [offerState]);

  const priceDetailsOverviewTextualData = useMemo(
    () =>
      textualData && {
        costDetails: textualData['costDetails'],
        costDetailsExplanation: {
          ...textualData['costDetailsExplanation'],
          explanationTitle: textualData['costDetailsExplanation'].title,
        },
        electri: textualData['costDetails'],
        electricityCostDetails: textualData['electricityCostDetails'],
        gasCostDetails: textualData['gasCostDetails'],
        warmthCostDetails: textualData['warmthCostDetails'],
        promotionTexts: textualData['promotionTexts'],
        productTypeLabels: textualData['productTypeLabels'],
      },
    [textualData],
  );

  const notification = useOfferChangeNotification(availableOffers);
  const offerChangeNotification = (
    <ExperimentProvider featureId={EMAIL_OFFER_FEATURE_ID}>
      <FeaturesReady fallback={null} timeout={1000}>
        {notification}
      </FeaturesReady>
    </ExperimentProvider>
  );

  const emailOffer = (
    <ExperimentProvider featureId={EMAIL_OFFER_FEATURE_ID}>
      <FeaturesReady fallback={null} timeout={1000}>
        <EmailOfferDialog textualData={textualData} availableOffers={availableOffers} />
      </FeaturesReady>
    </ExperimentProvider>
  );

  return (
    <>
      <Layout.Content
        variant={availableOffers.length === 1 ? 'A' : availableOffers.length === 2 ? 'C' : 'D'}
        isLoading={isLoading}
        handleSubmit={handleSubmit}>
        {isSuccessful && (
          <>
            {offerChangeNotification}
            <Header align="center">
              <Header.Title>{textualData?.content?.title?.value}</Header.Title>
            </Header>

            {shouldShowMoreInfoDialog && (
              <Stack alignX="center">
                <TrackedDialog
                  title={textualData?.content?.moreInfoDialog?.value?.title}
                  trigger={
                    <TextLink emphasis="high">{textualData?.content?.moreInfoDialog?.value?.triggerText}</TextLink>
                  }>
                  <RichText html={textualData?.content?.moreInfoDialog?.value?.content} />
                </TrackedDialog>
              </Stack>
            )}

            <Box>
              {!isSmallBreakpoint && (
                <Box paddingBottom="4" paddingTop="10">
                  <OverviewConsumption
                    textualData={{
                      description: format(textualData?.footer?.content?.value ?? '', {
                        address: address.medium({ street, houseNumber, houseNumberSuffix, city }),
                      }),
                      redeliveryLabel: textualData?.footer?.redeliveryLabel?.value,
                      electricityLabel: textualData?.footer?.electricityLabel?.value,
                      electricityHighLabel: textualData?.footer?.electricityHighLabel?.value,
                      electricityLowLabel: textualData?.footer?.electricityLowLabel?.value,
                      gasLabel: textualData?.footer?.gasLabel?.value,
                      warmthLabel: textualData?.footer?.warmthLabel?.value,
                      waterLabel: textualData?.footer?.waterLabel?.value,
                    }}
                    data={{
                      hasDoubleMeter,
                      hasSolarPanels,
                      usageElectricityHigh,
                      usageElectricityLow,
                      usageGas,
                      usageWarmth,
                      usageWater,
                      solarPanelsOutput,
                    }}
                  />
                </Box>
              )}
              <Box padding={{ initial: 0, md: 6 }}>
                <Stack gap="6">
                  <Controller
                    control={control}
                    name="offerType"
                    render={({ field: { onChange, value, name } }) => (
                      <RadioGroup
                        aria-labelledby="offerType"
                        name={name}
                        value={value}
                        direction={availableOffers.length === 2 ? { initial: 'column', lg: 'row' } : 'column'}
                        wrap={false}
                        alignY="center"
                        onValueChange={onChange}
                        error={errors['offerType']?.message}>
                        {availableOffers.map((offer, index) => (
                          <Stack.Item key={`${offer.type}-${index}`}>
                            <SMEOfferCard
                              key={offer.type}
                              offer={offer}
                              textualData={textualData}
                              flowContext={flowContext}
                              isSME={isSME}
                              energyType={energyType}
                            />
                          </Stack.Item>
                        ))}
                      </RadioGroup>
                    )}
                  />
                  {!isSmallBreakpoint && (
                    <Stack.Item>
                      <Divider />
                    </Stack.Item>
                  )}
                  <Stack.Item>
                    {selectedOffer?.products?.map(product => (
                      <Stack alignX="justify" direction="row" key={product.code}>
                        <Box padding="1">
                          <Text>{product.title}</Text>
                        </Box>
                        <Box padding="1">
                          <Text>{currency.euro(product.costsYearly?.[costsType] ?? 0)}</Text>
                        </Box>
                      </Stack>
                    ))}
                  </Stack.Item>
                  <Stack.Item>
                    <Divider emphasis="high" />
                  </Stack.Item>
                  <Stack.Item>
                    <Stack gap="2" alignX="justify" direction="row">
                      <Box padding="1">
                        <Text weight="bold">{textualData?.costDetails?.totalCostsLabel?.value}</Text>
                      </Box>
                      <Box padding="1">
                        <Price suffix="per maand" emphasis="medium">
                          {selectedOffer?.costsMonthly?.[costsType] ?? 0}
                        </Price>
                      </Box>
                    </Stack>
                    <Stack gap="2" alignX="justify" direction="row">
                      <Stack.Item>
                        <Text size="BodyS">
                          <TrackedDialog
                            title={textualData?.content?.priceDetailsDialog?.value?.title}
                            description={<RichText html={textualData?.content?.priceDetailsDialog?.value?.content} />}
                            trigger={
                              <TextLink emphasis="high">
                                {textualData?.content?.priceDetailsDialog?.value?.triggerText}
                              </TextLink>
                            }>
                            <PriceDetailsOverview
                              offer={selectedOffer}
                              flowContext={flowContext}
                              textualData={priceDetailsOverviewTextualData}
                            />
                          </TrackedDialog>
                        </Text>
                      </Stack.Item>
                      <Box padding="1">
                        <Price suffix="per jaar" emphasis="low">
                          {selectedOffer?.costsYearly?.[costsType] ?? 0}
                        </Price>
                      </Box>
                    </Stack>
                  </Stack.Item>
                </Stack>
              </Box>
            </Box>

            <Box paddingBottom="2">
              <Text size="BodyS" weight="regular">
                {textualData?.content?.legalText?.value}
              </Text>
            </Box>
            <Stack direction={{ initial: 'column' }} alignX="center" gap="4">
              <Stack.Item grow>
                <Button type="submit" isLoading={isLoading}>
                  {textualData?.content?.nextStepText?.value}
                </Button>
              </Stack.Item>
              <Stack.Item grow>{emailOffer}</Stack.Item>
            </Stack>
          </>
        )}

        {isFailed && (
          <Error
            type={'GENERIC'}
            alignY="center"
            textualData={{
              notification: isNotAvailable
                ? textualData?.genericError?.errorNotAvailableNotification
                : isNotFound
                  ? textualData?.genericError?.errorNotFoundNotification
                  : textualData?.genericError?.errorNotification,
              buttonLabel: !isNotAvailable && !isNotFound ? textualData?.genericError?.tryAgainButtonText : undefined,
            }}
          />
        )}
      </Layout.Content>

      {isSmallBreakpoint && (
        <Layout.Footer isLoading={isLoading || isFailed}>
          <Box paddingBottom="10" paddingTop="4">
            <OverviewConsumption
              textualData={{
                description: format(textualData?.footer?.content?.value ?? '', {
                  address: address.medium({ street, houseNumber, houseNumberSuffix, city }),
                }),
                redeliveryLabel: textualData?.footer?.redeliveryLabel?.value,
                electricityLabel: textualData?.footer?.electricityLabel?.value,
                electricityHighLabel: textualData?.footer?.electricityHighLabel?.value,
                electricityLowLabel: textualData?.footer?.electricityLowLabel?.value,
                gasLabel: textualData?.footer?.gasLabel?.value,
                warmthLabel: textualData?.footer?.warmthLabel?.value,
                waterLabel: textualData?.footer?.waterLabel?.value,
              }}
              data={{
                hasDoubleMeter,
                hasSolarPanels,
                usageElectricityHigh,
                usageElectricityLow,
                usageGas,
                usageWarmth,
                usageWater,
                solarPanelsOutput,
              }}
            />
          </Box>
        </Layout.Footer>
      )}
    </>
  );
};
