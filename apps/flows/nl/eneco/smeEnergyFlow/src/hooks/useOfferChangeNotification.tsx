import { useEffect, useRef, useState } from 'react';

import { useActor } from '@xstate/react';

import { removeQueryParam } from '@common/path';
import { useFlowContext } from '@eneco/flows/src/hooks';

import { OfferChangeNotification } from '../components/OfferChangeNotification';
import { getCostDetails, getMonthlyPriceChange, isReturnFromEmail, PriceChangeStatus } from '../helpers';
import { RETURN_SOURCE_PARAM } from '../helpers/misc';
import { Offer } from '../types';

export const useOfferChangeNotification = (availableOffers: Offer[]) => {
  const { flowService, basketService } = useFlowContext();
  const returnedFromEmailRef = useRef(isReturnFromEmail());
  const [flowState] = useActor(flowService);
  const [basketState] = useActor(basketService);
  const [priceChange, setPriceChange] = useState<PriceChangeStatus>('unchanged');
  const {
    context: { companyName },
  } = flowState;

  const {
    // @ts-ignore basketState is poorly typed. Should be resolved by migrating to the flow v2
    context: { cachedOffers },
  } = basketState;

  useEffect(() => {
    removeQueryParam(RETURN_SOURCE_PARAM);
  }, []);

  useEffect(() => {
    if (availableOffers?.length > 0 && cachedOffers?.length > 0) {
      const currentCosts = availableOffers.map((currentOffer: Offer) => getCostDetails(currentOffer));
      const cachedCosts = cachedOffers.map((cachedOffer: Offer) => cachedOffer.costDetails);
      setPriceChange(getMonthlyPriceChange(currentCosts, cachedCosts));
    }
  }, [availableOffers, cachedOffers]);

  if (returnedFromEmailRef.current && companyName) {
    return <OfferChangeNotification companyName={companyName} priceChange={priceChange} />;
  }
  return null;
};
