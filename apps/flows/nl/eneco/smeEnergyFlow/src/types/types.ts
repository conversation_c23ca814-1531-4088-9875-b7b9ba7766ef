import {
  DC_Domain_Models_Products_CostDetailType,
  DC_Domain_Models_Products_DiscountType,
  DC_Domain_Models_Products_ProductType,
  Products_Offers_ContractSpan,
  Products_Offers_Usages,
  Products_VatModel,
  Extensions_Products_DiscountProductDetail,
} from '@monorepo-types/dc';

export interface Price {
  value: string | number;
  previousValue?: string | number;

  period?: string;
  type?: DC_Domain_Models_Products_CostDetailType;
}

export interface CostDetails {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

export interface Product {
  type: DC_Domain_Models_Products_ProductType;

  title?: string;
  tag?: string;

  code: string; // SKU
  productCombinationCode?: string;

  variant?: DC_Domain_Models_Products_DiscountType;

  contractSpan?: Products_Offers_ContractSpan;

  costsMonthly?: Products_VatModel;
  costsYearly?: Products_VatModel;

  costDetails: Record<string, unknown>;

  contractEndDate?: string;
  contractStartDate?: string;

  mostPopularChoice?: boolean;
}

export interface OfferContractSpan extends Products_Offers_ContractSpan {
  type: DC_Domain_Models_Products_ProductType;
}

export interface Offer {
  type: DC_Domain_Models_Products_DiscountType;

  title?: string;
  tag?: string;

  usps?: string[];

  products: Product[];

  mostPopularChoice: boolean;

  costsMonthly?: Products_VatModel;
  costsYearly?: Products_VatModel;
  costsYearlyPromotion?: Products_VatModel;

  costDiscounts: {
    cashback?: Price;
    productDetails?: Extensions_Products_DiscountProductDetail;
  };

  costDetails: CostDetails;

  contractSpan?: OfferContractSpan[];

  contractEndDate?: string;
  contractStartDate?: string;

  usages?: Products_Offers_Usages;
}

export interface OfferTermsAndConditions {
  title: string;
  href: string;
}
