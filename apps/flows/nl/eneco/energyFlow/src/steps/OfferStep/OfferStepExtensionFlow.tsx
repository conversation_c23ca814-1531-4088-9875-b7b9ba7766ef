import React, { FC, useEffect, useMemo, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import useDC from '@dc/useDC';
import { FlowContext } from '@eneco/flows/types';
import { Header, Layout, NextButton } from '@eneco/flows2';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { useFormatter } from '@i18n';
import { usePlaceholderContent } from '@sitecore/common';
import { OfferStepRendering, Fields } from '@sitecore/types/OfferStep';
import {
  Bleed,
  Box,
  Carousel,
  Divider,
  Expandable,
  Grid,
  NotificationBox,
  RadioGroup,
  RadioTile,
  Text,
  TextLink,
  VisuallyHidden,
} from '@sparky';
import { useMediaQuery } from '@sparky/hooks';

import { OverviewConsumption, Ribbon, TariffDetailsOverview, Error, ProductCard } from '../../components';
import { FlowConsumerContextEnergy } from '../../context';
import {
  getOffers,
  getOffer,
  constructTextualDataKey,
  getCashbackTexts,
  getUpsellProductAvailability,
  checkAvailabilityOfferType,
  selectDefaultDiscountCode,
  checkOffersIncludesDynamicPricingProduct,
  checkOfferIncludesDynamicPricingProduct,
  getCrossSellRentalDeviceProductAvailability,
  checkOfferIncludesNonDynamicPricingProduct,
} from '../../helpers';
import { handleNbaFeedback } from '../../helpers/dc';
import { useFlowTracking } from '../../hooks';
import { useProductItemContent } from '../../hooks/sitecore/useProductItemContent';
import { useFlowNavigation } from '../../hooks/updateContextAndGoToNextStep';
import { useOfferMachine } from '../../hooks/useOfferMachine';
import { Offer, EnergyType } from '../../types';

interface FormFields {
  discountCode?: string;
}

export const checkOffersIncludesNonDynamicPricingProduct = (offers: Offer[]): boolean => {
  return !!offers.find(offer => checkOfferIncludesNonDynamicPricingProduct(offer));
};

const OfferCard: FC<{
  offer: Offer;
  isSME?: boolean;
  energyType?: EnergyType;
  textualData?: Fields;
  flowContext?: FlowContext;
  isOpen: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ offer, isSME, energyType = EnergyType.Electricity, textualData, flowContext, isOpen, setOpen }) => {
  const { format, currency } = useFormatter();

  const textualDataKey = constructTextualDataKey(offer.contractSpan, energyType);

  const { getProductDetails } = useProductItemContent(textualData?.productData?.productDataList);

  const productDetails = getProductDetails(textualDataKey);

  // Ensures that any product item is not rendered unless it is mapped on Sitecore, even though that's exposed by the api
  if (!productDetails) {
    return null;
  }

  const { title, promotionText, usps } = productDetails;

  const costsType = isSME ? 'vatExcluded' : 'vatIncluded';

  const cashbackTexts = getCashbackTexts(
    offer?.costDiscounts?.cashback?.type,
    textualData?.promotionTexts,
    textualData?.giftData,
    offer?.costDiscounts?.cashback?.value,
  );

  return (
    <ProductCard
      id={offer.type ?? ''}
      title={title}
      tag={offer.mostPopularChoice ? promotionText : undefined}
      usps={usps}
      price={{
        value: offer?.costsMonthly?.[costsType] ?? 0,
        period: checkOfferIncludesDynamicPricingProduct(offer)
          ? textualData?.productData?.perMonthEstimatedLabel?.value
          : textualData?.productData?.perMonthLabel?.value,
      }}
      ribbon={
        cashbackTexts ? (
          <Ribbon
            size="small"
            emphasis="high"
            title={format(cashbackTexts.title, {
              [`${offer?.costDiscounts?.cashback?.type}`]: currency.euroNoFractionDigits(
                offer?.costDiscounts?.cashback?.value ?? 0,
              ),
            })}
            text={cashbackTexts.text}
            trigger={cashbackTexts?.trigger}
          />
        ) : null
      }>
      <Expandable isOpen={isOpen} setOpen={setOpen}>
        <Box paddingRight="6">
          <Expandable.Trigger isTransparent aria-labelledby={`${offer.type}-tariff-details`}>
            <Box id={`${offer.type}-tariff-details`} paddingLeft="6" paddingY="3">
              {textualData?.productData?.tariffsTriggerText?.value}
            </Box>
          </Expandable.Trigger>
        </Box>

        <Expandable.Content>
          <Bleed horizontal="6">
            <Divider />
          </Bleed>
          <Box paddingX={6} paddingY={3}>
            <TariffDetailsOverview offer={offer} textualData={textualData} flowContext={flowContext} />
          </Box>
        </Expandable.Content>
      </Expandable>
    </ProductCard>
  );
};

export const OfferStepExtensionFlow: FC = () => {
  const { businessUnit, label } = useDC();
  const { useFlowSelector, useFlowActorRef } = useFlowHooks<FlowConsumerContextEnergy>();
  const flowState = useFlowSelector(state => state);
  const sendToFlowMachine = useFlowActorRef().send;
  const { updateContextAndGoToNextStep } = useFlowNavigation();
  const [productFilter, setProductFilter] = useState('');
  const {
    context: flowContext,
    context: {
      discountCode,
      houseNumber,
      houseNumberSuffix,
      street,
      city,
      hasDoubleMeter,
      hasSolarPanels,
      hasUpsellGas,
      hasCrossSellRentalDevice,
      usageElectricityHigh,
      usageElectricityLow,
      usageGas,
      usageWarmth,
      usageWater,
      solarPanelsOutput,
      energyType,
      isSME,
      isUsageDetailsEditProhibited,
    },
  } = flowState;

  const { offerState, sendToOfferMachine } = useOfferMachine(businessUnit, label);

  const { context: { offer } = {} } = offerState;

  const { OfferStep: { fields: textualData, params: { allowDynamicOrHybridOnly = 'false' } = {} } = {} } =
    usePlaceholderContent<{ OfferStep: OfferStepRendering }>();

  const offers = getOffers({ ...flowContext, hasCrossSellRentalDevice: false }, offer);

  const showProductToggle =
    allowDynamicOrHybridOnly === 'false' &&
    checkOffersIncludesDynamicPricingProduct(offers) &&
    checkOffersIncludesNonDynamicPricingProduct(offers);

  const availableOffers = useMemo(() => {
    if (!showProductToggle) {
      return offers;
    }

    if (!productFilter) {
      return [];
    }

    return offers.filter(offer =>
      productFilter === 'dynamic'
        ? checkOfferIncludesDynamicPricingProduct(offer)
        : !checkOfferIncludesDynamicPricingProduct(offer),
    );
  }, [offers, productFilter, showProductToggle]);

  const formSchema = yup.object({
    discountCode: yup.string().required(textualData?.content?.noOfferSelectedNotification?.value.content ?? ''),
  });

  const {
    handleSubmit,
    control,
    getValues,
    setValue,
    watch,
    formState: { errors, isSubmitSuccessful },
  } = useForm<FormFields>({
    mode: 'onSubmit',
    resolver: yupResolver(formSchema),
    defaultValues: {
      // For this step we won't be using the "defaultValue" straight away since the earlier
      // selected "discountCode" might no longer be available due to flow context changes
    },
  });

  const { trackViewItem, trackSelectItem, trackAddToCart } = useFlowTracking();

  const { format, address } = useFormatter();

  const [isLoading, setIsLoading] = useState(true);
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [isFailed, setIsFailed] = useState(false);

  const [isNotFound, setIsNotFound] = useState(false);
  const [isNotAvailable, setIsNotAvailable] = useState(false);
  const [isNotAuthenticated, setIsNotAuthenticated] = useState(false);

  const [isInitialOfferSelection, setInitialOfferSelection] = useState(!discountCode);
  const [isPriceDetailsOpen, setIsPriceDetailsOpen] = useState(false);

  const isLargeBreakpoint = useMediaQuery('lg');
  const isMediumBreakpoint = useMediaQuery('md');
  const isExtraLargeBreakpoint = useMediaQuery('xl');

  const [shouldShowMoreInfoDialog, setShouldShowMoreInfoDialog] = useState(false);
  const shouldShowCarousel = !(
    isLargeBreakpoint ||
    (!isLargeBreakpoint && availableOffers.length < 2) ||
    (isMediumBreakpoint && availableOffers.length < 3) ||
    availableOffers.length === 1
  );
  const shouldShowCarouselIndicators = shouldShowCarousel && (!isExtraLargeBreakpoint || availableOffers.length > 3);
  const shouldAlignCrouselItems = shouldShowCarousel && isExtraLargeBreakpoint && availableOffers.length === 3;

  const selectedDiscountCode = watch('discountCode');
  const selectedDiscountCodeIndex = Math.max(
    availableOffers?.findIndex(offer => offer.type === selectedDiscountCode) ?? 0,
    0,
  );

  useEffect(() => {
    if (!availableOffers.length || availableOffers?.some(offer => offer.type === selectedDiscountCode)) return;
    setValue('discountCode', selectDefaultDiscountCode(availableOffers));
  }, [availableOffers, selectedDiscountCode, setValue]);

  useEffect(() => {
    sendToOfferMachine({
      type: 'GET_EXTENSION_OFFER',
      flowContext: { ...flowContext },
      allowDynamicOrHybridOnly: allowDynamicOrHybridOnly === 'true',
    });
  }, []);

  useEffect(() => {
    if (selectedDiscountCode) {
      if (isInitialOfferSelection) setInitialOfferSelection(false);

      const selectedOffer = getOffer({ ...flowContext, ...{ discountCode: selectedDiscountCode } }, offer);
      if (selectedOffer) trackSelectItem(selectedOffer, offer, isInitialOfferSelection);
    }
  }, [selectedDiscountCode]);

  useEffect(() => {
    if (isSubmitSuccessful) {
      const isRentalDeviceCrossSellProductAvailable = getCrossSellRentalDeviceProductAvailability(offer);

      const isGasUpsellProductAvailable = getUpsellProductAvailability(
        'gas',
        { ...flowContext, ...{ discountCode: selectedDiscountCode } },
        offer,
      );

      const selectedOffer = getOffer({ ...flowContext, ...{ discountCode: selectedDiscountCode } }, offer);

      if (selectedOffer) trackAddToCart(selectedOffer, offer);
      if (selectedOffer?.nbaContext) {
        const { serviceLocationId, contextId, actionId, variationId, servingPointId } = selectedOffer.nbaContext;

        handleNbaFeedback(
          businessUnit,
          label,
          'Success',
          serviceLocationId ?? '',
          contextId ?? '',
          actionId,
          variationId,
          servingPointId,
        );
      }

      updateContextAndGoToNextStep({
        ...getValues(),
        isRentalDeviceCrossSellProductAvailable,
        isGasUpsellProductAvailable,
        hasCrossSellRentalDevice: hasCrossSellRentalDevice && isRentalDeviceCrossSellProductAvailable,
        hasUpsellGas: hasUpsellGas && isGasUpsellProductAvailable,
      });
    }
  }, [isSubmitSuccessful]);

  useEffect(() => {
    if (!offerState.matches('IDLE')) {
      setIsLoading(offerState.matches('GET_EXTENSION'));
      setIsFailed(!offerState.matches('FETCHING') && !offerState.matches('SUCCESS'));
      setIsSuccessful(offerState.matches('SUCCESS'));

      setIsNotFound(offerState.matches('ERROR_NOT_FOUND'));
      setIsNotAvailable(offerState.matches('ERROR_NOT_AVAILABLE'));
      setIsNotAuthenticated(offerState.matches('ERROR_NOT_AUTHENTICATED'));
    }

    if (offerState.matches('SUCCESS')) {
      const selectedOfferType = checkAvailabilityOfferType(offers, discountCode);

      if (selectedOfferType) {
        const isDynamicPricingProduct = checkOfferIncludesDynamicPricingProduct(
          offers.find(offer => offer.type === selectedOfferType),
        );
        setValue('discountCode', selectedOfferType);
        setProductFilter(isDynamicPricingProduct ? 'dynamic' : 'fixed');
      }

      setShouldShowMoreInfoDialog(
        !!(
          checkOffersIncludesDynamicPricingProduct(offers) && textualData?.content?.moreInfoDialog?.value?.triggerText
        ),
      );

      offers.forEach(availableOffer => {
        trackViewItem(availableOffer, offer);
        if (availableOffer.nbaContext?.servingPointId) {
          handleNbaFeedback(
            businessUnit,
            label,
            'Served',
            availableOffer.nbaContext.serviceLocationId ?? '',
            availableOffer.nbaContext.contextId ?? '',
            availableOffer.nbaContext.actionId,
            availableOffer.nbaContext.variationId,
            availableOffer.nbaContext.servingPointId ?? '',
          );
          handleNbaFeedback(
            businessUnit,
            label,
            'Viewed',
            availableOffer.nbaContext.serviceLocationId ?? '',
            availableOffer.nbaContext.contextId ?? '',
            availableOffer.nbaContext.actionId,
            availableOffer.nbaContext.variationId,
            availableOffer.nbaContext.servingPointId ?? '',
          );
        }
      });
    }
  }, [offerState]);

  return (
    <>
      <Layout.Content variant="C" isLoading={isLoading} handleSubmit={handleSubmit}>
        {isSuccessful && (
          <>
            <Header>
              <Header.Title>{textualData?.content?.title?.value}</Header.Title>
              <Header.Text>{textualData?.content?.text?.value}</Header.Text>
            </Header>

            {textualData?.content?.notification?.value?.content && (
              <NotificationBox
                isAlert={false}
                variant={textualData?.content?.notification?.value?.variant}
                title={textualData?.content?.notification?.value?.title}
                text={<RichText html={textualData?.content?.notification?.value?.content} />}
              />
            )}

            {shouldShowMoreInfoDialog && (
              <TrackedDialog
                title={textualData?.content?.moreInfoDialog?.value?.title}
                trigger={
                  <TextLink emphasis="high">{textualData?.content?.moreInfoDialog?.value?.triggerText}</TextLink>
                }>
                <RichText html={textualData?.content?.moreInfoDialog?.value?.content} />
              </TrackedDialog>
            )}

            {showProductToggle && (
              <Box paddingBottom="5">
                {textualData?.content?.contractTypeTitle?.value && (
                  <Box paddingY="3">
                    <Text weight="bold">{textualData?.content.contractTypeTitle.value}</Text>
                  </Box>
                )}
                <Grid gridTemplateColumns={'1fr 1fr'} gap="6">
                  <Grid.Item gridColumn={{ initial: '1/-1', md: 1 }}>
                    <VisuallyHidden id="productFilter">
                      {textualData?.productData?.contractTypeRadio?.value?.label}
                    </VisuallyHidden>
                    <RadioGroup
                      aria-labelledby="productFilter"
                      name="Productfilter"
                      value={productFilter}
                      onValueChange={setProductFilter}>
                      <Grid gridTemplateColumns={'1fr 1fr'} gap="6">
                        {textualData?.productData?.contractTypeRadio?.value?.options?.map(option => (
                          <RadioTile key={option.name} value={option.name}>
                            {option.label}
                          </RadioTile>
                        ))}
                      </Grid>
                    </RadioGroup>
                  </Grid.Item>
                </Grid>
              </Box>
            )}

            {availableOffers.length > 0 ? (
              <>
                <Controller
                  control={control}
                  name="discountCode"
                  render={({ field: { onChange, value, name } }) => (
                    <RadioGroup
                      aria-labelledby="discountCode"
                      name={name}
                      value={value}
                      direction={availableOffers.length >= 2 ? { initial: 'column', md: 'row' } : 'row'}
                      alignY="start"
                      wrap={false}
                      onValueChange={onChange}
                      error={errors['discountCode']?.message}>
                      {shouldShowCarousel ? (
                        <Bleed
                          horizontal={!isLargeBreakpoint ? { initial: 6, md: 10 } : undefined}
                          right={shouldAlignCrouselItems ? 24 : !isLargeBreakpoint ? 6 : 0}>
                          <Carousel
                            key={value}
                            showIndicator={shouldShowCarouselIndicators}
                            scrollBehavior="snap"
                            scrollMarginX="gridGutter"
                            hasCardPadding
                            layout="default"
                            startAt={selectedDiscountCodeIndex}
                            scrollTo={selectedDiscountCodeIndex}>
                            {availableOffers.map(offer => (
                              <Carousel.Item key={offer.type}>
                                <OfferCard
                                  key={offer.type}
                                  isOpen={isPriceDetailsOpen}
                                  setOpen={setIsPriceDetailsOpen}
                                  offer={offer}
                                  textualData={textualData}
                                  flowContext={flowContext}
                                  isSME={isSME}
                                  energyType={energyType}
                                />
                              </Carousel.Item>
                            ))}
                          </Carousel>
                        </Bleed>
                      ) : availableOffers.length === 1 && !isMediumBreakpoint ? (
                        availableOffers.map(offer => (
                          <OfferCard
                            key={offer.type}
                            isOpen={isPriceDetailsOpen}
                            setOpen={setIsPriceDetailsOpen}
                            offer={offer}
                            textualData={textualData}
                            flowContext={flowContext}
                            isSME={isSME}
                            energyType={energyType}
                          />
                        ))
                      ) : (
                        <Grid gridTemplateColumns="1fr 1fr 1fr" gap="6">
                          {availableOffers.map(offer => (
                            <OfferCard
                              key={offer.type}
                              isOpen={isPriceDetailsOpen}
                              setOpen={setIsPriceDetailsOpen}
                              offer={offer}
                              textualData={textualData}
                              flowContext={flowContext}
                              isSME={isSME}
                              energyType={energyType}
                            />
                          ))}
                        </Grid>
                      )}
                    </RadioGroup>
                  )}
                />
                <NextButton isLoading={isSubmitSuccessful} alignX={{ initial: 'justify', md: 'center' }}>
                  {textualData?.content?.nextStepText?.value}
                </NextButton>
              </>
            ) : null}
          </>
        )}

        {isFailed && (
          <Error
            type={isNotAuthenticated ? 'AUTHENTIFICATION' : 'GENERIC'}
            alignY="center"
            textualData={{
              notification: isNotAuthenticated
                ? textualData?.genericError?.errorNotAuthenticatedNotification
                : isNotAvailable
                  ? textualData?.genericError?.errorNotAvailableNotification
                  : isNotFound
                    ? textualData?.genericError?.errorNotFoundNotification
                    : textualData?.genericError?.errorNotification,
              buttonLabel: isNotAuthenticated
                ? textualData?.genericError?.loginButtonText
                : !isNotAvailable && !isNotFound
                  ? textualData?.genericError?.tryAgainButtonText
                  : undefined,
            }}
          />
        )}
      </Layout.Content>

      <Layout.Footer isLoading={isLoading || isFailed}>
        <OverviewConsumption
          textualData={{
            description: format(textualData?.footer?.footerDescription?.value ?? '', {
              address: address.medium({ street, houseNumber, houseNumberSuffix, city }),
            }),
            redeliveryLabel: textualData?.footer?.redeliveryLabel?.value,
            electricityLabel: textualData?.footer?.electricityLabel?.value,
            electricityHighLabel: textualData?.footer?.electricityHighLabel?.value,
            electricityLowLabel: textualData?.footer?.electricityLowLabel?.value,
            gasLabel: textualData?.footer?.gasLabel?.value,
            warmthLabel: textualData?.footer?.warmthLabel?.value,
            waterLabel: textualData?.footer?.waterLabel?.value,
          }}
          data={{
            hasDoubleMeter,
            hasSolarPanels,
            usageElectricityHigh,
            usageElectricityLow,
            usageGas,
            usageWarmth,
            usageWater,
            solarPanelsOutput,
          }}
          editButton={
            isUsageDetailsEditProhibited
              ? undefined
              : {
                  label: textualData?.footer?.modifyTriggerText?.value ?? '',
                  onClick: () => sendToFlowMachine({ type: 'GOTO', stepName: 'STEP_USAGE' }),
                }
          }
        />
      </Layout.Footer>
    </>
  );
};
