import React, { FC, useEffect, useMemo, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useMachine } from '@xstate/react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import useDC from '@dc/useDC';
import { Header, Layout, NextButton } from '@eneco/flows2';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { useFormatter } from '@i18n';
import { usePlaceholderContent } from '@sitecore/common';
import { OfferStepRendering } from '@sitecore/types/OfferStep';
import { Box, Grid, NotificationBox, RadioGroup, RadioTile, Text, TextLink, VisuallyHidden } from '@sparky';

import { OverviewConsumption, Error, ProductCardWrapper } from '../../components';
import { FlowConsumerContextEnergy } from '../../context';
import {
  getOffers,
  getOffer,
  getUpsellProductAvailability,
  checkAvailabilityOfferType,
  selectDefaultDiscountCode,
  checkOffersIncludesDynamicPricingProduct,
  checkOfferIncludesDynamicPricingProduct,
  getCrossSellRentalDeviceProductAvailability,
  checkOfferIncludesNonDynamicPricingProduct,
} from '../../helpers';
import { useFlowTracking } from '../../hooks';
import { useFlowNavigation } from '../../hooks/updateContextAndGoToNextStep';
import { offerMachine } from '../../machines';
import { Offer } from '../../types';

interface FormFields {
  discountCode?: string;
}

export const checkOffersIncludesNonDynamicPricingProduct = (offers: Offer[]): boolean => {
  return !!offers.find(offer => checkOfferIncludesNonDynamicPricingProduct(offer));
};

export const OfferStep_B: FC = () => {
  const { useFlowSelector, useFlowActorRef } = useFlowHooks<FlowConsumerContextEnergy>();
  const flowState = useFlowSelector(state => state);
  const sendToFlowMachine = useFlowActorRef().send;
  const { updateContextAndGoToNextStep } = useFlowNavigation();
  const [productFilter, setProductFilter] = useState('');
  const {
    context: flowContext,
    context: {
      discountCode,
      houseNumber,
      houseNumberSuffix,
      street,
      city,
      hasDoubleMeter,
      hasSolarPanels,
      hasUpsellGas,
      hasCrossSellRentalDevice,
      usageElectricityHigh,
      usageElectricityLow,
      usageGas,
      usageWarmth,
      usageWater,
      solarPanelsOutput,
      isUsageDetailsEditProhibited,
    },
  } = flowState;

  const [offerState, sendToOfferMachine] = useMachine(offerMachine, {
    state: offerMachine.initialState,
  });

  const { context: { offer } = {} } = offerState;

  const { OfferStep: { fields: textualData } = {} } = usePlaceholderContent<{ OfferStep: OfferStepRendering }>();

  const offers = getOffers({ ...flowContext, hasCrossSellRentalDevice: false }, offer);

  const showProductToggle =
    checkOffersIncludesDynamicPricingProduct(offers) && checkOffersIncludesNonDynamicPricingProduct(offers);
  const availableOffers = useMemo(() => {
    if (!showProductToggle) {
      return offers;
    }

    if (!productFilter) {
      return [];
    }

    return offers.filter(offer =>
      productFilter === 'dynamic'
        ? checkOfferIncludesDynamicPricingProduct(offer)
        : !checkOfferIncludesDynamicPricingProduct(offer),
    );
  }, [offers, productFilter, showProductToggle]);

  const formSchema = yup.object({
    discountCode: yup.string().required(),
  });

  const {
    control,
    handleSubmit,
    getValues,
    setValue,
    watch,
    formState: { isSubmitSuccessful },
  } = useForm<FormFields>({
    mode: 'onSubmit',
    resolver: yupResolver(formSchema),
    defaultValues: {
      // For this step we won't be using the "defaultValue" straight away since the earlier
      // selected "discountCode" might no longer be available due to flow context changes
    },
  });

  const { businessUnit, label } = useDC();
  const { trackViewItem, trackSelectItem, trackAddToCart } = useFlowTracking();

  const { format, address } = useFormatter();

  const [isLoading, setIsLoading] = useState(true);
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [isFailed, setIsFailed] = useState(false);

  const [isNotFound, setIsNotFound] = useState(false);
  const [isNotAvailable, setIsNotAvailable] = useState(false);
  const [isNotAuthenticated, setIsNotAuthenticated] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState<Offer | undefined>(undefined);

  const [isInitialOfferSelection, setInitialOfferSelection] = useState(!discountCode);
  const [shouldShowMoreInfoDialog, setShouldShowMoreInfoDialog] = useState(false);

  const selectedDiscountCode = watch('discountCode');

  useEffect(() => {
    if (!availableOffers.length || availableOffers?.some(offer => offer.type === selectedDiscountCode)) return;
    setValue('discountCode', selectDefaultDiscountCode(availableOffers));
  }, [availableOffers, selectedDiscountCode, setValue]);

  useEffect(() => {
    sendToOfferMachine({
      type: 'GET_OFFER',
      config: {
        businessUnit,
        label,
      },
      values: { ...flowContext },
    });
  }, []);

  useEffect(() => {
    if (selectedDiscountCode) {
      if (isInitialOfferSelection) setInitialOfferSelection(false);

      setSelectedOffer(getOffer({ ...flowContext, ...{ discountCode: selectedDiscountCode } }, offer));
      if (selectedOffer) trackSelectItem(selectedOffer, offer, isInitialOfferSelection);
    }
  }, [selectedDiscountCode]);

  useEffect(() => {
    if (isSubmitSuccessful) {
      const isRentalDeviceCrossSellProductAvailable = getCrossSellRentalDeviceProductAvailability(offer);

      const isGasUpsellProductAvailable = getUpsellProductAvailability(
        'gas',
        { ...flowContext, ...{ discountCode: selectedDiscountCode } },
        offer,
      );

      const selectedOffer = getOffer({ ...flowContext, ...{ discountCode: selectedDiscountCode } }, offer);

      if (selectedOffer) trackAddToCart(selectedOffer, offer);

      updateContextAndGoToNextStep({
        ...getValues(),
        isRentalDeviceCrossSellProductAvailable,
        isGasUpsellProductAvailable,
        hasCrossSellRentalDevice: hasCrossSellRentalDevice && isRentalDeviceCrossSellProductAvailable,
        hasUpsellGas: hasUpsellGas && isGasUpsellProductAvailable,
      });
    }
  }, [isSubmitSuccessful]);

  useEffect(() => {
    if (!offerState.matches('IDLE')) {
      setIsLoading(offerState.matches('FETCHING'));
      setIsFailed(!offerState.matches('FETCHING') && !offerState.matches('SUCCESS'));
      setIsSuccessful(offerState.matches('SUCCESS'));

      setIsNotFound(offerState.matches('ERROR_NOT_FOUND'));
      setIsNotAvailable(offerState.matches('ERROR_NOT_AVAILABLE'));
      setIsNotAuthenticated(offerState.matches('ERROR_NOT_AUTHENTICATED'));
    }

    if (offerState.matches('SUCCESS')) {
      const selectedOfferType = checkAvailabilityOfferType(offers, discountCode);

      if (selectedOfferType) {
        const isDynamicPricingProduct = checkOfferIncludesDynamicPricingProduct(
          offers.find(offer => offer.type === selectedOfferType),
        );
        setValue('discountCode', selectedOfferType);
        setProductFilter(isDynamicPricingProduct ? 'dynamic' : 'fixed');
      }

      setShouldShowMoreInfoDialog(
        !!(
          checkOffersIncludesDynamicPricingProduct(offers) && textualData?.content?.moreInfoDialog?.value?.triggerText
        ),
      );

      offers.forEach(availableOffer => {
        trackViewItem(availableOffer, offer);
      });
    }
  }, [offerState]);

  const contractOptions = textualData?.productData?.contractTypeRadio?.value?.options ?? [];
  const hasSubtitleLayout = contractOptions[0]?.description;

  const gridLayout = hasSubtitleLayout ? '1fr' : { initial: '1fr', md: '1fr 1fr' };
  const buttonLayout = hasSubtitleLayout
    ? { md: `repeat(${contractOptions.length}, 1fr)` }
    : contractOptions.length > 2
      ? '1fr'
      : '1fr 1fr';

  return (
    <>
      <Layout.Content variant="C" isLoading={isLoading} handleSubmit={handleSubmit}>
        {isSuccessful && (
          <>
            <Header>
              <Header.Title>{textualData?.content?.title?.value}</Header.Title>
              <Header.Text>{textualData?.content?.text?.value}</Header.Text>
            </Header>

            {textualData?.content?.notification?.value?.content && (
              <NotificationBox
                isAlert={false}
                variant={textualData?.content?.notification?.value?.variant}
                title={textualData?.content?.notification?.value?.title}
                text={<RichText html={textualData?.content?.notification?.value?.content} />}
              />
            )}

            {shouldShowMoreInfoDialog && (
              <TrackedDialog
                title={textualData?.content?.moreInfoDialog?.value?.title}
                trigger={
                  <TextLink emphasis="high">{textualData?.content?.moreInfoDialog?.value?.triggerText}</TextLink>
                }>
                <RichText html={textualData?.content?.moreInfoDialog?.value?.content} />
              </TrackedDialog>
            )}

            {showProductToggle && (
              <Box paddingBottom="5">
                {textualData?.content?.contractTypeTitle?.value && (
                  <Box paddingY="3">
                    <Text weight="bold">{textualData?.content.contractTypeTitle.value}</Text>
                  </Box>
                )}
                <Grid gridTemplateColumns={gridLayout} gap="6">
                  <VisuallyHidden id="productFilter">
                    {textualData?.productData?.contractTypeRadio?.value?.label}
                  </VisuallyHidden>
                  <RadioGroup
                    aria-labelledby="productFilter"
                    direction={hasSubtitleLayout ? 'column' : contractOptions.length > 2 ? 'column' : 'row'}
                    name="Productfilter"
                    value={productFilter}
                    onValueChange={e => setProductFilter(e)}
                    wrap={!hasSubtitleLayout}>
                    <Grid gridTemplateColumns={buttonLayout} gap="3">
                      {contractOptions?.map(option => (
                        <RadioTile key={option.name} value={option.name} subtitle={option?.description}>
                          {option.label}
                        </RadioTile>
                      ))}
                    </Grid>
                  </RadioGroup>
                </Grid>
              </Box>
            )}

            {availableOffers.length > 0 ? (
              <>
                {textualData && availableOffers && (
                  <ProductCardWrapper
                    availableOffers={availableOffers}
                    control={control}
                    selectedOffer={selectedOffer}
                    textualData={textualData}
                  />
                )}

                <NextButton isLoading={isSubmitSuccessful} alignX={{ initial: 'justify', md: 'center' }}>
                  {textualData?.content?.nextStepText?.value}
                </NextButton>
              </>
            ) : null}
          </>
        )}

        {isFailed && (
          <Error
            type={isNotAuthenticated ? 'AUTHENTIFICATION' : 'GENERIC'}
            alignY="center"
            textualData={{
              notification: isNotAuthenticated
                ? textualData?.genericError?.errorNotAuthenticatedNotification
                : isNotAvailable
                  ? textualData?.genericError?.errorNotAvailableNotification
                  : isNotFound
                    ? textualData?.genericError?.errorNotFoundNotification
                    : textualData?.genericError?.errorNotification,
              buttonLabel: isNotAuthenticated
                ? textualData?.genericError?.loginButtonText
                : !isNotAvailable && !isNotFound
                  ? textualData?.genericError?.tryAgainButtonText
                  : undefined,
            }}
          />
        )}
      </Layout.Content>

      <Layout.Footer isLoading={isLoading || isFailed}>
        <OverviewConsumption
          textualData={{
            description: format(textualData?.footer?.footerDescription?.value ?? '', {
              address: address.medium({ street, houseNumber, houseNumberSuffix, city }),
            }),
            redeliveryLabel: textualData?.footer?.redeliveryLabel?.value,
            electricityLabel: textualData?.footer?.electricityLabel?.value,
            electricityHighLabel: textualData?.footer?.electricityHighLabel?.value,
            electricityLowLabel: textualData?.footer?.electricityLowLabel?.value,
            gasLabel: textualData?.footer?.gasLabel?.value,
            warmthLabel: textualData?.footer?.warmthLabel?.value,
            waterLabel: textualData?.footer?.waterLabel?.value,
          }}
          data={{
            hasDoubleMeter,
            hasSolarPanels,
            usageElectricityHigh,
            usageElectricityLow,
            usageGas,
            usageWarmth,
            usageWater,
            solarPanelsOutput,
          }}
          editButton={
            isUsageDetailsEditProhibited
              ? undefined
              : {
                  label: textualData?.footer?.modifyTriggerText?.value ?? '',
                  onClick: () => sendToFlowMachine({ type: 'GOTO', stepName: 'STEP_USAGE' }),
                }
          }
        />
      </Layout.Footer>
    </>
  );
};
