import React, { FC, PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import useDC from '@dc/useDC';
import { Header, Layout, NextButton } from '@eneco/flows2';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { useFormatter } from '@i18n';
import { usePlaceholderContent } from '@sitecore/common';
import { OxxioOfferStepRendering, Fields } from '@sitecore/types/OxxioOfferStep';
import {
  Bleed,
  Carousel,
  NotificationBox,
  RadioGroup,
  Stack,
  Divider,
  Box,
  Grid,
  RadioTile,
  VisuallyHidden,
  TextLink,
} from '@sparky';
import { useMediaQuery } from '@sparky/hooks';

import {
  PriceDetailsOverviewOxxio as PriceDetailsOverview,
  OverviewConsumption,
  Ribbon,
  Error,
} from '../../components';
import { ProductCard } from '../../components/ProductCard/ProductCardOxxio';
import { FlowConsumerContextEnergy } from '../../context';
import {
  getOffers,
  getOffer,
  constructTextualDataKey,
  getOfferSubTitle,
  getCashbackTexts,
  getUpsellProductAvailability,
  checkAvailabilityOfferType,
  selectDefaultDiscountCode,
  checkOffersIncludesDynamicPricingProduct,
} from '../../helpers';
import {
  checkOfferIncludesDynamicOrVariablePricingProduct,
  filterOffersByType,
  getAvailableOfferTypes,
} from '../../helpers/misc';
import { checkOnDiscountType, getOfferTitle } from '../../helpers/sitecore';
import { useFlowTracking } from '../../hooks';
import { useProductItemContent } from '../../hooks/sitecore/useProductItemContent';
import { useFlowNavigation } from '../../hooks/updateContextAndGoToNextStep';
import { useOfferMachine } from '../../hooks/useOfferMachine';
import { Offer, EnergyType } from '../../types';
import { OfferType } from '../../types/enums';

interface FormFields {
  discountCode?: string;
}

interface ContractOption {
  name: string;
  label: string;
  description?: string;
}

const OfferCard: FC<
  PropsWithChildren<{
    offer: Offer;
    isSME?: boolean;
    energyType?: EnergyType;
    textualData?: Fields;
    isOpen: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    isCashbackAvailable: boolean;
    isTagAvailable: boolean;
    productFilter?: string;
  }>
> = ({
  offer,
  isOpen,
  setOpen,
  isSME,
  isCashbackAvailable,
  isTagAvailable,
  energyType = EnergyType.Electricity,
  textualData,
  productFilter,
  children,
}) => {
  const { currency, format } = useFormatter();

  const textualDataKey = checkOnDiscountType(
    constructTextualDataKey(offer.contractSpan, energyType),
    offer?.discountType,
  );

  const { getProductDetails } = useProductItemContent(textualData?.productData?.productDataList);

  const productDetails = getProductDetails(textualDataKey);

  // Ensures that any product item is not rendered unless it is mapped on Sitecore, even though that's exposed by the api
  if (!productDetails) {
    return null;
  }

  const { promotionText, usps } = productDetails;

  const costsType = isSME ? 'vatExcluded' : 'vatIncluded';

  const cashbackTexts = getCashbackTexts(offer.costDiscounts?.cashback?.type, textualData?.promotionTexts);

  return (
    <ProductCard
      isOpen={isOpen}
      setOpen={setOpen}
      id={offer.type ?? ''}
      title={getOfferTitle(offer, textualData?.productData)}
      subtitle={getOfferSubTitle(
        energyType,
        textualData?.productTypeLabels,
        offer?.discountType,
        textualData?.productData?.dynamicProductNameText?.value.toLowerCase(),
        textualData?.productData?.fixedPeriodLabel?.value.toLowerCase(),
      )}
      tag={offer.mostPopularChoice ? promotionText : undefined}
      isTagAvailable={isTagAvailable}
      usps={usps}
      priceMonthly={{
        value: offer?.costsMonthly?.[costsType] ?? 0,
        period:
          productFilter !== OfferType.Dynamic
            ? textualData?.productData?.perMonthLabel?.value
            : textualData?.productData?.perMonthEstimatedLabel?.value,
      }}
      priceYearly={{
        value: Number(offer?.costsYearly?.[costsType] ?? 0) - Number(offer?.costDiscounts?.cashback?.value ?? 0),
        previousValue:
          offer.costsYearlyPromotion || offer?.costDiscounts?.cashback?.value
            ? currency.euro(offer?.costsYearly?.[costsType] ?? 0)
            : undefined,
        period: textualData?.productData?.perYearLabel?.value,
      }}
      priceDetailsTriggerLabel={textualData?.productData?.tariffsTriggerText?.value}
      loyaltyBonusLabel={textualData?.productData?.loyaltyBonusLabel?.value}
      isCashbackAvailable={isCashbackAvailable}
      ribbon={
        cashbackTexts && (
          <Ribbon
            theme="oxxio"
            size="small"
            emphasis="medium"
            title={format(cashbackTexts.title, {
              [`${offer?.costDiscounts?.cashback?.type}`]: currency.euroNoFractionDigits(
                offer?.costDiscounts?.cashback?.value ?? 0,
              ),
            })}
            text={cashbackTexts.text}
            trigger={cashbackTexts?.trigger}
          />
        )
      }>
      {children}
    </ProductCard>
  );
};

export const OfferStep: FC = () => {
  const { businessUnit, label } = useDC();
  const { useFlowSelector, useFlowActorRef } = useFlowHooks<FlowConsumerContextEnergy>();
  const flowState = useFlowSelector(state => state);
  const sendToFlowMachine = useFlowActorRef().send;
  const { updateContextAndGoToNextStep } = useFlowNavigation();
  const [productFilter, setProductFilter] = useState('');
  const [showProductToggle, setShowProductToggle] = useState(true);
  const [showCard, setShowCard] = useState(false);
  const cardRef = useRef<HTMLDivElement | null>(null);

  const {
    context: flowContext,
    context: {
      discountCode,
      houseNumber,
      houseNumberSuffix,
      street,
      city,
      hasDoubleMeter,
      hasSolarPanels,
      hasUpsellElectricity,
      hasUpsellGas,
      usageElectricityHigh,
      usageElectricityLow,
      usageGas,
      usageWarmth,
      usageWater,
      solarPanelsOutput,
      energyType,
      isSME,
      isUsageDetailsEditProhibited,
    },
  } = flowState;

  const { offerState, sendToOfferMachine } = useOfferMachine(businessUnit, label);

  const { context: { offer } = {} } = offerState;
  const availableOffers = getOffers(flowContext, offer);

  const offers = useMemo(() => {
    if (!showProductToggle) {
      return availableOffers;
    }

    if (!productFilter) return [];

    if (productFilter) {
      return filterOffersByType(availableOffers, productFilter);
    }
    return availableOffers.filter(offer => offer.discountType?.includes(productFilter));
  }, [availableOffers, productFilter]);

  const toggleCard = () => {
    setShowCard(true);
  };

  useEffect(() => {
    if (showCard && cardRef.current) {
      const card = cardRef.current;
      card.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
      setShowCard(false);
    }
  }, [showCard]);

  const { OxxioOfferStep: { fields: textualData } = {} } = usePlaceholderContent<{
    OxxioOfferStep: OxxioOfferStepRendering;
  }>();

  const formSchema = yup.object({
    discountCode: yup.string().required(textualData?.content?.noOfferSelectedNotification?.value.content ?? ''),
  });

  const {
    handleSubmit,
    control,
    getValues,
    setValue,
    watch,
    formState: { errors, isSubmitSuccessful },
  } = useForm<FormFields>({
    mode: 'onSubmit',
    resolver: yupResolver(formSchema),
    defaultValues: {
      // For this step we won't be using the "defaultValue" straight away since the earlier
      // selected "discountCode" might no longer be available due to flow context changes
    },
  });

  const { trackViewItem, trackSelectItem, trackAddToCart } = useFlowTracking();

  const { format, address } = useFormatter();

  const [isLoading, setIsLoading] = useState(true);
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [isFailed, setIsFailed] = useState(false);

  const [isNotFound, setIsNotFound] = useState(false);
  const [isNotAuthenticated, setIsNotAuthenticated] = useState(false);
  const [isNotAvailable, setIsNotAvailable] = useState(false);

  const [isInitialOfferSelection, setInitialOfferSelection] = useState(!discountCode);

  const [isPriceDetailsOpen, setIsPriceDetailsOpen] = useState(false);
  const [isPriceDetailsExplanationOpen, setIsPriceDetailsExplanationOpen] = useState(false);
  const [shouldShowMoreInfoDialog, setShouldShowMoreInfoDialog] = useState(false);

  const isSmallBreakpoint = useMediaQuery('md');
  const isLargeBreakpoint = useMediaQuery('lg');
  const isCashbackAvailable = !!availableOffers?.find(offer => offer.costDiscounts?.cashback);
  const isTagAvailable = !!availableOffers?.find(offer => offer.mostPopularChoice);

  const selectedDiscountCode = watch('discountCode');
  const selectedDiscountCodeIndex = Math.max(
    availableOffers?.findIndex(offer => offer.type === selectedDiscountCode) ?? 0,
    0,
  );

  useEffect(() => {
    if (!offers.length || offers?.some(offer => offer.type === selectedDiscountCode)) return;
    setValue('discountCode', selectDefaultDiscountCode(offers));
  }, [offers, selectedDiscountCode, setValue]);

  useEffect(() => {
    sendToOfferMachine({
      type: flowContext?.isContractExtension ? 'GET_EXTENSION_OFFER' : 'GET_OFFER',
      flowContext: { ...flowContext },
    });
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    if (selectedDiscountCode) {
      if (isInitialOfferSelection) setInitialOfferSelection(false);

      const selectedOffer = getOffer({ ...flowContext, ...{ discountCode: selectedDiscountCode } }, offer);

      if (selectedOffer) trackSelectItem(selectedOffer, offer, isInitialOfferSelection);
    }
    // eslint-disable-next-line
  }, [selectedDiscountCode]);

  useEffect(() => {
    if (isSubmitSuccessful) {
      const isGasUpsellProductAvailable = getUpsellProductAvailability(
        'gas',
        { ...flowContext, ...{ discountCode: selectedDiscountCode } },
        offer,
      );
      const isElectricityUpsellProductAvailable = getUpsellProductAvailability(
        'electricity',
        { ...flowContext, ...{ discountCode: selectedDiscountCode } },
        offer,
      );

      const selectedOffer = getOffer({ ...flowContext, ...{ discountCode: selectedDiscountCode } }, offer);

      if (selectedOffer) trackAddToCart(selectedOffer, offer);

      updateContextAndGoToNextStep({
        ...getValues(),
        isGasUpsellProductAvailable,
        isElectricityUpsellProductAvailable,
        hasUpsellGas: hasUpsellGas && isGasUpsellProductAvailable,
        hasUpsellElectricity: hasUpsellElectricity && isElectricityUpsellProductAvailable,
      });
    }
    // eslint-disable-next-line
  }, [isSubmitSuccessful]);

  useEffect(() => {
    if (!offerState.matches('IDLE')) {
      setIsLoading(offerState.matches('FETCHING') || offerState.matches('GET_EXTENSION'));
      setIsFailed(!offerState.matches('FETCHING') && !offerState.matches('SUCCESS'));
      setIsSuccessful(offerState.matches('SUCCESS'));

      setIsNotFound(offerState.matches('ERROR_NOT_FOUND'));
      setIsNotAvailable(offerState.matches('ERROR_NOT_AVAILABLE'));
      setIsNotAuthenticated(offerState.matches('ERROR_NOT_AUTHENTICATED'));
    }

    if (offerState.matches('SUCCESS')) {
      const selectedOfferType = checkAvailabilityOfferType(availableOffers, discountCode);
      const availableOfferTypes = getAvailableOfferTypes(availableOffers);
      setShowProductToggle(availableOfferTypes.length !== 1);

      if (selectedOfferType) {
        const isDynamicPricingProduct = checkOfferIncludesDynamicOrVariablePricingProduct(
          availableOffers.find(offer => offer.type === selectedOfferType),
        );
        setValue('discountCode', selectedOfferType);
        if (isDynamicPricingProduct) setProductFilter(isDynamicPricingProduct);
      }

      setShouldShowMoreInfoDialog(
        !!(
          checkOffersIncludesDynamicPricingProduct(availableOffers) &&
          textualData?.content?.moreInfoDialog?.value?.triggerText
        ),
      );

      availableOffers.forEach(availableOffer => {
        trackViewItem(availableOffer, offer);
      });
    }
    // eslint-disable-next-line
  }, [offerState]);

  const contractOptions: ContractOption[] = textualData?.productData?.contractTypeRadio?.value?.options ?? [];

  const checkOfferTypes = (contractOptions: ContractOption[]): ContractOption[] => {
    const filteredOptions = contractOptions.filter(option =>
      getAvailableOfferTypes(availableOffers).some(offer => offer?.includes(option.name)),
    );
    return filteredOptions;
  };

  const handleValueChange = (value: string) => {
    setProductFilter(value);
    toggleCard();
  };

  const hasSubtitleLayout = contractOptions[0]?.description;

  const gridLayout = hasSubtitleLayout ? '1fr' : { initial: '1fr', md: '1fr 1fr' };
  const buttonLayout = hasSubtitleLayout
    ? { md: `repeat(${contractOptions.length}, 1fr)` }
    : contractOptions.length > 2
      ? '1fr'
      : '1fr 1fr';

  return (
    <Layout.Content
      variant={availableOffers.length !== 1 ? 'C' : 'A'}
      isLoading={isLoading}
      handleSubmit={handleSubmit}>
      {isSuccessful && (
        <>
          <Header align="center">
            <Header.Title>{textualData?.content?.title?.value}</Header.Title>
            {textualData?.content?.text?.value && <Header.Text>{textualData.content.text.value}</Header.Text>}
          </Header>

          {textualData?.content?.notification?.value?.content && (
            <NotificationBox
              isAlert={false}
              variant={textualData?.content?.notification?.value?.variant}
              title={textualData?.content?.notification?.value?.title}
              text={<RichText html={textualData?.content?.notification?.value?.content} />}
            />
          )}

          <Stack.Item>
            <Bleed horizontal="gridGutter">
              <Divider />
            </Bleed>

            <OverviewConsumption
              theme="oxxio"
              textualData={{
                description: format(textualData?.footer?.footerDescription?.value ?? '', {
                  address: address.medium({ street, houseNumber, houseNumberSuffix, city }),
                }),
                redeliveryLabel: textualData?.footer?.redeliveryLabel?.value,
                electricityLabel: textualData?.footer?.electricityLabel?.value,
                electricityHighLabel: textualData?.footer?.electricityHighLabel?.value,
                electricityLowLabel: textualData?.footer?.electricityLowLabel?.value,
                gasLabel: textualData?.footer?.gasLabel?.value,
                warmthLabel: textualData?.footer?.warmthLabel?.value,
                waterLabel: textualData?.footer?.waterLabel?.value,
              }}
              data={{
                hasDoubleMeter,
                hasSolarPanels,
                usageElectricityHigh,
                usageElectricityLow,
                usageGas,
                usageWarmth,
                usageWater,
                solarPanelsOutput,
              }}
              editButton={
                isUsageDetailsEditProhibited
                  ? undefined
                  : {
                      label: textualData?.footer?.modifyTriggerText?.value ?? '',
                      onClick: () => sendToFlowMachine({ type: 'GOTO', stepName: 'STEP_USAGE' }),
                    }
              }
            />

            {shouldShowMoreInfoDialog && (
              <TrackedDialog
                title={textualData?.content?.moreInfoDialog?.value?.title}
                trigger={
                  <TextLink emphasis="high">{textualData?.content?.moreInfoDialog?.value?.triggerText}</TextLink>
                }>
                <RichText html={textualData?.content?.moreInfoDialog?.value?.content} />
              </TrackedDialog>
            )}
            {showProductToggle && (
              <Box paddingTop="10" paddingBottom="5">
                <Grid gridTemplateColumns={gridLayout} gap="6">
                  <VisuallyHidden id="productFilter">
                    {textualData?.productData?.contractTypeRadio?.value?.label}
                  </VisuallyHidden>
                  <RadioGroup
                    aria-labelledby="productFilter"
                    direction={hasSubtitleLayout ? 'column' : 'row'}
                    name="Productfilter"
                    value={productFilter}
                    onValueChange={e => handleValueChange(e)}
                    wrap={!hasSubtitleLayout}>
                    <Grid gridTemplateColumns={buttonLayout} gap="3">
                      {checkOfferTypes(contractOptions)?.map(option => (
                        <RadioTile key={option.name} value={option.name} subtitle={option?.description}>
                          {option.label}
                        </RadioTile>
                      ))}
                    </Grid>
                  </RadioGroup>
                </Grid>
              </Box>
            )}

            <Controller
              control={control}
              name="discountCode"
              render={({ field: { onChange, value, name } }) => (
                <RadioGroup
                  aria-labelledby="discountCode"
                  name={name}
                  direction={{ initial: 'column', lg: undefined }}
                  alignY="justify"
                  wrap={false}
                  value={value}
                  onValueChange={onChange}
                  error={errors['discountCode']?.message}>
                  {!isSmallBreakpoint && offers.length === 1 ? (
                    <Grid gridTemplateColumns="1fr">
                      <Box ref={cardRef}>
                        {offers.map(offer => (
                          <OfferCard
                            key={offer.type}
                            isOpen={isPriceDetailsOpen}
                            setOpen={setIsPriceDetailsOpen}
                            offer={offer}
                            textualData={textualData}
                            isSME={isSME}
                            energyType={energyType}
                            isCashbackAvailable={isCashbackAvailable}
                            productFilter={productFilter}
                            isTagAvailable={isTagAvailable}>
                            <PriceDetailsOverview
                              offer={offer}
                              flowContext={flowContext}
                              textualData={textualData}
                              isOpen={isPriceDetailsExplanationOpen}
                              setOpen={setIsPriceDetailsExplanationOpen}
                            />
                          </OfferCard>
                        ))}
                      </Box>
                    </Grid>
                  ) : isLargeBreakpoint || offers.length === 1 ? (
                    <Grid gridTemplateColumns="1fr 1fr 1fr" gap="3">
                      {offers.map(offer => (
                        <OfferCard
                          key={offer.type}
                          isOpen={isPriceDetailsOpen}
                          setOpen={setIsPriceDetailsOpen}
                          offer={offer}
                          textualData={textualData}
                          isSME={isSME}
                          energyType={energyType}
                          isCashbackAvailable={isCashbackAvailable}
                          productFilter={productFilter}
                          isTagAvailable={isTagAvailable}>
                          <PriceDetailsOverview
                            offer={offer}
                            flowContext={flowContext}
                            textualData={textualData}
                            isOpen={isPriceDetailsExplanationOpen}
                            setOpen={setIsPriceDetailsExplanationOpen}
                          />
                        </OfferCard>
                      ))}
                    </Grid>
                  ) : (
                    <Bleed horizontal={{ initial: 6, md: 10, lg: 0 }}>
                      <Box ref={cardRef}>
                        <Carousel
                          showIndicator
                          scrollBehavior="snap"
                          scrollMarginX="gridGutter"
                          hasCardPadding
                          startAt={selectedDiscountCodeIndex}>
                          {offers.map(offer => (
                            <Carousel.Item key={offer.type}>
                              <OfferCard
                                isOpen={isPriceDetailsOpen}
                                setOpen={setIsPriceDetailsOpen}
                                offer={offer}
                                textualData={textualData}
                                isSME={isSME}
                                energyType={energyType}
                                isCashbackAvailable={isCashbackAvailable}
                                productFilter={productFilter}
                                isTagAvailable={isTagAvailable}>
                                <PriceDetailsOverview
                                  offer={offer}
                                  flowContext={flowContext}
                                  textualData={textualData}
                                  isOpen={isPriceDetailsExplanationOpen}
                                  setOpen={setIsPriceDetailsExplanationOpen}
                                />
                              </OfferCard>
                            </Carousel.Item>
                          ))}
                        </Carousel>
                      </Box>
                    </Bleed>
                  )}
                </RadioGroup>
              )}
            />
          </Stack.Item>

          <NextButton isLoading={isSubmitSuccessful} alignX={{ initial: 'justify', md: 'center' }}>
            {textualData?.content?.nextStepText?.value}
          </NextButton>
        </>
      )}

      {isFailed && (
        <Stack direction="column" gap="10">
          <Error
            alignY="center"
            type={isNotAuthenticated ? 'AUTHENTIFICATION' : 'GENERIC'}
            textualData={{
              notification: isNotAuthenticated
                ? textualData?.genericError?.errorNotAuthenticatedNotification
                : isNotAvailable
                  ? textualData?.genericError?.errorNotAvailableNotification
                  : isNotFound
                    ? textualData?.genericError?.errorNotFoundNotification
                    : textualData?.genericError?.errorNotification,
              buttonLabel: isNotAuthenticated
                ? textualData?.genericError?.loginButtonText
                : !isNotAvailable && !isNotFound
                  ? textualData?.genericError?.tryAgainButtonText
                  : undefined,
            }}
          />
        </Stack>
      )}
    </Layout.Content>
  );
};
