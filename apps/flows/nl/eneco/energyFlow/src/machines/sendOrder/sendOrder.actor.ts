import { putProductsOrderV2 } from '@dc/services';
import { putProductsOrderForCustomerV2 } from '@dc/services/ProductsService';
import { fromPromise } from '@eneco-packages/xstate-custom/xstate';
import {
  DC_Repositories_Base_Enumerations_BusinessUnit,
  DC_Repositories_Base_Enumerations_Label,
  Products_Offers_V3_OfferResponseModel,
  RequestModels_Products_Order_ProductOrder,
  RequestModels_Products_Order_ProductOrderPersonal,
} from '@monorepo-types/dc';

import { getDeliveryAddress, getProducts, getSendProductOrderRequestBody } from './sendOrder.helpers';
import { FlowConsumerContextEnergy as FlowContext } from '../../context';
import { selectProductCombinationByDiscountCode } from '../../helpers';

type SendProductsOrderContext = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  flowContext: FlowContext;
  offersData: Products_Offers_V3_OfferResponseModel;
};

const sendProductsOrder = async ({ businessUnit, label, flowContext, offersData }: SendProductsOrderContext) => {
  const requestBody: RequestModels_Products_Order_ProductOrder = getSendProductOrderRequestBody(
    flowContext,
    offersData,
  );

  try {
    const response = await putProductsOrderV2({
      businessUnit: businessUnit,
      label: label,
      requestBody,
    });

    return response;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(error?.message);
    }
    throw new Error('Failed to send order');
  }
};

const sendOrderExtensionService = async ({
  businessUnit,
  label,
  flowContext,
  offersData,
}: SendProductsOrderContext) => {
  const selectedProductCombination = selectProductCombinationByDiscountCode(
    offersData?.productCombinations ?? [],
    flowContext.discountCode,
  );

  const desiredAdvancePaymentAmount = selectedProductCombination?.costDetails?.find(
    costDetail => costDetail?.type === 'monthlyCosts',
  )?.costs?.vatIncluded;

  const requestBody: RequestModels_Products_Order_ProductOrderPersonal = {
    data: {
      productOffers: getProducts(flowContext, offersData),
      deliveryAddress: getDeliveryAddress(flowContext),
      configuration: {
        marketingCampaignCode: offersData?.configuration?.marketingCampaignCode,
        actionCode: offersData?.configuration?.actionCode,
        promotionCode: selectedProductCombination?.discountCode,
        discountType: selectedProductCombination?.discountType,
      },
      isResidence: flowContext.hasResidencePurpose,
      referenceId: flowContext.basketId,
      emailAddress: flowContext.emailAddress,
      phoneNumbers: [flowContext.phoneNumber?.replace(/[^\d]/g, '') ?? ''],
      desiredAdvancePaymentAmount,
    },
  };

  try {
    const response = await putProductsOrderForCustomerV2({
      businessUnit: businessUnit,
      label: label,
      accountId: Number(flowContext.accountId),
      customerId: Number(flowContext.customerId),
      requestBody,
    });
    return response;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(error?.message);
    }
    throw new Error('Failed to send order');
  }
};

export type sendProductsOrderOutput = Awaited<ReturnType<typeof sendProductsOrder>>;

export const actors = {
  sendProductOrder: fromPromise<sendProductsOrderOutput, SendProductsOrderContext>(
    async ({ input }) => await sendProductsOrder(input),
  ),
  sendProductExtensionOrder: fromPromise<sendProductsOrderOutput, SendProductsOrderContext>(
    async ({ input }) => await sendOrderExtensionService(input),
  ),
};
