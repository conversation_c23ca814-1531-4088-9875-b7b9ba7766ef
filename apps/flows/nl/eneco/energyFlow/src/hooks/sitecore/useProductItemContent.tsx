import { useCallback } from 'react';

import { TextField } from '@sitecore-jss/sitecore-jss-react';

interface ProductDetails {
  title?: string;
  promotionText?: string;
  tariffDescription?: string;
  tariffSplitExplanation?: string;
  usps: string[];
}

export interface Data {
  byPeriodLabel: TextField;
  id: TextField;
  usp2Text: TextField;
  usp1Text: TextField;
  usp3Text: TextField;
  usp4Text: TextField;
  name: TextField;
  promotionText: TextField;
  tariffDescriptionText?: TextField;
  tariffSplitExplanationText?: TextField;
}
interface OfferData {
  id: string;
  url: string;
  name: string;
  displayName: string;
  fields: {
    data?: Data;
  };
  [k: string]: unknown;
}

export const useProductItemContent = (textualData?: OfferData[]) => {
  const doesItemExist = useCallback(
    (key: string) => {
      if (!textualData) {
        return false;
      }
      return textualData?.some(i => i.fields?.data?.id?.value === key);
    },
    [textualData],
  );

  const getProductDetails: (key: string) => ProductDetails | null = useCallback(
    (key: string) => {
      // Check for existence for safety, in case consumer doesn't check using 'doesItemExist'
      if (!doesItemExist(key)) {
        return null;
      }

      const productItem = textualData!.find(i => i.fields?.data?.id?.value === key);

      // Unwrap values
      return {
        title: productItem?.fields?.data?.name?.value,
        promotionText: productItem?.fields?.data?.promotionText?.value,
        tariffDescription: productItem?.fields?.data?.tariffDescriptionText?.value,
        tariffSplitExplanation: productItem?.fields?.data?.tariffSplitExplanationText?.value,
        usps: !productItem?.fields?.data
          ? []
          : Object.entries(productItem.fields.data)
              .filter(([key, _]) => {
                return key.startsWith('usp');
              })
              .filter(([_, value]) => Boolean(value.value))
              .map(([_, value]) => value.value),
      };
    },
    [doesItemExist, textualData],
  ) as (key: string) => ProductDetails | null;

  return { doesItemExist, getProductDetails };
};
