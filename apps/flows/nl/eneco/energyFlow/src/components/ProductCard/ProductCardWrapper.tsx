import React, { FC, useState } from 'react';

import { useActor } from '@xstate/react';
import { Control, Controller, useForm } from 'react-hook-form';

import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { useFlowContext } from '@eneco/flows/src/hooks';
import { FlowContext } from '@eneco/flows/src/types';
import { Price } from '@eneco/flows2';
import { useFormatter } from '@i18n';
import { Fields } from '@sitecore/types/OfferStep';
import { Box, Stack, Text, Divider, RadioGroup, Stretch, Grid, TextLink, Expandable, Bleed } from '@sparky';
import { useMediaQuery } from '@sparky/hooks';

import {
  OverviewConsumption,
  PriceDetailsOverview,
  ProductCardRenewalFlow,
  Ribbon,
  TariffDetailsOverview,
} from '../../components';
import { checkOfferIncludesDynamicPricingProduct, constructTextualDataKey, getCashbackTexts } from '../../helpers';
import { useProductItemContent } from '../../hooks/sitecore/useProductItemContent';
import { EnergyType, Offer } from '../../types';

interface FormFields {
  discountCode?: string;
}

interface Props {
  control: Control<FormFields>;
  availableOffers: Offer[];
  textualData: Fields;
  selectedOffer: Offer | undefined;
}

//This component is created for an a/b test that will run in the future.
//This component has the newer design. it's like a box around the productCards
export const OfferCardRenewal: FC<{
  offer: Offer;
  isSME?: boolean;
  energyType?: EnergyType;
  textualData?: Fields;
  flowContext?: FlowContext;
  isOpen: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ offer, isSME, energyType = EnergyType.Electricity, textualData, flowContext, isOpen, setOpen }) => {
  const { format, currency } = useFormatter();
  const textualDataKey = constructTextualDataKey(offer.contractSpan, energyType);

  const { getProductDetails } = useProductItemContent(textualData?.productData?.productDataList);

  const productDetails = getProductDetails(textualDataKey);

  // Ensures that any product item is not rendered unless it is mapped on Sitecore, even though that's exposed by the api
  if (!productDetails) {
    return null;
  }

  const { title, promotionText, usps } = productDetails;

  const costsType = isSME ? 'vatExcluded' : 'vatIncluded';

  const cashbackTexts = getCashbackTexts(offer?.costDiscounts?.cashback?.type, textualData?.promotionTexts);

  return (
    <ProductCardRenewalFlow
      id={offer.type ?? ''}
      title={title}
      tag={offer.mostPopularChoice ? promotionText : undefined}
      usps={usps}
      price={{
        value: offer?.costsMonthly?.[costsType] ?? 0,
        period: checkOfferIncludesDynamicPricingProduct(offer)
          ? textualData?.productData?.perMonthEstimatedLabel?.value
          : textualData?.productData?.perMonthLabel?.value,
      }}
      ribbon={
        cashbackTexts && (
          <Ribbon
            size="small"
            emphasis="high"
            title={format(cashbackTexts.title, {
              [`${offer?.costDiscounts?.cashback?.type}`]: currency.euroNoFractionDigits(
                offer?.costDiscounts?.cashback?.value ?? 0,
              ),
            })}
            text={cashbackTexts.text}
            trigger={cashbackTexts?.trigger}
          />
        )
      }>
      <Expandable isOpen={isOpen} setOpen={setOpen}>
        <Stretch height>
          <Box paddingRight="6">
            <Stack alignY="justify" alignX="end">
              <Expandable.Trigger isTransparent aria-labelledby={`${offer.type}-tariff-details`}>
                <Box id={`${offer.type}-tariff-details`} paddingLeft="6" paddingY="3">
                  {textualData?.productData?.tariffsTriggerText?.value}
                </Box>
              </Expandable.Trigger>
            </Stack>
          </Box>
        </Stretch>

        <Expandable.Content>
          <Bleed horizontal="6">
            <Divider />
          </Bleed>
          <Box paddingX={6} paddingY={3}>
            <TariffDetailsOverview offer={offer} textualData={textualData} flowContext={flowContext} />
          </Box>
        </Expandable.Content>
      </Expandable>
    </ProductCardRenewalFlow>
  );
};

export const ProductCardWrapper: FC<React.PropsWithChildren<Props>> = ({
  availableOffers,
  control,
  selectedOffer,
  textualData,
}) => {
  const { flowService } = useFlowContext();
  const [flowState, sendToFlowMachine] = useActor(flowService);

  const [isPriceDetailsOpen, setIsPriceDetailsOpen] = useState(false);

  const {
    formState: { errors },
  } = useForm<FormFields>();
  const { format, address } = useFormatter();

  const {
    context: flowContext,
    context: {
      street,
      houseNumber,
      houseNumberSuffix,
      city,
      energyType,
      hasDoubleMeter,
      hasSolarPanels,
      usageElectricityHigh,
      usageElectricityLow,
      usageGas,
      usageWarmth,
      usageWater,
      solarPanelsOutput,
      isUsageDetailsEditProhibited,
    },
  } = flowState;

  const isSmallBreakpoint = !useMediaQuery('lg');

  return (
    <>
      <Box backgroundColor="backgroundPrimary" borderRadius="m" padding="6">
        <OverviewConsumption
          layout="B"
          textualData={{
            description: format(textualData?.footer?.footerDescription?.value ?? '', {
              address: address.medium({ street, houseNumber, houseNumberSuffix, city }),
            }),
            redeliveryLabel: textualData?.footer?.redeliveryLabel?.value,
            electricityLabel: textualData?.footer?.electricityLabel?.value,
            electricityHighLabel: textualData?.footer?.electricityHighLabel?.value,
            electricityLowLabel: textualData?.footer?.electricityLowLabel?.value,
            gasLabel: textualData?.footer?.gasLabel?.value,
            warmthLabel: textualData?.footer?.warmthLabel?.value,
            waterLabel: textualData?.footer?.waterLabel?.value,
          }}
          data={{
            hasDoubleMeter,
            hasSolarPanels,
            usageElectricityHigh,
            usageElectricityLow,
            usageGas,
            usageWarmth,
            usageWater,
            solarPanelsOutput,
          }}
          editButton={
            isUsageDetailsEditProhibited
              ? undefined
              : {
                  label: textualData?.footer?.modifyTriggerText?.value ?? '',
                  onClick: () => sendToFlowMachine('STEP_USAGE'),
                }
          }
        />
        <Controller
          control={control}
          name="discountCode"
          render={({ field: { onChange, value, name } }) => (
            <RadioGroup
              aria-labelledby="discountCode"
              name={name}
              value={value}
              direction={availableOffers.length === 2 ? { initial: 'column', lg: 'row' } : 'column'}
              alignY="start"
              wrap={true}
              onValueChange={onChange}
              error={errors['discountCode']?.message}>
              {!isSmallBreakpoint ? (
                <Grid gridTemplateColumns="1fr 1fr 1fr" gap="6">
                  {availableOffers.map(offer => (
                    <OfferCardRenewal
                      key={offer.type}
                      isOpen={isPriceDetailsOpen}
                      setOpen={setIsPriceDetailsOpen}
                      offer={offer}
                      textualData={textualData}
                      flowContext={flowContext}
                      energyType={energyType}
                    />
                  ))}
                </Grid>
              ) : (
                <>
                  {availableOffers.map(offer => (
                    <OfferCardRenewal
                      key={offer.type}
                      isOpen={isPriceDetailsOpen}
                      setOpen={setIsPriceDetailsOpen}
                      offer={offer}
                      textualData={textualData}
                      flowContext={flowContext}
                      energyType={energyType}
                    />
                  ))}
                </>
              )}
            </RadioGroup>
          )}
        />
        <Box paddingBottom="4" paddingTop="8">
          <Divider />
        </Box>
        <Box paddingBottom="10">
          <Stack>
            {selectedOffer?.products.map(product => (
              <Stack alignX="justify" direction="row" key={product.code}>
                <Text>{product.description}</Text>
                <Price>{Number(product.costsMonthly?.vatIncluded)}</Price>
              </Stack>
            ))}
          </Stack>
        </Box>
        <Box paddingBottom="4" paddingTop="6">
          <Divider emphasis="high" />
        </Box>
        <Box>
          <Stack>
            <Stack alignX="justify" direction="row">
              <Stack>
                <Text weight="bold">Totaal</Text>
                <TrackedDialog
                  title="Bekijk de prijsopbouw"
                  trigger={<TextLink emphasis="high">Bekijk de prijsopbouw</TextLink>}>
                  <PriceDetailsOverview offer={selectedOffer} flowContext={flowContext} textualData={textualData} />
                </TrackedDialog>
              </Stack>
              <Stack gap="6">
                <Price suffix="per maand">{Number(selectedOffer?.costsMonthly?.vatIncluded)}</Price>
                <Price emphasis="low" suffix="per jaar">
                  {Number(selectedOffer?.costsMonthly?.vatIncluded)}
                </Price>
              </Stack>
            </Stack>
          </Stack>
        </Box>
      </Box>
      <Box padding="4">
        <Text size="BodyS" weight="regular" color="textLowEmphasis">
          Prijzen incl. btw, overheidsheffingen en netbeheerkosten.
        </Text>
      </Box>
    </>
  );
};
