import { FC } from 'react';

import { FlowContext } from '@eneco/flows/types';
import { productUnits } from '@enumerations/productUnits';
import { useFormatter } from '@i18n';
import { DC_Domain_Models_Products_DiscountType } from '@monorepo-types/dc';
import { Fields } from '@sitecore/types/OfferStep';
import { Box, Stack, Text } from '@sparky';
import { GasIcon, ElectricityIcon, WarmthIcon, WaterIcon, RedeliveryIcon } from '@sparky/icons';

import { constructTextualDataKey } from '../../helpers';
import { useProductItemContent } from '../../hooks/sitecore/useProductItemContent';
import { Offer } from '../../types';

interface Props {
  offer?: Offer;
  textualData?: Fields;
  flowContext?: FlowContext;
}

const NEW_ENERGY_3_YEARS_DISCOUNT_TYPE = 'newEnergy3Years' as DC_Domain_Models_Products_DiscountType;
const EXTEND_CONTRACT_DISCOUNT_TYPE = 'extend3Years' as DC_Domain_Models_Products_DiscountType;

export const TariffDetailsOverview: FC<Props> = ({ textualData, offer, flowContext }): JSX.Element => {
  const { energyType, isSME } = flowContext ?? {};
  const { costDetails, usages } = offer ?? {};

  const {
    electricityDeliveryNormal = 0,
    electricityDeliveryLow = 0,
    electricityRedeliveryRemainder = 0,
    gas = 0,
    warmthElectricity = 0,
    warmthWater = 0,
  } = usages || {};

  const { currency } = useFormatter();

  const costsType = isSME ? 'vatExcluded' : 'vatIncluded';

  const textualDataKey = constructTextualDataKey(offer?.contractSpan, energyType);
  const { getProductDetails } = useProductItemContent(textualData?.productData?.productDataList);

  const productDetails = getProductDetails(textualDataKey);

  const tariffDescriptionText = productDetails?.tariffDescription;
  const tariffSplitExplanationText = productDetails?.tariffSplitExplanation;

  return (
    <Text size={{ initial: 'BodyXS', md: 'BodyS' }}>
      <Stack gap={{ initial: '1', md: '3' }}>
        {(electricityDeliveryNormal || electricityDeliveryLow) && (
          <>
            <Stack.Item>
              <Stack direction="row" alignY="start" gap="2">
                <Stack.Item grow={true}>
                  <Stack direction="row" alignY="center" gap="2">
                    <ElectricityIcon color="iconElectricity" />
                    <Text weight="bold">
                      {Number(electricityDeliveryLow) > 0
                        ? textualData?.unitPriceLabels?.electricityHighLabel?.value
                        : textualData?.unitPriceLabels?.electricityLabel?.value}
                    </Text>
                  </Stack>
                </Stack.Item>

                <Stack.Item shrink={false}>
                  <Stack direction="row" alignY="end" gap="2">
                    <Stack.Item grow={true}>
                      {`${currency.euroHighPrecision(
                        costDetails?.electricityCostDetails?.tariff?.price?.[costsType],
                      )} / ${productUnits.electricity}`}
                    </Stack.Item>
                  </Stack>
                </Stack.Item>
              </Stack>
            </Stack.Item>

            {Number(electricityDeliveryLow) > 0 && (
              <Stack.Item>
                <Stack direction="row" alignY="start" gap="2">
                  <Stack.Item grow={true}>
                    <Stack direction="row" alignY="center" gap="2">
                      <ElectricityIcon color="iconElectricity" />
                      <Text weight="bold">{textualData?.unitPriceLabels?.electricityLowLabel?.value}</Text>
                    </Stack>
                  </Stack.Item>

                  <Stack.Item shrink={false}>
                    <Stack direction="row" alignY="end" gap="2">
                      <Stack.Item grow={true}>
                        {`${currency.euroHighPrecision(
                          costDetails?.electricityCostDetails?.lowtariff?.price?.[costsType],
                        )} / ${productUnits.electricity}`}
                      </Stack.Item>
                    </Stack>
                  </Stack.Item>
                </Stack>
              </Stack.Item>
            )}
          </>
        )}

        {Number(gas) > 0 && (
          <Stack.Item>
            <Stack direction="row" alignY="start" gap="2">
              <Stack.Item grow={true}>
                <Stack direction="row" alignY="center" gap="2">
                  <GasIcon color="iconGas" />
                  <Text weight="bold">{textualData?.unitPriceLabels?.gasLabel?.value}</Text>
                </Stack>
              </Stack.Item>

              <Stack.Item shrink={false}>
                <Stack direction="row" alignY="end" gap="2">
                  <Stack.Item grow={true}>
                    {`${currency.euroHighPrecision(costDetails?.gasCostDetails?.tariff?.price?.[costsType])} / ${
                      productUnits.gas
                    }`}
                  </Stack.Item>
                </Stack>
              </Stack.Item>
            </Stack>
          </Stack.Item>
        )}

        {Number(warmthElectricity) > 0 && (
          <Stack.Item>
            <Stack direction="row" alignY="start" gap="2">
              <Stack.Item grow={true}>
                <Stack direction="row" alignY="center" gap="2">
                  <WarmthIcon color="iconHeat" />
                  <Text weight="bold">{textualData?.unitPriceLabels?.warmthLabel?.value}</Text>
                </Stack>
              </Stack.Item>

              <Stack.Item shrink={false}>
                <Stack direction="row" alignY="end" gap="2">
                  <Stack.Item grow={true}>
                    {`${currency.euroHighPrecision(
                      costDetails?.warmthEkvCostDetails?.tariff?.price?.[costsType] ??
                        costDetails?.warmthCostDetails?.tariff?.price?.[costsType],
                    )} / ${productUnits.warmth}`}
                  </Stack.Item>
                </Stack>
              </Stack.Item>
            </Stack>
          </Stack.Item>
        )}

        {Number(warmthWater) > 0 && (
          <>
            <Stack.Item>
              <Stack direction="row" alignY="start" gap="2">
                <Stack.Item grow={true}>
                  <Stack direction="row" alignY="center" gap="2">
                    <WaterIcon color="iconWater" />
                    <Text weight="bold">{textualData?.unitPriceLabels?.waterColdLabel?.value}</Text>
                  </Stack>
                </Stack.Item>

                <Stack.Item shrink={false}>
                  <Stack direction="row" alignY="end" gap="2">
                    <Stack.Item grow={true}>
                      {`${currency.euroHighPrecision(
                        costDetails?.tapwaterCostDetails?.tariffcoldwater?.price?.[costsType],
                      )} / ${productUnits.tapWater}`}
                    </Stack.Item>
                  </Stack>
                </Stack.Item>
              </Stack>
            </Stack.Item>

            <Stack.Item>
              <Stack direction="row" alignY="start" gap="2">
                <Stack.Item grow={true}>
                  <Stack direction="row" alignY="center" gap="2">
                    <WaterIcon color="iconWater" />
                    <Text weight="bold">{textualData?.unitPriceLabels?.waterWarmLabel?.value}</Text>
                  </Stack>
                </Stack.Item>

                <Stack.Item shrink={false}>
                  <Stack direction="row" alignY="end" gap="2">
                    <Stack.Item grow={true}>
                      {`${currency.euroHighPrecision(
                        costDetails?.tapwaterCostDetails?.tariffwarmwater?.price?.[costsType],
                      )} / ${productUnits.tapWater}`}
                    </Stack.Item>
                  </Stack>
                </Stack.Item>
              </Stack>
            </Stack.Item>
          </>
        )}

        {Number(electricityRedeliveryRemainder) > 0 && (
          <Stack.Item>
            <Stack direction="row" alignY="start" gap="2">
              <Stack.Item grow={true}>
                <Stack direction="row" alignY="center" gap="2">
                  <RedeliveryIcon color="iconSolar" />
                  <Text weight="bold">{textualData?.unitPriceLabels?.redeliveryLabel?.value}</Text>
                </Stack>
              </Stack.Item>

              <Stack.Item shrink={false}>
                <Stack direction="row" alignY="end" gap="2">
                  <Stack.Item grow={true}>
                    {`${currency.euroHighPrecision(
                      costDetails?.electricityCostDetails?.redeliverytariff?.price?.[costsType],
                    )} / ${productUnits.electricity}`}
                  </Stack.Item>
                </Stack>
              </Stack.Item>
            </Stack>
          </Stack.Item>
        )}
        {/* TODO - uncomment it when DC part is also ready, we want to introduce it in one-go */}
        {offer?.contractSpan?.some(span => span.duration === 3 && span.byPeriod === 'Y') &&
          (offer?.discountType === NEW_ENERGY_3_YEARS_DISCOUNT_TYPE ||
            offer?.discountType === EXTEND_CONTRACT_DISCOUNT_TYPE) && (
            <Box paddingTop={{ initial: '3', md: '0' }}>
              <Text size="BodyXS" color="textLowEmphasis">
                {tariffSplitExplanationText}
              </Text>
            </Box>
          )}

        {tariffDescriptionText && (
          <Box paddingTop={{ initial: '3', md: '0' }}>
            <Text size="BodyXS" color="textLowEmphasis">
              {tariffDescriptionText}
            </Text>
          </Box>
        )}
      </Stack>
    </Text>
  );
};
