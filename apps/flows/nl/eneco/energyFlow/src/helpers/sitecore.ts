import { capitalizeFirstLetter } from '@common/string';
import { DC_Domain_Models_Products_CostDetailType, DC_Domain_Models_Products_ProductType } from '@monorepo-types/dc';
import { GiftData, ProductData as ProductDataEneco, PromotionTexts } from '@sitecore/types/OfferStep';
import {
  CostDetailsExplanation,
  ParagraphField,
  ProductTypeLabels as ProductTypeLabelsEneco,
} from '@sitecore/types/OverviewStep';
import {
  ProductData as ProductDataOxxio,
  ProductTypeLabels as ProductTypeLabelsOxxio,
} from '@sitecore/types/OxxioOverviewStep';

import { FlowConsumerContextEnergy as FlowContext } from '../context';
import { EnergyType, Offer, OfferContractSpan } from '../types';
import { ENECO_ENERGY_FLOW_GIFT } from './constants';
import {
  checkOfferIncludesDynamicPricingProduct,
  checkOfferIncludesNonDynamicPricingProduct,
  energyTypeIncludes,
} from './misc';
import { OfferType } from '../types/enums';

// Multi Label / Flow Union Types
type ProductDataUnion = Omit<Partial<ProductDataEneco & ProductDataOxxio>, 'productDataList'>;
export type ProductTypeLabelsUnion = Partial<ProductTypeLabelsEneco & ProductTypeLabelsOxxio>;


export const constructTextualDataKey = (contractSpan?: OfferContractSpan[], energyType?: EnergyType): string => {
  const getKeyPrefix = (productType: DC_Domain_Models_Products_ProductType, contractSpans?: OfferContractSpan[]) => {
    const contractSpan = contractSpans?.find(span => span.type === productType);
    // Default key pattern
    const textualDataKey = `${contractSpan?.duration}-${contractSpan?.byPeriod?.toLowerCase()}-${contractSpan?.type}`;
    // Prepend priceDifferentiation key to the default key for TimeOfUse products, not to break the current mapping and to handle TimeOfUse properly
    return `${contractSpan?.priceDifferentiation === 'TimeOfUse' ? `${contractSpan?.priceDifferentiation}-` : ''}${textualDataKey}`;
  };

  switch (energyType) {
    case EnergyType.Gas:
      return `${getKeyPrefix('gas', contractSpan)}`;

    case EnergyType.Electricity:
      return `${getKeyPrefix('electricity', contractSpan)}`;

    case EnergyType.ElectricityAndGas:
    case EnergyType.ElectricityAndGasAndWarmth:
    case EnergyType.ElectricityAndGasAndWarmthAndTapWater:
      return `${getKeyPrefix('electricity', contractSpan)}-${getKeyPrefix('gas', contractSpan)}`;

    case EnergyType.ElectricityAndWarmth:
    case EnergyType.ElectricityAndWarmthAndTapWater:
    case EnergyType.ElectricityAndGeothermalHeating:
      return `${getKeyPrefix('electricity', contractSpan)}-${getKeyPrefix('warmth', contractSpan)}`;

    default:
      return '';
  }
};

export const checkOnDiscountType = (dataKey: string, discountType?: string): string => {
  if (discountType?.includes(OfferType.Variable)) {
    return `${dataKey}-${OfferType.Variable}`;
  }
  return dataKey;
};

export const getProductTitleFromProductType = (
  productType: DC_Domain_Models_Products_ProductType,
  textualData?: ProductTypeLabelsUnion,
): string => {
  switch (productType) {
    case 'electricity':
      return textualData?.electricityProductLabel?.value ?? '';

    case 'gas':
      return textualData?.gasProductLabel?.value ?? '';

    case 'warmth':
    case 'warmthEkv':
      return textualData?.warmthProductLabel?.value ?? '';

    case 'warmtewisselaar':
      return textualData?.warmthExchangeSetLabel?.value ?? '';

    case 'tapwater':
      return textualData?.waterProductLabel?.value ?? '';

    default:
      return capitalizeFirstLetter(productType);
  }
};

type CashbackText = { title: string; text: string; label?: string; trigger: string } | undefined;

export const getCashbackTexts = (
  type?: DC_Domain_Models_Products_CostDetailType,
  promotionTextualData?: PromotionTexts,
  giftDataTextualData?: GiftData,
  cashbackValue?: string | number | undefined,
): CashbackText => {
  if (!type) return undefined;

  if (isCashbackType(type)) {
    return getCashbackPromotionText(type, promotionTextualData);
  }

  if (type === ENECO_ENERGY_FLOW_GIFT) {
    return getGiftPromotionText(giftDataTextualData, cashbackValue);
  }

  return undefined;
};

// check if the type is one of the cashback types
// TODO - replace cashbackDirect and cashbackOnYearNote with constants
const isCashbackType = (type: DC_Domain_Models_Products_CostDetailType): boolean => {
  return type === 'cashBackDirect' || type === 'cashBackOnYearNote';
};

// handle cashback text logic
const getCashbackPromotionText = (
  type: DC_Domain_Models_Products_CostDetailType,
  promotionTextualData?: PromotionTexts,
): CashbackText => {
  const cashbackType = type.replace('cashBack', 'cashback');
  return {
    title: promotionTextualData?.[`${cashbackType}RibbonContent` as keyof PromotionTexts]?.value ?? '',
    text: promotionTextualData?.[`${cashbackType}ExplanationContent` as keyof PromotionTexts]?.value ?? '',
    label: promotionTextualData?.[`${cashbackType}LabelContent` as keyof PromotionTexts]?.value ?? '',
    trigger: promotionTextualData?.cashbackExplanationTriggerText?.value ?? '',
  };
};

// handle gift promotion text logic
const getGiftPromotionText = (
  giftDataTextualData?: GiftData,
  cashbackValue?: string | number | undefined,
): CashbackText => {
  const matchingGift = giftDataTextualData?.giftProductDataList?.find(
    giftProduct => giftProduct?.fields?.gift?.giftId?.value === cashbackValue,
  );

  if (!matchingGift) return undefined;

  return {
    title: matchingGift.fields?.content?.title.value ?? '',
    text: matchingGift.fields?.content?.descriptionContent?.value ?? '',
    trigger: giftDataTextualData?.giftExplanationTriggerText?.value ?? '',
  };
};

const getContractDuration = (offer: Offer, type?: string) => {
  if (type) {
    return offer.contractSpan?.find(contract => contract.type === type);
  }
  return offer?.contractSpan?.[0];
};

const getContractDurationLabel = (
  periodLabel: string,
  monthlyLabel: string,
  yearLabel: string,
  contractSpan?: OfferContractSpan,
) => {
  return `${periodLabel} ${contractSpan?.duration} ${contractSpan?.byPeriod === 'M' ? monthlyLabel : yearLabel}`;
};

export const getOfferTitle = (offer: Offer, textualData?: ProductDataUnion): string => {
  let contractDuration;
  switch (true) {
    case offer?.discountType?.includes('Hybrid'):
      contractDuration = getContractDuration(offer, 'gas');
      return `${getContractDurationLabel(textualData?.hybridProductNameText?.value ?? '', textualData?.monthsLabel?.value ?? '', textualData?.yearLabel?.value ?? '', contractDuration)}`;
    case offer?.discountType?.includes('dynamic'):
      return textualData?.dynamicProductNameText?.value ?? '';
    case offer?.discountType?.includes('variable'):
      return textualData?.variableProductNameText?.value ?? '';
    default:
      contractDuration = getContractDuration(offer);
      return `${getContractDurationLabel(textualData?.fixedProductNameText?.value ?? '', textualData?.monthsLabel?.value ?? '', textualData?.yearLabel?.value ?? '', contractDuration)}`;
  }
};

export const getOfferSubTitle = (
  energyType: EnergyType,
  textualData?: ProductTypeLabelsUnion,
  discountType?: string,
  variablePeriodLabel?: string,
  fixedPeriodLabel?: string,
): string | undefined => {
  if (
    discountType?.includes('Hybrid') &&
    energyTypeIncludes(energyType, EnergyType.Electricity) &&
    energyTypeIncludes(energyType, EnergyType.Gas)
  ) {
    return `${getProductTitleFromProductType('electricity', textualData)} ${variablePeriodLabel ?? ''} & ${getProductTitleFromProductType(
      'gas',
      textualData,
    )} ${fixedPeriodLabel ?? ''}`;
  } else if (energyTypeIncludes(energyType, EnergyType.Electricity) && energyTypeIncludes(energyType, EnergyType.Gas)) {
    return `${getProductTitleFromProductType('electricity', textualData)} & ${getProductTitleFromProductType(
      'gas',
      textualData,
    )}`;
  } else if (energyTypeIncludes(energyType, EnergyType.Gas)) {
    return getProductTitleFromProductType('gas', textualData);
  } else if (energyTypeIncludes(energyType, EnergyType.Electricity)) {
    return getProductTitleFromProductType('electricity', textualData);
  }
};

export const getPriceDetailsExplanationTexts = (
  flowContext: FlowContext,
  offer?: Offer,
  textualData?: CostDetailsExplanation,
): string => {
  const { hasSolarPanels } = flowContext;

  const isDynamicPricingContract =
    checkOfferIncludesDynamicPricingProduct(offer) && !checkOfferIncludesNonDynamicPricingProduct(offer);
  const isHybridContract =
    checkOfferIncludesDynamicPricingProduct(offer) && checkOfferIncludesNonDynamicPricingProduct(offer);
  const isRegularContract = !isDynamicPricingContract && !isHybridContract;

  return Object.keys(textualData || {})
    .sort(elem => (elem === 'lowHighTariffDifferenceParagraph' ? -1 : 0))
    .reduce((acc, elem, index) => {
      // exclude simple TextField values
      if (typeof textualData?.[elem as keyof CostDetailsExplanation]?.value === 'string') {
        return acc;
      }

      // exclude redeliveryParagraph or redeliveryDynamicPricingParagraph  based on "hasSolarPanels" value
      if (
        !hasSolarPanels &&
        (elem === 'redeliveryParagraph' ||
          elem === 'redeliveryDynamicPricingParagraph' ||
          elem === 'redeliveryHybridParagraph')
      ) {
        return acc;
      }

      // exclude certain ParagraphField based on a current offer type
      if (isDynamicPricingContract || isHybridContract) {
        if (
          elem === 'monthlyFeesParagraph' ||
          elem === 'redeliveryParagraph' ||
          elem === 'lowHighTariffDifferenceParagraph'
        ) {
          return acc;
        }

        if (isDynamicPricingContract) {
          if (elem === 'monthlyFeesHybridParagraph' || elem === 'redeliveryHybridParagraph') {
            return acc;
          }
        }

        if (isHybridContract) {
          if (elem === 'monthlyFeesDynamicPricingProductParagraph' || elem === 'redeliveryDynamicPricingParagraph') {
            return acc;
          }
        }
      }

      if (isRegularContract) {
        if (
          elem === 'monthlyFeesDynamicPricingProductParagraph' ||
          elem === 'redeliveryDynamicPricingParagraph' ||
          elem === 'monthlyFeesHybridParagraph' ||
          elem === 'redeliveryHybridParagraph'
        ) {
          return acc;
        }
      }

      const paragraph = (textualData?.[elem as keyof CostDetailsExplanation] as ParagraphField)?.value;

      // exclude empty ParagraphField values
      if (!paragraph?.title || !paragraph?.content) {
        return acc;
      }

      // add new paragraph
      acc += `<p><strong>${paragraph?.title}</strong><br/>${paragraph?.content}</p>`;

      // add additional line break if necessary
      acc += `${index + 1 < Object.keys(textualData || {}).length ? '<br/>' : ''}`;

      return acc;
    }, '');
};
