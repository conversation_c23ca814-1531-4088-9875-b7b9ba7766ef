import { capitalizeFirstLetter } from '@common/string';
import { Label } from '@dc/client/types';
import { putServiceLocationNextBestActionFeedback } from '@dc/services';
import { DetailsIconType } from '@eneco/flows2/types';
import {
  DC_Domain_Models_Products_CostDetailType,
  DC_Domain_Models_Products_ProductType,
  Products_Offers_ProductCostDetail,
  Products_VatModel,
  Products_Offers_V3_OfferResponseModel,
  Products_Offers_V3_OfferProductCombinationResponseModel,
  DC_Domain_Models_Products_DiscountType,
  DC_Repositories_Base_Enumerations_BusinessUnit,
} from '@monorepo-types/dc';

import { ENECO_ENERGY_FLOW_GIFT } from './constants';
import { FlowConsumerContextEnergy as FlowContext } from '../context';
import { EnergyType, Product, Offer, Price, OfferTermsAndConditions, OfferContractSpan } from '../types';
import { NbaStatus } from '../types/types';

export const mergeOfferCosts = (
  costs1?: Products_VatModel,
  costs2?: Products_VatModel,
): Products_VatModel | undefined =>
  Object.fromEntries(
    Object.keys(costs1 ?? {}).map(key => [
      key,
      (costs1?.[key as keyof Products_VatModel] ?? 0) + (costs2?.[key as keyof Products_VatModel] ?? 0),
    ]),
  ) as unknown as Products_VatModel;

export const getOfferCostDetailsDetails = (products: Product[] = []): Record<string, unknown> =>
  products.reduce((acc, product) => Object.assign(acc, { ...product?.costDetails }), {});

export const getCrossSellRentalDeviceProductAvailability = (offer?: Products_Offers_V3_OfferResponseModel): boolean =>
  !!offer?.products?.find(p => p.productType === 'huurapparaat');

export const getUpsellProductAvailability = (
  productType: DC_Domain_Models_Products_ProductType,
  flowContext: FlowContext,
  offer?: Products_Offers_V3_OfferResponseModel,
): boolean => getUpsellProductPrice(productType, flowContext, offer) !== 0;

const getOfferContractSpanFromProducts = (products: Product[]): OfferContractSpan[] =>
  products.map(product => {
    return product?.contractSpan
      ? {
          ...product.contractSpan,
          // Threat "warmthEkv" and "bronwarmte" as regular "warmth" product
          type: product?.type === 'warmthEkv' || product?.type === 'bronwarmte' ? 'warmth' : product?.type,
          priceDifferentiation: product.priceDifferentiation,
        }
      : {
          duration: 0,
          byPeriod: 'D',
          type: 'unknown',
          priceDifferentiation: product.priceDifferentiation,
        };
  });

export const selectProductCombinationByDiscountCode = (
  productCombinations: Products_Offers_V3_OfferProductCombinationResponseModel[],
  discountCode?: string | DC_Domain_Models_Products_DiscountType,
): Products_Offers_V3_OfferProductCombinationResponseModel =>
  productCombinations.find(c => c.discountCode === discountCode) ?? productCombinations[0];

const selectCostDetailsByType = (
  costDetailsType: DC_Domain_Models_Products_CostDetailType,
  costDetails?: Products_Offers_ProductCostDetail[] | null,
): Products_VatModel | undefined => (costDetails ?? []).find(i => i.type === costDetailsType)?.costs;

export const getEnergyTypesFromProductTypes = (
  productCombinations: Array<Array<DC_Domain_Models_Products_ProductType>>,
): EnergyType[] => {
  const energyTypes: EnergyType[] = [];

  productCombinations?.forEach(productCombination => {
    // Threat "warmthEkv" as regular "warmth" product
    const warmthEkvIndex = productCombination.indexOf('warmthEkv');

    if (warmthEkvIndex !== -1) {
      productCombination[warmthEkvIndex] = 'warmth';
    }

    // Remove "warmtewisselaar" product since it is not an "energy" type product
    const warmtewisselaarIndex = productCombination.indexOf('warmtewisselaar');
    if (warmtewisselaarIndex !== -1) {
      productCombination.splice(warmtewisselaarIndex, 1);
    }

    // Product Combination Check: ELECTRICITY && GAS && SOMETHING
    if (productCombination.includes(EnergyType.Electricity) && productCombination.includes(EnergyType.Gas)) {
      // ELECTRICITY && GAS && WARMTH && TAPWATER
      if (productCombination.includes(EnergyType.Warmth) && productCombination.includes(EnergyType.TapWater)) {
        energyTypes.unshift(EnergyType.ElectricityAndGasAndWarmthAndTapWater);
      }
      // ELECTRICITY && GAS && WARMTH
      else if (productCombination.includes(EnergyType.Warmth)) {
        energyTypes.unshift(EnergyType.ElectricityAndGasAndWarmth);
      }
      // ELECTRICITY && GAS
      else {
        energyTypes.unshift(EnergyType.ElectricityAndGas);
      }
    }
    // Product Combination Check: ELECTRICITY && WARMTH && SOMETHING
    else if (productCombination.includes(EnergyType.Electricity) && productCombination.includes(EnergyType.Warmth)) {
      // ELECTRICITY && WARMTH && TAPWATER
      if (productCombination.includes(EnergyType.TapWater)) {
        energyTypes.unshift(EnergyType.ElectricityAndWarmthAndTapWater);
        // ELECTRICITY && WARMTH
      } else {
        energyTypes.unshift(EnergyType.ElectricityAndWarmth);
      }
    }
    // Procuct Combination Check: WARMTH && TAPWATER
    else if (productCombination.includes(EnergyType.Warmth) && productCombination.includes(EnergyType.TapWater)) {
      // WARMTH && TAPWATER
      energyTypes.unshift(EnergyType.WarmthAndTapWater);
    }
    // Product Combination Check: ELECTRICITY && GEOTHERMALHEATING (bronwarmte)
    else if (productCombination.includes(EnergyType.Electricity) && productCombination.includes('bronwarmte')) {
      energyTypes.unshift(EnergyType.ElectricityAndGeothermalHeating);
    }
    // Single Product Choice Check:
    else if (productCombination.length === 1) {
      const capitalizedProductType = capitalizeFirstLetter(productCombination[0]);

      if (productCombination.includes('bronwarmte')) {
        energyTypes.unshift(EnergyType.GeothermalHeating);
      } else if (capitalizedProductType in EnergyType) {
        energyTypes.unshift(EnergyType[capitalizedProductType as keyof typeof EnergyType]);
      }
    }
  });

  return energyTypes;
};

export const getProductTypesFromEnergyType = (
  energyType?: EnergyType,
): Array<DC_Domain_Models_Products_ProductType> => {
  switch (energyType) {
    case EnergyType.Electricity:
      return ['electricity'];
    case EnergyType.ElectricityAndGas:
      return ['electricity', 'gas'];
    case EnergyType.ElectricityAndGasAndWarmth:
      return ['electricity', 'gas', 'warmth', 'warmthEkv', 'warmtewisselaar'];
    case EnergyType.ElectricityAndGasAndWarmthAndTapWater:
      return ['electricity', 'gas', 'warmth', 'warmthEkv', 'tapwater', 'warmtewisselaar'];
    case EnergyType.ElectricityAndWarmth:
      return ['electricity', 'warmth', 'warmthEkv', 'warmtewisselaar'];
    case EnergyType.ElectricityAndWarmthAndTapWater:
      return ['electricity', 'warmth', 'warmthEkv', 'tapwater', 'warmtewisselaar'];
    case EnergyType.ElectricityAndGeothermalHeating:
      return ['electricity', 'bronwarmte', 'warmth'];
    case EnergyType.Gas:
      return ['gas'];
    case EnergyType.Warmth:
      return ['warmth', 'warmthEkv', 'warmtewisselaar'];
    case EnergyType.WarmthAndTapWater:
      return ['warmth', 'warmthEkv', 'tapwater', 'warmtewisselaar'];
    case EnergyType.GeothermalHeating:
      return ['bronwarmte', 'warmth'];
    default:
      return [];
  }
};

export const getProductIconTypeFromProductType = (
  productType: DC_Domain_Models_Products_ProductType,
): DetailsIconType => {
  switch (productType) {
    case 'bronwarmte':
    case 'warmth':
    case 'warmthEkv':
    case 'warmtewisselaar':
      return 'heating';

    case 'gas':
      return 'gas';

    case 'electricity':
      return 'electricity';

    case 'tapwater':
      return 'water';

    case 'huurapparaat':
      return 'boiler';

    default:
      return 'other';
  }
};

export const getProductCategoryFromProductType = (
  productType: DC_Domain_Models_Products_ProductType,
): 'commodity' | 'non-commodity' => {
  if (
    productType === 'gas' ||
    productType === 'electricity' ||
    productType === 'warmth' ||
    productType === 'warmthEkv' ||
    productType === 'tapwater'
  ) {
    return 'commodity';
  }

  return 'non-commodity';
};

const getProducts = (
  selectedProductCombination: Products_Offers_V3_OfferProductCombinationResponseModel,
  offer?: Products_Offers_V3_OfferResponseModel,
): Product[] => {
  return (selectedProductCombination.products
    ?.filter(p => p.productType !== 'cashback')
    ?.map(product => {
      const selectedProduct = offer?.products?.find(p => p.marketingProductCode === product.marketingProductCode);

      return (
        selectedProduct && {
          type: selectedProduct.productType,
          code: selectedProduct.marketingProductCode,

          productCombinationCode: selectedProductCombination?.discountCode ?? undefined,
          variant: selectedProductCombination?.discountCode ?? undefined,

          description: selectedProduct.description,

          contractSpan: selectedProduct.contractSpan,

          costsMonthly: selectCostDetailsByType('monthlyCosts', selectedProduct.costDetails),
          costsYearly: selectCostDetailsByType('yearlyCosts', selectedProduct.costDetails),

          costDetails: {
            [`${product.productType}CostDetails`]: selectedProduct.productRateDetails?.reduce(
              (acc, item) =>
                Object.assign(acc, {
                  [item.type]: {
                    price: {
                      vat: item.vat,
                      vatExcluded: item.vatExcluded,
                      vatIncluded: item.vatIncluded,
                    },
                    components: item.components,
                  },
                }),
              {},
            ),

            [`${product.productType}EnergyTaxDetails`]: selectedProduct.energyTaxDetails?.tariffs?.reduce(
              (acc, item) =>
                Object.assign(acc, {
                  [`bracketNr${item.bracketNr}`]: {
                    active: item.usage > 0,
                    usageMax: item.bracketUsageMax,
                    usageMin: item.bracketUsageMin,
                    price: {
                      vatAmount: item.vatAmount,
                      vatExcluded: item.vatExcluded,
                      vatIncluded: item.vatIncluded,
                    },
                  },
                }),
              {},
            ),

            [`${product.productType}Total`]: selectedProduct.costDetails?.reduce(
              (acc, item) => Object.assign(acc, { [item.type ?? '']: item.costs }),
              {},
            ),
          },

          contractStartDate: offer?.contractStartDate ?? undefined,
          contractEndDate: selectedProduct.contractEndDate ?? undefined,
          priceDifferentiation: selectedProduct.priceDifferentiation,
        }
      );
    }) ?? []) as Product[];
};

const getCashbackValue = (
  selectedProductCombination: Products_Offers_V3_OfferProductCombinationResponseModel,
  offer?: Products_Offers_V3_OfferResponseModel,
): Price | undefined => {
  const cashbackProductMarketingProductCode = selectedProductCombination.products?.find(
    p => p.productType === 'cashback',
  )?.marketingProductCode;

  const cashbackProduct = offer?.products?.find(
    p => p.internalDcMarketingProductCode === cashbackProductMarketingProductCode,
  );

  const cashbackProductCostDetails = cashbackProduct?.costDetails?.find(
    c => c.type === 'cashBackDirect' || c.type === 'cashBackOnYearNote' || c.type === ENECO_ENERGY_FLOW_GIFT,
  );

  if (cashbackProductCostDetails?.type === ENECO_ENERGY_FLOW_GIFT) {
    return {
      value: cashbackProductCostDetails?.value ?? 0,
      type: ENECO_ENERGY_FLOW_GIFT,
    };
  }

  return {
    value: Number(cashbackProductCostDetails?.value),
    type: cashbackProductCostDetails?.type ?? 'unKnown',
  };
};

export const getUpsellProductPrice = (
  productType: DC_Domain_Models_Products_ProductType,
  flowContext: FlowContext,
  offer?: Products_Offers_V3_OfferResponseModel,
): number => {
  const { discountCode } = flowContext;

  const costsType = 'vatIncluded';

  if (offer?.productCombinations) {
    const selectedProductCombination = selectProductCombinationByDiscountCode(offer.productCombinations, discountCode);
    const selectedProduct = selectedProductCombination.products?.find(p => p.productType === productType);

    const selectedProductCostDetails = offer?.products?.find(
      p => p.marketingProductCode === selectedProduct?.marketingProductCode,
    )?.costDetails;
    const alternativeProductCostDetails = offer?.products?.find(
      p =>
        p.marketingProductCode === selectedProduct?.upsellMarketingProductCode ||
        p.marketingProductCode === selectedProduct?.downsellMarketingProductCode,
    )?.costDetails;

    const selectedProductPrice = selectCostDetailsByType('monthlyCosts', selectedProductCostDetails)?.[costsType] ?? 0;
    const alternativeProductPrice =
      selectCostDetailsByType('monthlyCosts', alternativeProductCostDetails)?.[costsType] ?? selectedProductPrice;

    return Math.abs(selectedProductPrice - alternativeProductPrice);
  }

  return 0;
};

export const getOffers = (flowContext: FlowContext, offer?: Products_Offers_V3_OfferResponseModel): Offer[] => {
  const { energyType, hasUpsellGas = false, hasUpsellElectricity = false } = flowContext;

  const selectedProducts = [...getProductTypesFromEnergyType(energyType), ...['cashback']];

  return (
    (offer?.productCombinations
      ?.filter(o =>
        o.products?.every(product => {
          if (selectedProducts.findIndex(p => p === product.productType) === -1) {
            return false;
          }

          if (product.productType === 'electricity') {
            if (hasUpsellElectricity && !!product.upsellMarketingProductCode) {
              return false;
            }

            if (!hasUpsellElectricity && !!product.downsellMarketingProductCode) {
              return false;
            }
          }

          if (product.productType === 'gas') {
            if (hasUpsellGas && !!product.upsellMarketingProductCode) {
              return false;
            }

            if (!hasUpsellGas && !!product.downsellMarketingProductCode) {
              return false;
            }
          }

          return true;
        }),
      )
      .map(o => getOffer({ ...flowContext, discountCode: o.discountCode ?? '' }, offer)) as Offer[]) ?? []
  );
};

export const getOffer = (
  flowContext: FlowContext,
  offer?: Products_Offers_V3_OfferResponseModel,
): Offer | undefined => {
  const { discountCode, energyType, hasCrossSellRentalDevice } = flowContext;

  if (offer?.productCombinations && discountCode && energyType) {
    const selectedProductCombination = selectProductCombinationByDiscountCode(offer.productCombinations, discountCode);

    let selectedProductCombinationMonthlyCosts = selectCostDetailsByType(
      'monthlyCosts',
      selectedProductCombination?.costDetails,
    );
    let selectedProductCombinationYearlyCosts = selectCostDetailsByType(
      'yearlyCosts',
      selectedProductCombination?.costDetails,
    );
    let selectedProductCombinationYearlyPromotionCosts = selectCostDetailsByType(
      'yearlyCostsPromotion',
      selectedProductCombination?.costDetails,
    );

    const selectedProductCombinationProducts = getProducts(selectedProductCombination, offer);

    const selectedProductCombinationCashbackProduct = selectedProductCombination.products?.find(
      p => p.productType === 'cashback',
    );

    const selectedProductCombinationCashbackProductCosts =
      selectedProductCombinationCashbackProduct && getCashbackValue(selectedProductCombination, offer);

    if (hasCrossSellRentalDevice) {
      const selectedRentalDeviceProductCombination = offer.productCombinations.find(p =>
        p.products?.find(product => product.productType === 'huurapparaat'),
      );

      if (selectedRentalDeviceProductCombination) {
        const selectedRentalDeviceProduct = getProducts(selectedRentalDeviceProductCombination, offer)?.[0];

        selectedProductCombinationMonthlyCosts = mergeOfferCosts(
          selectedProductCombinationMonthlyCosts,
          selectedRentalDeviceProduct?.costsMonthly,
        );

        selectedProductCombinationYearlyCosts = mergeOfferCosts(
          selectedProductCombinationYearlyCosts,
          selectedRentalDeviceProduct?.costsYearly,
        );

        selectedProductCombinationYearlyPromotionCosts = mergeOfferCosts(
          selectedProductCombinationYearlyPromotionCosts,
          selectedRentalDeviceProduct?.costsYearly,
        );

        if (selectedRentalDeviceProduct) {
          selectedProductCombinationProducts.push(selectedRentalDeviceProduct);
        }
      }
    }

    const offerObject: Offer = {
      type: selectedProductCombination?.discountCode ?? undefined,
      products: selectedProductCombinationProducts,
      discountType: selectedProductCombination?.discountType ?? '',
      costsMonthly: selectedProductCombinationMonthlyCosts,
      costsYearly: selectedProductCombinationYearlyCosts,
      costDetails: {
        ...getOfferCostDetailsDetails(selectedProductCombinationProducts),
        cashback: selectedProductCombinationCashbackProductCosts,
      },
      costDiscounts: {
        cashback: selectedProductCombinationCashbackProductCosts,
      },
      nbaContext: selectedProductCombination.nbaContext,
      contractSpan: getOfferContractSpanFromProducts(selectedProductCombinationProducts),
      contractStartDate: selectedProductCombinationProducts?.find(product => product?.contractStartDate)
        ?.contractStartDate,
      contractEndDate: selectedProductCombinationProducts?.find(product => product.contractEndDate)?.contractEndDate,
      usages: offer.usages,
    };

    if (
      selectedProductCombinationYearlyCosts?.vatExcluded !== selectedProductCombinationYearlyPromotionCosts?.vatExcluded
    ) {
      offerObject.costsYearlyPromotion = selectedProductCombinationYearlyPromotionCosts;
    }

    const productWithFutureRate = offer?.products?.find(p => p.productFutureRate !== null);
    if (productWithFutureRate?.productFutureRate) {
      offerObject.productFutureRate = productWithFutureRate.productFutureRate;
    }

    return offerObject;
  }

  return undefined;
};

// this function will getOffer opposite of your current selection
// with >>> without upsell product and other way around
export const getOfferAlternative = (
  flowContext: FlowContext,
  offer?: Products_Offers_V3_OfferResponseModel,
): Offer | undefined => {
  const { discountCode, hasUpsellGas, hasUpsellElectricity } = flowContext;

  if (offer?.productCombinations && discountCode) {
    const selectedProducts: string[] =
      selectProductCombinationByDiscountCode(offer.productCombinations, discountCode)?.products?.map(product => {
        if (product.productType === 'electricity') {
          return (
            (hasUpsellElectricity ? product.upsellMarketingProductCode : product.downsellMarketingProductCode) ??
            product.marketingProductCode ??
            ''
          );
        } else if (product.productType === 'gas') {
          return (
            (hasUpsellGas ? product.upsellMarketingProductCode : product.downsellMarketingProductCode) ??
            product.marketingProductCode ??
            ''
          );
        } else {
          return product.marketingProductCode ?? '';
        }
      }) ?? [];

    const selectDiscountCode =
      offer?.productCombinations?.find(
        o =>
          o.products?.length === selectedProducts.length &&
          o.products?.every(product => {
            return selectedProducts.findIndex(p => p === product.marketingProductCode) !== -1;
          }),
      )?.discountCode ?? undefined;

    return getOffer({ ...flowContext, discountCode: selectDiscountCode }, offer);
  }
};

export const getOfferTermsAndConditions = (
  flowContext: FlowContext,
  offer?: Products_Offers_V3_OfferResponseModel,
): OfferTermsAndConditions[] | undefined => {
  const { discountCode, energyType, hasCrossSellRentalDevice = false } = flowContext;
  const salesConditions: OfferTermsAndConditions[] = [];

  const isDuplicate = (title: string, href: string) => {
    return salesConditions.some(condition => condition.title === title && condition.href === href);
  };

  if (offer?.productCombinations && discountCode && energyType) {
    const selectedProdcutCombination = selectProductCombinationByDiscountCode(offer.productCombinations, discountCode);

    // read terms and condition of the individual products
    selectedProdcutCombination?.products?.forEach(product =>
      offer?.products?.forEach(p => {
        if (
          product.marketingProductCode === p.marketingProductCode ||
          product.marketingProductCode === p.internalDcMarketingProductCode
        ) {
          p.labels?.forEach(({ value, url }) => {
            if (value && url && !isDuplicate(value, url)) {
              salesConditions.push({ title: `${value}`, href: `${url}` });
            }
          });
        }
      }),
    );

    // conditional cross-sell rental device logic that will add rental device product
    // related "terms and conditions" document urls to the offer's conditions
    if (hasCrossSellRentalDevice) {
      const selectedRentalDeviceProductCombination = offer.productCombinations.find(p =>
        p.products?.find(product => product.productType === 'huurapparaat'),
      );

      if (selectedRentalDeviceProductCombination) {
        selectedRentalDeviceProductCombination.products?.forEach(product =>
          offer?.products?.forEach(p => {
            if (
              product.marketingProductCode === p.marketingProductCode ||
              product.marketingProductCode === p.internalDcMarketingProductCode
            ) {
              p.labels?.forEach(({ value, url }) => {
                if (value && url && !isDuplicate(value, url)) {
                  salesConditions.push({ title: `${value}`, href: `${url}` });
                }
              });
            }
          }),
        );
      }
    }

    // read terms and condition of the product combination
    selectedProdcutCombination?.labels
      ?.filter(({ key }) => key?.includes('termsandconditions.'))
      .forEach(({ value, url }) => {
        if (value && url && !isDuplicate(value, url)) {
          salesConditions.push({ title: `${value}`, href: `${url}` });
        }
      });
  }

  return salesConditions;
};

export const handleNbaFeedback = (
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit,
  label: Label,
  status: NbaStatus,
  serviceLocationId: string,
  contextId?: string,
  actionId?: number,
  treatmentVariationId?: number,
  servingPointId?: number,
) => {
  if (!status) return null;

  const requestBody = {
    data: {
      status,
      contextId,
      actionId,
      treatmentVariationId,
      servingPointId: servingPointId ? +servingPointId : undefined,
    },
  };

  putServiceLocationNextBestActionFeedback({
    businessUnit: businessUnit.toLocaleLowerCase() as DC_Repositories_Base_Enumerations_BusinessUnit,
    label: label,
    serviceLocationId: serviceLocationId,
    requestBody,
  });
};
