export {
  energyTypeIncludes,
  checkOffersIncludesDynamicPricingProduct,
  checkOfferIncludesDynamicPricingProduct,
  checkOfferIncludesNonDynamicPricingProduct,
  getMinContractStartDateInDays,
  getMaxContractStartDateInDays,
  getMinUrgentContractStartDateInDays,
  getMaxUrgentContractStartDateInDays,
  getMaxUsageValue,
  checkAvailabilityOfferType,
  selectDefaultDiscountCode,
} from './misc';

export { formatInitials, addLeadingZero } from './format';

export {
  getOffer,
  getOfferAlternative,
  getOffers,
  getEnergyTypesFromProductTypes,
  getUpsellProductAvailability,
  getCrossSellRentalDeviceProductAvailability,
  getProductIconTypeFromProductType,
  getProductTypesFromEnergyType,
  getProductCategoryFromProductType,
  getOfferTermsAndConditions,
  getUpsellProductPrice,
  selectProductCombinationByDiscountCode,
} from './dc';

export {
  constructTextualDataKey,
  getCashbackTexts,
  getProductTitleFromProductType,
  getOfferTitle,
  getOfferSubTitle,
  getPriceDetailsExplanationTexts,
} from './sitecore';

export {
  ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX,
  ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_MOVING_OUT,
  ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_SWITCH,
  ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_URGENT,
  ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX_URGENT,
  OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX,
  OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_MOVING_OUT,
  OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_SWITCH,
  OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_URGENT,
  OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX_URGENT,
  CONTRACT_CONSIDERATION_PERIOD_IN_DAYS,
  SOLAR_PANEL_OUTPUT,
  ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_HIGH,
  ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_LOW,
  ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_GAS,
  ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_WARMTH,
  ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_WATER,
  OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_HIGH,
  OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_LOW,
  OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_GAS,
  OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_WARMTH,
  OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_WATER,
} from './constants';
