import { FlowContext } from '@eneco/flows/src/types';
import mockOffer from '@mocks/sitecore/apps/flows/responses/offer.eneco.response.v3.1234AB-56';

import {
  cashBackTexts,
  contractSpanArray1Year,
  contractSpanArray3Year,
  costDetailsExplanation,
  giftData,
  productTypeLabels,
} from './test.data';
import { EnergyType } from '../../types';
import { getOffer } from '../dc';
import {
  constructTextualDataKey,
  getCashbackTexts,
  getOfferSubTitle,
  getPriceDetailsExplanationTexts,
} from '../sitecore';

describe('constructTextualDataKey', () => {
  it('should contstruct a textualkey based on the contract spans and energyType that are given', () => {
    const key = constructTextualDataKey(contractSpanArray1Year, EnergyType.ElectricityAndGas);
    expect(key).toStrictEqual('1-y-electricity-1-y-gas');
  });

  it('should contstruct a textualkey based on the contract spans and energyType (gas) that are given', () => {
    const key = constructTextualDataKey(contractSpanArray1Year, EnergyType.Gas);
    expect(key).toStrictEqual('1-y-gas');
  });

  it('should contstruct a textualkey based on the contract spans and energyType (electricty) that are given', () => {
    const key = constructTextualDataKey(contractSpanArray3Year, EnergyType.Electricity);
    expect(key).toStrictEqual('3-y-electricity');
  });
});

describe('getCashbackTexts', () => {
  it('should provide a object with texts based on the provided discount/cashback', () => {
    const result = getCashbackTexts('cashBackOnYearNote', cashBackTexts, giftData, 240);
    expect(result?.title).toStrictEqual('{cashBackOnYearNote} Cashback On Year Note');
  });

  it('should create a title based on a only gas key and a list provided from sitecore', () => {
    const result = getCashbackTexts('gift', cashBackTexts, giftData, 'Gamma-Giftcard-50');
    expect(result?.title).toStrictEqual('Gamma cadeaukaart 50');
  });

  it('should return undefined when there is no cashback type', () => {
    const result = getCashbackTexts(undefined, cashBackTexts, giftData, 'Gamma-Giftcard-50');
    expect(result).toBeUndefined();
  });

  it('should return undefined when there is no cashback amount', () => {
    const result = getCashbackTexts('gift', cashBackTexts, giftData);
    expect(result).toBeUndefined();
  });
});

describe('getOfferSubTitle', () => {
  it('should construct a subtitle bas on the given data and energyType', () => {
    const result = getOfferSubTitle(EnergyType.ElectricityAndGas, productTypeLabels, undefined, 'variable', 'vast');
    expect(result).toStrictEqual('Groene stroom & Gas');
  });
  it('should construct a subtitle bas on the given data and energyType (gas)', () => {
    const result = getOfferSubTitle(EnergyType.Gas, productTypeLabels, undefined, 'variable', 'vast');
    expect(result).toStrictEqual('Gas');
  });

  it('should construct a subtitle bas on the given data and energyType (electricity)', () => {
    const result = getOfferSubTitle(EnergyType.Electricity, productTypeLabels, undefined, 'variable', 'vast');
    expect(result).toStrictEqual('Groene stroom');
  });

  it('should construct a subtitle bas on the given data and energyType (hybrid electricity and gas)', () => {
    const result = getOfferSubTitle(EnergyType.ElectricityAndGas, productTypeLabels, 'Hybrid', 'variable', 'vast');
    expect(result).toStrictEqual('Groene stroom variable & Gas vast');
  });
});

const mockFlowContext: FlowContext = {
  ci: false,
  energyType: EnergyType.ElectricityAndGas,
  hasDoubleMeter: true,
  hasSolarPanels: true,
  hasUpsellGas: false,
  isGasUpsellProductAvailable: true,
  isMoving: false,
  isRentalDeviceCrossSellProductAvailable: false,
  isUsageKnown: true,
  usageElectricityHigh: 1212,
  usageElectricityLow: 1212,
  usageGas: 121,
};
describe('getPriceDetailsExplanationTexts', () => {
  it('should return a specific set of information texts based on the choices made in the flow', () => {
    const mockContext = {
      ...mockFlowContext,
      hasSolarPanels: false,
    };
    const result = getPriceDetailsExplanationTexts(mockContext, undefined, costDetailsExplanation);
    expect(result).toStrictEqual(
      '<p><strong>Normaal- en daltarieven</strong><br/>We wekken met elkaar steeds meer duurzame stroom op, voornamelijk overdag. Vraag en aanbod verandert hierdoor, wat voor andere prijzen op de energiemarkt zorgt. Dit heeft tot gevolg dat normaal- en daltarieven steeds dichter bij elkaar liggen of gelijk zijn.</p><br/><p><strong>Variabele kosten</strong><br/>De getoonde variabele kosten zijn op basis van de door jou opgegeven hoeveelheid verbruik van kWh / m³ / GJ.</p><br/><p><strong>Vaste kosten</strong><br/>Vaste kosten zijn kosten ongeacht de hoogte van je verbruik (voorheen vastrecht genoemd).</p><br/><p><strong>Netbeheerkosten</strong><br/>Netbeheerkosten zijn de kosten voor je aansluiting en voor het transport door de netbeheerder. Het bedrag wordt door ons namens de netbeheerder bij je in rekening gebracht.</p><br/><p><strong>Verblijfsfunctie</strong><br/>Alleen als er sprake is van een woon-, werk- of andere verblijfsfunctie, heb je recht op vermindering energiebelasting. Dit is een door de overheid vastgesteld kortingbedrag op de energiebelasting.</p><br/><p><strong>Overheidsheffingen</strong><br/>Deze bestaan uit Energiebelasting en Opslag Duurzame Energie.</p><br/><p><strong>Verwachte einddatum</strong><br/>De verwachte einddatum contract is een indicatie gebaseerd op de gemiddelde doorlooptijd van een aanmelding en de gekozen looptijd.</p><br/><p><strong>Prijsverschillen</strong><br/>Bij het berekenen van je kosten ronden wij de tarieven af. Soms ontstaan hierdoor kleine afrondingsverschillen op het prijsoverzicht en de energienota.</p><br/><p><strong>Maandbedrag</strong><br/>Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. Dat kan afwijken van het termijnbedrag dat je gaat betalen. Omdat daarvoor het historisch verbruik van je woning wordt gebruikt. In de Eneco app of op Mijn Eneco pas je later je termijnbedrag makkelijk aan. Alle gebruikte prijzen in deze berekening zijn op basis van normaal tarief 123 kWh, dal tarief 12 kWh en een aansluitwaarde van maximaal 3 x 25 Ampère.</p><br/><p><strong>Actuele tarieven</strong><br/>In dit prijsoverzicht staan altijd de actuele tarieven. In de toekomst kunnen deze tarieven wijzigen. Als dit zo is, dan informeren wij je natuurlijk op tijd.</p><br/><p><strong>Overheid en heffingen</strong><br/>De tarieven van stroom en gas zijn inclusief overheidsheffingen en btw bij een jaarlijks verbruik tot 10.000 kWh / 5.000 m³ en bij gas altijd inclusief regiotoeslag op basis van het door jou opgegeven adres. In het kWh-/m³-tarief dragen wij de bovengenoemde heffingen af aan de overheid.</p><br/>',
    );
  });

  it('should return a specific set of information texts based on the choices made in the flow (with solarpanels)', () => {
    const result = getPriceDetailsExplanationTexts(mockFlowContext, undefined, costDetailsExplanation);
    expect(result).toStrictEqual(
      '<p><strong>Normaal- en daltarieven</strong><br/>We wekken met elkaar steeds meer duurzame stroom op, voornamelijk overdag. Vraag en aanbod verandert hierdoor, wat voor andere prijzen op de energiemarkt zorgt. Dit heeft tot gevolg dat normaal- en daltarieven steeds dichter bij elkaar liggen of gelijk zijn.</p><br/><p><strong>Variabele kosten</strong><br/>De getoonde variabele kosten zijn op basis van de door jou opgegeven hoeveelheid verbruik van kWh / m³ / GJ.</p><br/><p><strong>Vaste kosten</strong><br/>Vaste kosten zijn kosten ongeacht de hoogte van je verbruik (voorheen vastrecht genoemd).</p><br/><p><strong>Netbeheerkosten</strong><br/>Netbeheerkosten zijn de kosten voor je aansluiting en voor het transport door de netbeheerder. Het bedrag wordt door ons namens de netbeheerder bij je in rekening gebracht.</p><br/><p><strong>Verblijfsfunctie</strong><br/>Alleen als er sprake is van een woon-, werk- of andere verblijfsfunctie, heb je recht op vermindering energiebelasting. Dit is een door de overheid vastgesteld kortingbedrag op de energiebelasting.</p><br/><p><strong>Overheidsheffingen</strong><br/>Deze bestaan uit Energiebelasting en Opslag Duurzame Energie.</p><br/><p><strong>Verwachte einddatum</strong><br/>De verwachte einddatum contract is een indicatie gebaseerd op de gemiddelde doorlooptijd van een aanmelding en de gekozen looptijd.</p><br/><p><strong>Prijsverschillen</strong><br/>Bij het berekenen van je kosten ronden wij de tarieven af. Soms ontstaan hierdoor kleine afrondingsverschillen op het prijsoverzicht en de energienota.</p><br/><p><strong>Maandbedrag</strong><br/>Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. Dat kan afwijken van het termijnbedrag dat je gaat betalen. Omdat daarvoor het historisch verbruik van je woning wordt gebruikt. In de Eneco app of op Mijn Eneco pas je later je termijnbedrag makkelijk aan. Alle gebruikte prijzen in deze berekening zijn op basis van normaal tarief 123 kWh, dal tarief 12 kWh en een aansluitwaarde van maximaal 3 x 25 Ampère.</p><br/><p><strong>Actuele tarieven</strong><br/>In dit prijsoverzicht staan altijd de actuele tarieven. In de toekomst kunnen deze tarieven wijzigen. Als dit zo is, dan informeren wij je natuurlijk op tijd.</p><br/><p><strong>Overheid en heffingen</strong><br/>De tarieven van stroom en gas zijn inclusief overheidsheffingen en btw bij een jaarlijks verbruik tot 10.000 kWh / 5.000 m³ en bij gas altijd inclusief regiotoeslag op basis van het door jou opgegeven adres. In het kWh-/m³-tarief dragen wij de bovengenoemde heffingen af aan de overheid.</p><br/><p><strong>Teruglevering</strong><br/>Als je zelf stroom opwekt, dan verrekenen we de door jou terug geleverde stroom met je stroomverbruik. Betaal je twee tarieven voor je stroom? Dan trekken we de terug geleverde stroom eerst af van het normaal- en daarna van het dalverbruik. Lever je zelfs meer stroom terug dan je totale stroomverbruik? Daarvoor ontvang je een terugleververgoeding. Dit is exclusief energiebelasting, opslag duurzame energie of btw. De terugleververgoeding kan tussentijds wijzigen.</p><br/>',
    );
  });

  it('should return a specific set of information texts based on the choices made in the flow (hybrid offer)', () => {
    const offerWithDynamicPricingProduct = getOffer(
      { energyType: EnergyType.ElectricityAndGas, discountCode: 'POAH12BSxxxxxC000xx' },
      mockOffer,
    );
    const result = getPriceDetailsExplanationTexts(
      mockFlowContext,
      offerWithDynamicPricingProduct,
      costDetailsExplanation,
    );
    expect(result).toStrictEqual(
      '<p><strong>Variabele kosten</strong><br/>De getoonde variabele kosten zijn op basis van de door jou opgegeven hoeveelheid verbruik van kWh / m³ / GJ.</p><br/><p><strong>Vaste kosten</strong><br/>Vaste kosten zijn kosten ongeacht de hoogte van je verbruik (voorheen vastrecht genoemd).</p><br/><p><strong>Netbeheerkosten</strong><br/>Netbeheerkosten zijn de kosten voor je aansluiting en voor het transport door de netbeheerder. Het bedrag wordt door ons namens de netbeheerder bij je in rekening gebracht.</p><br/><p><strong>Verblijfsfunctie</strong><br/>Alleen als er sprake is van een woon-, werk- of andere verblijfsfunctie, heb je recht op vermindering energiebelasting. Dit is een door de overheid vastgesteld kortingbedrag op de energiebelasting.</p><br/><p><strong>Overheidsheffingen</strong><br/>Deze bestaan uit Energiebelasting en Opslag Duurzame Energie.</p><br/><p><strong>Verwachte einddatum</strong><br/>De verwachte einddatum contract is een indicatie gebaseerd op de gemiddelde doorlooptijd van een aanmelding en de gekozen looptijd.</p><br/><p><strong>Prijsverschillen</strong><br/>Bij het berekenen van je kosten ronden wij de tarieven af. Soms ontstaan hierdoor kleine afrondingsverschillen op het prijsoverzicht en de energienota.</p><br/><p><strong>Maandbedrag Hybrid</strong><br/>Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. Dat kan afwijken van het termijnbedrag dat je gaat betalen. De getoonde leveringstarieven gelden voor de situatie in je woning op basis van het aantal (actieve) telwerken. De tarieven voor elektriciteit variëren per uur. Je vindt de actuele tarieven een dag van tevoren (rond 15.00 uur) terug in de Eneco app. In dit prijsoverzicht staan altijd de actuele verwachte tarieven.</p><br/><p><strong>Actuele tarieven</strong><br/>In dit prijsoverzicht staan altijd de actuele tarieven. In de toekomst kunnen deze tarieven wijzigen. Als dit zo is, dan informeren wij je natuurlijk op tijd.</p><br/><p><strong>Overheid en heffingen</strong><br/>De tarieven van stroom en gas zijn inclusief overheidsheffingen en btw bij een jaarlijks verbruik tot 10.000 kWh / 5.000 m³ en bij gas altijd inclusief regiotoeslag op basis van het door jou opgegeven adres. In het kWh-/m³-tarief dragen wij de bovengenoemde heffingen af aan de overheid.</p><br/><p><strong>Teruglevering Hybrid</strong><br/>Als je zelf stroom opwekt - en dit niet direct zelf gebruikt - lever je deze opgewekte stroom terug. Dit kan alleen als je aangemeld bent op energieleveren.nl. Je ontvangt voor teruggeleverde stroom het werkelijke uurtarief per kWh op het moment van terugleveren, inclusief de inkoopvergoeding en overheidsheffingen en btw. Lever je over het gehele verbruiksjaar, waar de (jaar)nota op betrekking heeft, meer terug dan dat je verbruikt, dan ontvang je, in afwijking van artikel 3 van het Voorwaardenoverzicht, voor het netto teruggeleverde volume de marktprijs per kWh, die in het uur van teruglevering geldt (exclusief de inkoopvergoeding en overheidsheffingen en btw) min de verkoopvergoeding (€ 0,02232 per kWh tot 31 okt, vanaf 1 nov € 0,02564 per kWh). De verkoopvergoeding is ter dekking van de kosten die Eneco maakt voor de verkoop en afhandeling van stroom op de energiemarkt en kan wijzigen, waardoor de vergoeding voor netto teruggeleverde stroom kan fluctueren.</p>',
    );
  });

  it('should return a specific set of information texts based on the choices made in the flow (dynamic offer)', () => {
    const offerWithoutDynamicPricingProduct = getOffer(
      { energyType: EnergyType.ElectricityAndGas, discountCode: 'POAA12AAxxxxxC000xx' },
      mockOffer,
    );
    const result = getPriceDetailsExplanationTexts(
      mockFlowContext,
      offerWithoutDynamicPricingProduct,
      costDetailsExplanation,
    );
    expect(result).toStrictEqual(
      '<p><strong>Normaal- en daltarieven</strong><br/>We wekken met elkaar steeds meer duurzame stroom op, voornamelijk overdag. Vraag en aanbod verandert hierdoor, wat voor andere prijzen op de energiemarkt zorgt. Dit heeft tot gevolg dat normaal- en daltarieven steeds dichter bij elkaar liggen of gelijk zijn.</p><br/><p><strong>Variabele kosten</strong><br/>De getoonde variabele kosten zijn op basis van de door jou opgegeven hoeveelheid verbruik van kWh / m³ / GJ.</p><br/><p><strong>Vaste kosten</strong><br/>Vaste kosten zijn kosten ongeacht de hoogte van je verbruik (voorheen vastrecht genoemd).</p><br/><p><strong>Netbeheerkosten</strong><br/>Netbeheerkosten zijn de kosten voor je aansluiting en voor het transport door de netbeheerder. Het bedrag wordt door ons namens de netbeheerder bij je in rekening gebracht.</p><br/><p><strong>Verblijfsfunctie</strong><br/>Alleen als er sprake is van een woon-, werk- of andere verblijfsfunctie, heb je recht op vermindering energiebelasting. Dit is een door de overheid vastgesteld kortingbedrag op de energiebelasting.</p><br/><p><strong>Overheidsheffingen</strong><br/>Deze bestaan uit Energiebelasting en Opslag Duurzame Energie.</p><br/><p><strong>Verwachte einddatum</strong><br/>De verwachte einddatum contract is een indicatie gebaseerd op de gemiddelde doorlooptijd van een aanmelding en de gekozen looptijd.</p><br/><p><strong>Prijsverschillen</strong><br/>Bij het berekenen van je kosten ronden wij de tarieven af. Soms ontstaan hierdoor kleine afrondingsverschillen op het prijsoverzicht en de energienota.</p><br/><p><strong>Maandbedrag</strong><br/>Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. Dat kan afwijken van het termijnbedrag dat je gaat betalen. Omdat daarvoor het historisch verbruik van je woning wordt gebruikt. In de Eneco app of op Mijn Eneco pas je later je termijnbedrag makkelijk aan. Alle gebruikte prijzen in deze berekening zijn op basis van normaal tarief 123 kWh, dal tarief 12 kWh en een aansluitwaarde van maximaal 3 x 25 Ampère.</p><br/><p><strong>Actuele tarieven</strong><br/>In dit prijsoverzicht staan altijd de actuele tarieven. In de toekomst kunnen deze tarieven wijzigen. Als dit zo is, dan informeren wij je natuurlijk op tijd.</p><br/><p><strong>Overheid en heffingen</strong><br/>De tarieven van stroom en gas zijn inclusief overheidsheffingen en btw bij een jaarlijks verbruik tot 10.000 kWh / 5.000 m³ en bij gas altijd inclusief regiotoeslag op basis van het door jou opgegeven adres. In het kWh-/m³-tarief dragen wij de bovengenoemde heffingen af aan de overheid.</p><br/><p><strong>Teruglevering</strong><br/>Als je zelf stroom opwekt, dan verrekenen we de door jou terug geleverde stroom met je stroomverbruik. Betaal je twee tarieven voor je stroom? Dan trekken we de terug geleverde stroom eerst af van het normaal- en daarna van het dalverbruik. Lever je zelfs meer stroom terug dan je totale stroomverbruik? Daarvoor ontvang je een terugleververgoeding. Dit is exclusief energiebelasting, opslag duurzame energie of btw. De terugleververgoeding kan tussentijds wijzigen.</p><br/>',
    );
  });

  it('should return a empty string when there is no textualData', () => {
    const result = getPriceDetailsExplanationTexts(mockFlowContext);
    expect(result).toStrictEqual('');
  });
});
