import { putServiceLocationNextBestActionFeedback } from '@dc/services';
import { FlowContext } from '@eneco/flows/types';
import mockOffer from '@mocks/sitecore/apps/flows/responses/offer.eneco.response.v3.1234AB-56';
import mockOfferWarmth from '@mocks/sitecore/apps/flows/responses/offer.eneco.response.v3.2002BB-200';
import mockOfferOxxio from '@mocks/sitecore/apps/flows/responses/offer.oxxio.response.v3.1234AB-56';
import { Products_VatModel } from '@monorepo-types/dc';

import { EnergyType, Product } from '../../types';
import {
  getEnergyTypesFromProductTypes,
  getOffer,
  getOffers,
  getProductCategoryFromProductType,
  getProductIconTypeFromProductType,
  getProductTypesFromEnergyType,
  getOfferTermsAndConditions,
  getUpsellProductPrice,
  mergeOfferCosts,
  getOfferCostDetailsDetails,
  getOfferAlternative,
  handleNbaFeedback,
} from '../dc';
import { getProductTitleFromProductType } from '../sitecore';

jest.mock('@dc/services', () => ({
  ...jest.requireActual('@dc/services'),
  putServiceLocationNextBestActionFeedback: jest.fn(),
}));

describe('Given a getOffers', () => {
  describe('when DC mock data and EnergyType are provided', () => {
    it('should return product offers', () => {
      const mockFlowContext: FlowContext = {
        energyType: EnergyType.ElectricityAndGas,
        hasUpsellGas: true,
      };

      expect(getOffers(mockFlowContext, mockOffer)).toHaveLength(4);
    });
  });
});

describe('Given a getEnergyTypesFromProductTypes', () => {
  describe('when ProductType(s) are provided', () => {
    it('should return corresponding EnergyType(s)', () => {
      expect(getEnergyTypesFromProductTypes([['electricity'], ['gas'], ['electricity', 'gas']])).toStrictEqual([
        'electricityAndGas',
        'gas',
        'electricity',
      ]);
      expect(getEnergyTypesFromProductTypes([['electricity'], ['warmth'], ['electricity', 'warmth']])).toStrictEqual([
        'electricityAndWarmth',
        'warmth',
        'electricity',
      ]);

      expect(
        getEnergyTypesFromProductTypes([
          ['electricity'],
          ['electricity', 'warmth', 'tapwater'],
          ['warmth', 'tapwater'],
        ]),
      ).toStrictEqual(['warmthAndTapWater', 'electricityAndWarmthAndTapWater', 'electricity']);

      expect(
        getEnergyTypesFromProductTypes([['electricity'], ['electricity', 'bronwarmte'], ['bronwarmte']]),
      ).toStrictEqual(['geothermalHeating', 'electricityAndGeothermalHeating', 'electricity']);

      expect(getEnergyTypesFromProductTypes([['tapwater']])).toStrictEqual([]);
    });
  });
});

describe('Given a getProductTypesFromEnergyType', () => {
  describe('when EnergyType is provided', () => {
    it('should return corresponding ProductType(s)', () => {
      expect(getProductTypesFromEnergyType(EnergyType.ElectricityAndGas)).toStrictEqual(['electricity', 'gas']);
      expect(getProductTypesFromEnergyType(EnergyType.ElectricityAndWarmth)).toStrictEqual([
        'electricity',
        'warmth',
        'warmthEkv',
        'warmtewisselaar',
      ]);
      expect(getProductTypesFromEnergyType(EnergyType.ElectricityAndWarmthAndTapWater)).toStrictEqual([
        'electricity',
        'warmth',
        'warmthEkv',
        'tapwater',
        'warmtewisselaar',
      ]);
    });
  });
});

describe('Given a getProductTitleFromProductType', () => {
  const sitecoreTextualDataMock = {
    warmthProductLabel: { value: 'Warmte' },
    electricityProductLabel: { value: 'Groene stroom' },
    gasProductLabel: { value: 'Gas' },
    waterProductLabel: { value: 'Water' },
    warmthExchangeSetLabel: { value: 'Warmtewisselaar' },
  };

  describe('when ProductType and Textual Data are provided', () => {
    it('should return corresponding value', () => {
      expect(getProductTitleFromProductType('warmth', sitecoreTextualDataMock)).toEqual('Warmte');
      expect(getProductTitleFromProductType('tapwater', sitecoreTextualDataMock)).toEqual('Water');
      expect(getProductTitleFromProductType('gas', sitecoreTextualDataMock)).toEqual('Gas');
      expect(getProductTitleFromProductType('electricity', sitecoreTextualDataMock)).toEqual('Groene stroom');
      expect(getProductTitleFromProductType('warmtewisselaar', sitecoreTextualDataMock)).toEqual('Warmtewisselaar');
      expect(getProductTitleFromProductType('huurapparaat', sitecoreTextualDataMock)).toEqual('Huurapparaat');
    });
  });
});

describe('Given a getProductIconTypeFromProductType', () => {
  describe('when Product Type is provided', () => {
    it('should return corresponding value', () => {
      expect(getProductIconTypeFromProductType('bronwarmte')).toEqual('heating');
      expect(getProductIconTypeFromProductType('warmth')).toEqual('heating');
      expect(getProductIconTypeFromProductType('warmtewisselaar')).toEqual('heating');
      expect(getProductIconTypeFromProductType('tapwater')).toEqual('water');
      expect(getProductIconTypeFromProductType('gas')).toEqual('gas');
      expect(getProductIconTypeFromProductType('electricity')).toEqual('electricity');
      expect(getProductIconTypeFromProductType('huurapparaat')).toEqual('boiler');

      expect(getProductIconTypeFromProductType('heatPump')).toEqual('other');
    });
  });
});

describe('Given a getProductCategoryFromProductType', () => {
  describe('when Product Type is provided', () => {
    it('should return corresponding value', () => {
      expect(getProductCategoryFromProductType('warmth')).toEqual('commodity');
      expect(getProductCategoryFromProductType('warmtewisselaar')).toEqual('non-commodity');
      expect(getProductCategoryFromProductType('tapwater')).toEqual('commodity');
      expect(getProductCategoryFromProductType('gas')).toEqual('commodity');
      expect(getProductCategoryFromProductType('electricity')).toEqual('commodity');
      expect(getProductCategoryFromProductType('huurapparaat')).toEqual('non-commodity');
      expect(getProductCategoryFromProductType('bronwarmte')).toEqual('non-commodity');
    });
  });
});

describe('Give a getOffer', () => {
  describe('when DC mock data, OfferType with gas upsell are provided', () => {
    it('should return correct data', () => {
      const mockFlowContext: FlowContext = {
        discountCode: 'POCA12AAxxxxxC000xx',
        energyType: EnergyType.ElectricityAndGas,
        hasUpsellGas: true,
      };

      const offerResponse = getOffer(mockFlowContext, mockOffer);

      expect(offerResponse?.products).toHaveLength(2);
      expect(offerResponse?.costsYearly?.vatIncluded).toBe(4792.9);
      expect(offerResponse?.costsMonthly?.vatIncluded).toBe(399.41);
      expect(offerResponse?.contractSpan).toEqual([
        { byPeriod: 'Y', duration: 1, type: 'electricity' },
        { byPeriod: 'Y', duration: 1, type: 'gas' },
      ]);
    });
  });
  describe('when DC mock data, OfferType without upsell are provided', () => {
    it('should return correct data', () => {
      const mockFlowContext: FlowContext = {
        discountCode: 'POAA12AAxxxxxC000xx',
        energyType: EnergyType.ElectricityAndGas,
        hasUpsellGas: false,
      };

      const offerResponse = getOffer(mockFlowContext, mockOffer);

      expect(offerResponse?.products).toHaveLength(2);
      expect(offerResponse?.costsYearly?.vatIncluded).toBe(4780.9);
      expect(offerResponse?.costsMonthly?.vatIncluded).toBe(398.41);
      expect(offerResponse?.contractSpan).toEqual([
        { byPeriod: 'Y', duration: 1, type: 'electricity' },
        { byPeriod: 'Y', duration: 1, type: 'gas' },
      ]);
    });
  });
  describe('when DC mock (oxxio) data, OfferType with gas and electricity upsells are provided', () => {
    it('should return correct data', () => {
      const mockFlowContext: FlowContext = {
        discountCode: '1WCF12xxxxx1C200xxx',
        energyType: EnergyType.ElectricityAndGas,
        hasUpsellGas: true,
        hasUpsellElectricity: true,
      };

      const offerResponse = getOffer(mockFlowContext, mockOfferOxxio);

      expect(offerResponse?.products).toHaveLength(2);
      expect(offerResponse?.costsYearly?.vatIncluded).toBe(4805.74);
      expect(offerResponse?.costsMonthly?.vatIncluded).toBe(400.48);
      expect(offerResponse?.contractSpan).toEqual([
        { byPeriod: 'Y', duration: 1, type: 'electricity' },
        { byPeriod: 'Y', duration: 1, type: 'gas' },
      ]);
    });
  });
  describe('when DC mock (oxxio) data, OfferType without upsells are provided', () => {
    it('should return correct data', () => {
      const mockFlowContext: FlowContext = {
        discountCode: '1EGF12xxxSO1C200xxx',
        energyType: EnergyType.ElectricityAndGas,
        hasUpsellGas: false,
        hasUpsellElectricity: false,
      };

      const offerResponse = getOffer(mockFlowContext, mockOfferOxxio);

      expect(offerResponse?.products).toHaveLength(2);
      expect(offerResponse?.costsYearly?.vatIncluded).toBe(4787.74);
      expect(offerResponse?.costsMonthly?.vatIncluded).toBe(398.98);
      expect(offerResponse?.contractSpan).toEqual([
        { byPeriod: 'Y', duration: 1, type: 'electricity' },
        { byPeriod: 'Y', duration: 1, type: 'gas' },
      ]);
    });
  });
});

describe('Given getOfferTermsAndConditions', () => {
  describe('when DC mock data, OfferType and ProductCombinationCode are provided ', () => {
    it('should return 1 document links based on DC mock data', () => {
      const mockFlowContext: FlowContext = {
        discountCode: 'POCA12AAxxxxxC000xx',
        energyType: EnergyType.ElectricityAndGas,
      };

      expect(getOfferTermsAndConditions(mockFlowContext, mockOffer)).toHaveLength(1);
    });

    it('should return 0 document links based on DC mock data', () => {
      const mockFlowContext: FlowContext = {
        discountCode: 'HP8xP0xA12xWRVxWTWKTWxWWSET',
        energyType: EnergyType.ElectricityAndWarmth,
      };

      expect(getOfferTermsAndConditions(mockFlowContext, mockOfferWarmth)).toHaveLength(0);
    });
  });
});

describe('Given getUpsellProductPrice', () => {
  describe('when DC mock data, OfferType, EnergyType and isSME proprty are provided ', () => {
    it('should return price including vat', () => {
      const mockFlowContext: FlowContext = {
        discountCode: 'POAA12AAxxxxxC000xx',
        energyType: EnergyType.ElectricityAndGas,
        isSME: false,
      };

      expect(getUpsellProductPrice('gas', mockFlowContext, mockOffer)).toBeCloseTo(1);
    });
  });

  describe('when DC mock (oxxio) data, OfferType, EnergyType and isSME proprty are provided ', () => {
    it('should return price including vat', () => {
      const mockFlowContext: FlowContext = {
        discountCode: '1WCF12xxxxx1C200xxx',
        energyType: EnergyType.ElectricityAndGas,
        isSME: false,
      };

      expect(getUpsellProductPrice('electricity', mockFlowContext, mockOfferOxxio)).toBeCloseTo(0.5);
    });
  });
});

describe('mergeOfferCosts', () => {
  it('merges two cost objects correctly', () => {
    const costs1: Products_VatModel = {
      vatExcluded: 100,
      vatIncluded: 120,
      vatAmount: 20,
    };
    const costs2: Products_VatModel = {
      vatExcluded: 50,
      vatIncluded: 60,
      vatAmount: 10,
    };

    const result = mergeOfferCosts(costs1, costs2);
    expect(result).toEqual({
      vatExcluded: 150,
      vatIncluded: 180,
      vatAmount: 30,
    });
  });

  it('handles undefined first cost object', () => {
    const costs2: Products_VatModel = {
      vatExcluded: 50,
      vatIncluded: 60,
      vatAmount: 10,
    };

    const result = mergeOfferCosts(undefined, costs2);
    expect(result).toEqual({});
  });

  it('handles undefined second cost object', () => {
    const costs1: Products_VatModel = {
      vatExcluded: 100,
      vatIncluded: 120,
      vatAmount: 20,
    };

    const result = mergeOfferCosts(costs1, undefined);
    expect(result).toEqual(costs1);
  });

  it('handles both cost objects as undefined', () => {
    const result = mergeOfferCosts(undefined, undefined);
    expect(result).toEqual({});
  });

  it('handles cost objects with zero values', () => {
    const costs1: Products_VatModel = {
      vatExcluded: 0,
      vatIncluded: 0,
      vatAmount: 0,
    };
    const costs2: Products_VatModel = {
      vatExcluded: 0,
      vatIncluded: 0,
      vatAmount: 0,
    };

    const result = mergeOfferCosts(costs1, costs2);
    expect(result).toEqual({
      vatExcluded: 0,
      vatIncluded: 0,
      vatAmount: 0,
    });
  });
});

describe('getOfferCostDetailsDetails', () => {
  it('should merge costDetails from all products into a single object', () => {
    const products: Product[] = [
      {
        type: 'electricity',
        description: 'Product A',
        code: 'SKU123',
        costDetails: { price: 100, tax: 10, type: 'electricity' },
        priceDifferentiation: 'Unknown',
      },
      {
        type: 'gas',
        description: 'Product B',
        code: 'SKU456',
        costDetails: { discount: 20, shipping: 5, type: 'gas' },
        priceDifferentiation: 'Unknown',
      },
    ];

    const result = getOfferCostDetailsDetails(products);

    expect(result).toEqual({
      discount: 20,
      price: 100,
      shipping: 5,
      tax: 10,
      type: 'gas',
    });
  });

  it('should return an empty object when products array is empty', () => {
    const products: Product[] = [];

    const result = getOfferCostDetailsDetails(products);

    expect(result).toEqual({});
  });

  it('should overwrite duplicate keys with the last product’s value', () => {
    const products: Product[] = [
      {
        type: 'electricity',
        description: 'Product A',
        code: 'SKU123',
        costDetails: { price: 100, tax: 10, type: 'electricity' },
        priceDifferentiation: 'Unknown',
      },
      {
        type: 'electricity',
        description: 'Product B',
        code: 'SKU456',
        costDetails: { price: 150, shipping: 5, type: 'electricity' },
        priceDifferentiation: 'Unknown',
      },
    ];

    const result = getOfferCostDetailsDetails(products);

    expect(result).toEqual({
      price: 150, // Overwritten by Product B
      tax: 10,
      shipping: 5,
      type: 'electricity',
    });
  });
});

describe('getOfferAlternative', () => {
  const mockFlowContext: FlowContext = {
    discountCode: '1WCF12xxxxx1C200xxx',
    energyType: EnergyType.ElectricityAndGas,
    isSME: false,
  };

  it('should return a matching product combination is found', () => {
    const result = getOfferAlternative(mockFlowContext, mockOffer);

    expect(result?.products).toHaveLength(3);
    expect(result?.costsYearly?.vatIncluded).toBe(4922.02);
    expect(result?.costsMonthly?.vatIncluded).toBe(410.17);
  });

  it('should return undefined when offer has no product combinations', () => {
    const result = getOfferAlternative(mockFlowContext, undefined);
    expect(result).toBeUndefined();
  });

  it('should handle missing discount code gracefully', () => {
    const result = getOfferAlternative({ ...mockFlowContext, discountCode: undefined }, mockOffer);

    expect(result).toBeUndefined();
  });
});

describe('handleNbaFeedback', () => {
  const mockFeedbackService = putServiceLocationNextBestActionFeedback as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call the feedback service with the correct parameters', () => {
    const businessUnit = 'nl';
    const label = 'eneco';
    const status = 'Viewed';
    const serviceLocationId = '12345';
    const contextId = 'context-1';
    const actionId = 10;
    const treatmentVariationId = 5;
    const servingPointId = 42;

    handleNbaFeedback(
      businessUnit,
      label,
      status,
      serviceLocationId,
      contextId,
      actionId,
      treatmentVariationId,
      servingPointId,
    );

    expect(mockFeedbackService).toHaveBeenCalledWith({
      businessUnit: 'nl',
      label: 'eneco',
      serviceLocationId: '12345',
      requestBody: {
        data: {
          status: 'Viewed',
          contextId: 'context-1',
          actionId: 10,
          treatmentVariationId: 5,
          servingPointId: 42,
        },
      },
    });
  });

  it('should call the feedback service with undefined servingPointId if it is not provided', () => {
    const businessUnit = 'be';
    const label = 'oxxio';
    const status = 'Success';
    const serviceLocationId = '67890';
    const contextId = 'context-2';

    handleNbaFeedback(businessUnit, label, status, serviceLocationId, contextId);

    expect(mockFeedbackService).toHaveBeenCalledWith({
      businessUnit: 'be',
      label: 'oxxio',
      serviceLocationId: '67890',
      requestBody: {
        data: {
          status: 'Success',
          contextId: 'context-2',
          actionId: undefined,
          treatmentVariationId: undefined,
          servingPointId: undefined,
        },
      },
    });
  });

  it('should convert servingPointId to a number if it is provided as a string', () => {
    const businessUnit = 'nl';
    const label = 'eneco';
    const status = 'Served';
    const serviceLocationId = '12345';
    const servingPointId = 42;

    handleNbaFeedback(businessUnit, label, status, serviceLocationId, undefined, undefined, undefined, servingPointId);

    expect(mockFeedbackService).toHaveBeenCalledWith({
      businessUnit: 'nl',
      label: 'eneco',
      serviceLocationId: '12345',
      requestBody: {
        data: {
          status: 'Served',
          contextId: undefined,
          actionId: undefined,
          treatmentVariationId: undefined,
          servingPointId: 42, // Converted to number
        },
      },
    });
  });
});
