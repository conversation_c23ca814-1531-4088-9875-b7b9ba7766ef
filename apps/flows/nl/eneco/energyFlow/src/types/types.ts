import { FlowActions, FlowRoute } from '@eneco/flows2/src/types';
import {
  DC_Domain_Models_NextBestAction_FeedbackStatus,
  DC_Domain_Models_Products_CostDetailType,
  DC_Domain_Models_Products_DiscountType,
  DC_Domain_Models_Products_ProductType,
  Products_Offers_ContractSpan,
  Products_Offers_V3_OfferUsagesResponseModel,
  Products_ProductRates_ProductFutureRateViewModel,
  Products_VatModel,
} from '@monorepo-types/dc';
import { Products_Offers_NbaContextResponse } from 'types/dc/src/Products_Offers_NbaContextResponse';

import { FlowConsumerContextEnergy } from '../context';
export interface Price {
  value: string | number;
  previousValue?: string | number;

  period?: string;
  type?: DC_Domain_Models_Products_CostDetailType;
}

export interface CostDetails {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

type PriceDifferentiation = 'Standard' | 'Dynamic' | 'TimeOfUse' | 'Unknown';

export interface Product {
  type: DC_Domain_Models_Products_ProductType;

  description?: string;
  tag?: string;

  code: string; // SKU
  productCombinationCode?: string;

  variant?: DC_Domain_Models_Products_DiscountType;

  contractSpan?: Products_Offers_ContractSpan;

  costsMonthly?: Products_VatModel;
  costsYearly?: Products_VatModel;

  costDetails: Record<string, unknown>;

  contractEndDate?: string;
  contractStartDate?: string;

  mostPopularChoice?: boolean;
  priceDifferentiation: PriceDifferentiation;
  productFutureRate?: Products_ProductRates_ProductFutureRateViewModel;
}

export interface OfferContractSpan extends Products_Offers_ContractSpan {
  type: DC_Domain_Models_Products_ProductType;
  priceDifferentiation?: PriceDifferentiation;
}

export interface Offer {
  type?: string;

  title?: string;
  tag?: string;

  usps?: string[];

  products: Product[];

  mostPopularChoice?: boolean;

  costsMonthly?: Products_VatModel;
  costsYearly?: Products_VatModel;
  costsYearlyPromotion?: Products_VatModel;

  costDiscounts: {
    cashback?: Price;
  };
  nbaContext?: Products_Offers_NbaContextResponse;

  costDetails: CostDetails;

  contractSpan?: OfferContractSpan[];

  contractEndDate?: string;
  contractStartDate?: string;
  discountType?: string;

  usages?: Products_Offers_V3_OfferUsagesResponseModel;
  productFutureRate?: Products_ProductRates_ProductFutureRateViewModel;
}

export interface OfferTermsAndConditions {
  title: string;
  href: string;
}

type Subset<K, T extends K> = T;
export type NbaStatus = Subset<DC_Domain_Models_NextBestAction_FeedbackStatus, 'Viewed' | 'Served' | 'Success'>;

export type MergedConfig = {
  routes: FlowRoute<FlowConsumerContextEnergy>[];
  actions: FlowActions<FlowConsumerContextEnergy>;
  initialContext: FlowConsumerContextEnergy;
  testVariable?: string;
};

export type TestConfigVariant = MergedConfig & {
  testKey: string;
  variant: string;
};
