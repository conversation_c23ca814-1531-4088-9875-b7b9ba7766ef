import React, { FC } from 'react';

import { screen, render } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { FlowProvider } from '@eneco/flows/src/utils';
import { TestAppProviders } from '@jest-tools/TestAppProviders';
import mockupData from '@mocks/sitecore/apps/flows/leadFlow/makeAppointmentStep';

import { MakeAppointmentStep } from './MakeAppointmentStep';
import { routes, actions, initialContext } from '../../LeadFlow.config';

const TestComponent: FC = () => {
  return (
    <TestAppProviders rendering={mockupData}>
      <FlowProvider routes={routes} actions={actions} initialContext={initialContext}>
        <MakeAppointmentStep />
      </FlowProvider>
    </TestAppProviders>
  );
};

Element.prototype.scrollIntoView = jest.fn();

describe('MakeAppointmentStep', () => {
  beforeEach(() => {
    render(<TestComponent />);
  });
  it('should render the MakeAppointmentStep correctly', async () => {
    expect(await screen.findByRole('heading', { name: /Maak een afspraak/i })).toBeVisible();
    expect(await screen.findByText(/Kies hier een dag en tijd die je goed uitkomen/i)).toBeVisible();
    expect(await screen.findByText(/Je wordt zo snel mogelijk gebeld/i)).toBeVisible();
    expect(await screen.findByText(/Bel me binnen een week/i)).toBeVisible();
    expect(await screen.findByText(/Laat meer opties zien/i)).toBeVisible();
    expect(await screen.findByText(/Donderdag 13 juli 2023/i)).toBeVisible();
  });

  it('should have the first item selected by default', async () => {
    const randomTimeSlotCard = await screen.findByLabelText(/Bel me binnen een week/i);
    expect(randomTimeSlotCard).toBeChecked();
  });

  it('should show all parts of the custom date picker correctly', async () => {
    const event = userEvent.setup();

    const customTimeSlotCard = await screen.findByLabelText(/Kies zelf een datum en tijd/i);
    await event.click(customTimeSlotCard);

    expect(await screen.findByText(/Kies een datum/i)).toBeVisible();

    const openCalendarButton = await screen.findByRole('button', { name: /Open kalender/i });
    await event.click(openCalendarButton);

    expect(await screen.findByText('Ma')).toBeVisible();
    expect(await screen.findByText('Di')).toBeVisible();
    expect(await screen.findByText('Wo')).toBeVisible();
    expect(await screen.findByText('Do')).toBeVisible();
    expect(await screen.findByText('Vr')).toBeVisible();
    expect(await screen.findByText('Za')).toBeVisible();
    expect(await screen.findByText('Zo')).toBeVisible();

    const dateButton = await screen.findByLabelText(/10 augustus 2023/i);
    await event.click(dateButton);

    expect(await screen.findByText(/Kies een tijd/i)).toBeVisible();
    expect(
      await screen.findByText(/Je wordt binnen deze tijden gebeld. Het gesprek zelf duurt ongeveer 20 minuten./i),
    ).toBeVisible();

    const volgendeButton = await screen.findByText(/Volgende/i);
    await event.click(volgendeButton);

    expect(await screen.findByText(/Je hebt geen keuze gemaakt/i)).toBeVisible();
  });
});
