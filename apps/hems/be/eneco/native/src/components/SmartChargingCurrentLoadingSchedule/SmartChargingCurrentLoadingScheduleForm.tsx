import { FC, PropsWithChildren, ReactNode } from 'react';

import { formatISO, add } from 'date-fns';
import { FieldErrors, UseFormRegister } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import { useTranslation } from '@i18n';
import { DC_Domain_Models_HEMS_ChargeMode } from '@monorepo-types/dc';
import NativeInputTimePicker from '@native-components/components/NativeInputTimePicker';
import { useContent } from '@sitecore/common';
import {
  SmartChargingCurrentLoadingScheduleRendering,
  NotificationField,
} from '@sitecore/types/SmartChargingCurrentLoadingSchedule';
import { Stack, Text, InputSelect, Box, Heading, NotificationBox } from '@sparky';
import { InfoIcon, PlugInIcon, BoostChargingIcon } from '@sparky/icons';

import { FormValues } from './SmartChargingCurrentLoadingSchedulePanel';
import { targetOfChargeLevelOptions } from '../../utils/constants';

export interface SmartChargingCurrentLoadingScheduleFormProps {
  departureDay: string;
  departureTime: string;
  targetSoc: number;
  setTargetSoc: (newState: number) => void;
  setDepartureDay: (newState: string) => void;
  setDepartureTime: (newState: string) => void;
  hasSuccess: boolean;
  notification: NotificationField;
  register: UseFormRegister<FormValues>;
  errors: FieldErrors<FormValues>;
  maxOemStateOfCharge: number | null | undefined;
  chargeMode: DC_Domain_Models_HEMS_ChargeMode | undefined;
}

const STATUS_MAP: Record<DC_Domain_Models_HEMS_ChargeMode, string> = {
  Smart: 'Slim laden',
  Boost: 'Boost',
  Unknown: '',
  Stopped: '',
};

const ICON_MAP: Record<DC_Domain_Models_HEMS_ChargeMode, ReactNode> = {
  Smart: <PlugInIcon color="iconElectricity" />,
  Boost: <BoostChargingIcon color="iconElectricity" />,
  Unknown: null,
  Stopped: null,
};

const SmartChargingCurrentLoadingScheduleForm: FC<PropsWithChildren<SmartChargingCurrentLoadingScheduleFormProps>> = ({
  departureTime,
  setDepartureTime,
  targetSoc,
  setTargetSoc,
  departureDay,
  setDepartureDay,
  hasSuccess,
  notification,
  register,
  errors,
  maxOemStateOfCharge,
  chargeMode,
}) => {
  const { fields } = useContent<SmartChargingCurrentLoadingScheduleRendering>();
  const { t } = useTranslation();

  const departureDateOptions = [
    { label: t('currentScheduleOverride.today'), value: formatISO(Date.now(), { representation: 'date' }) },
    {
      label: t('currentScheduleOverride.tomorrow'),
      value: formatISO(add(Date.now(), { days: 1 }), { representation: 'date' }),
    },
  ];

  return (
    <Box paddingX="2">
      <Stack gap="5">
        <Heading as="h4" size="S">
          {fields.pageTitle.value}
        </Heading>
        {chargeMode && STATUS_MAP[chargeMode]?.length > 0 && (
          <Box paddingBottom="3">
            <Stack direction="row" gap="2">
              {ICON_MAP[chargeMode]}
              <Text>
                {STATUS_MAP[chargeMode]} {t('status')}
              </Text>
            </Stack>
          </Box>
        )}
        {hasSuccess && (
          <NotificationBox
            isAlert
            title={notification.value.title}
            text={<RichText html={notification.value.content} />}
            variant={notification.value.variant}
          />
        )}
        {chargeMode !== 'Boost' && (
          <>
            <InputSelect
              placeholder=""
              value={departureDay}
              options={departureDateOptions}
              label={fields.dateOfDepartureLabel.value}
              name="departure-date"
              onChange={event => setDepartureDay(event.target.value)}
            />
            <NativeInputTimePicker
              {...register('formDepartureTime')}
              id="departure-time"
              label={fields.timeOfDepartureLabel.value}
              name="departure-time"
              value={departureTime}
              onChange={event => {
                setDepartureTime(event.target.value);
              }}
              error={errors?.formDepartureTime?.message && fields.errorDepartureTimeText.value}
            />
          </>
        )}
        <InputSelect
          placeholder=""
          value={targetSoc}
          options={targetOfChargeLevelOptions}
          label={fields.targetOfChargeLevelLabel.value}
          name="target-of-charge-percentage"
          onChange={event => setTargetSoc(Number(event.target.value))}
        />
        {maxOemStateOfCharge && (
          <Stack direction="row" gap="2" alignX="justify" alignY="start">
            <InfoIcon color="iconSecondary" />
            <Text>{fields.maxChargeLevelRemarkText.value}</Text>
            <Box backgroundColor="feedbackBackgroundInfo" borderRadius="m" padding="3">
              <Text data-testid="max-oem-percentage">{maxOemStateOfCharge}%</Text>
            </Box>
          </Stack>
        )}
      </Stack>
    </Box>
  );
};

export default SmartChargingCurrentLoadingScheduleForm;
