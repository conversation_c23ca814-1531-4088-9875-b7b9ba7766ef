import { FC } from 'react';

import ComponentError from '@components/ui/ComponentError/ComponentError';
import { useDocumentsGetCustomerContracts, useProductsGetProductsForAccountV2 } from '@dc/hooks';
import { useRouter } from '@dxp-next';
import { useLinkComponent } from '@link';
import { useContent, useLayoutData } from '@sitecore/common';
import { ProductsNavigationRendering } from '@sitecore/types/ProductsNavigation';
import { NavLink, Skeleton, Stack } from '@sparky';

const ProductsNavigation: FC = () => {
  const { context } = useLayoutData();
  const Link = useLinkComponent();
  const { activePath } = useRouter();
  const { fields } = useContent<ProductsNavigationRendering>();

  const { contractsLink, allProductsLinkText } = fields ?? {};
  const { data, isValidating, error } = useProductsGetProductsForAccountV2({ includeProductRates: true });

  const { data: contractsData } = useDocumentsGetCustomerContracts({
    includeActiveContracts: true,
    includePastContracts: true,
  });

  if (isValidating) return <Skeleton height={200} />;
  if (error || !data) return <ComponentError />;

  const basePath = context.basePath || '';

  const items = data.products?.map(product => {
    return {
      id: product.id,
      name: product.type?.description,
      path: `${basePath}${product.type?.name}/${product.agreementId}`,
    };
  });

  const hasContracts = contractsData?.contracts?.some(contract => !!contract.contractId) || false;

  return (
    <div role="navigation">
      <Stack as="ul" gap={{ initial: '4', md: '2' }}>
        <Stack.Item as="li">
          <Link href={basePath} linkType="internal" currentMatch="exact">
            <NavLink variant="secondary">{allProductsLinkText?.value}</NavLink>
          </Link>
        </Stack.Item>

        {hasContracts && !!contractsLink?.value?.text && (
          <Stack.Item as="li">
            <Link linkValue={contractsLink.value} linkType="internal">
              <NavLink isCurrent={activePath.includes(contractsLink?.value?.href.split('?')[0])} variant="secondary">
                {contractsLink?.value?.text}
              </NavLink>
            </Link>
          </Stack.Item>
        )}

        {items?.map(item => (
          <Stack.Item as="li" key={item.id}>
            <Link href={item.path} linkType="internal">
              <NavLink variant="secondary">{item.name}</NavLink>
            </Link>
          </Stack.Item>
        ))}
      </Stack>
    </div>
  );
};

export default ProductsNavigation;
