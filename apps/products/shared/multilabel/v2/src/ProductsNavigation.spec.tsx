import React from 'react';

import { render, screen } from '@testing-library/react';

import { TestAppProviders } from '@jest-tools/TestAppProviders';
import { data as productsGetProductsForAccountV2Response } from '@mocks/dc/customers/products/defaultNL';
import productsNavigationMock from '@mocks/sitecore/apps/products/nl/multilabel/v2/productsnavigation';

import ProductsNavigation from './ProductsNavigation';

const basePath = '/mijn-eneco/producten/';
describe('ProductsNavigation', () => {
  it('should render the component', async () => {
    render(
      <TestAppProviders rendering={productsNavigationMock} customerId="2029587" basePath={basePath}>
        <ProductsNavigation />
      </TestAppProviders>,
    );

    const productsResponse = productsGetProductsForAccountV2Response();
    const allProductsLink = await screen.findByRole('link', {
      name: productsNavigationMock.fields.allProductsLinkText.value,
    });

    const documentsLink = await screen.findByRole('link', {
      name: productsNavigationMock.fields.contractsLink.value.text,
    });

    expect(allProductsLink).toBeVisible();
    expect(allProductsLink).toHaveAttribute('href', basePath);

    expect(documentsLink).toBeVisible();
    expect(documentsLink).toHaveAttribute(
      'href',
      productsNavigationMock.fields.contractsLink.value.href +
        productsNavigationMock.fields.contractsLink?.value?.querystring,
    );

    const productsWithDescription = productsResponse.data?.products?.filter(
      product => !!product.type?.description?.length,
    );

    productsWithDescription?.forEach(product => {
      const productElement = screen.getByRole('link', { name: product.type?.description ?? '' });
      expect(productElement).toBeVisible();
      expect(productElement).toHaveAttribute('href', `${basePath}${product.type?.name}/${product.agreementId}`);
    });
  });
});
