import type { DC_Domain_Models_P1_Interval } from './DC_Domain_Models_P1_Interval';
import type { DC_Domain_Models_P1_UsageEnergyType } from './DC_Domain_Models_P1_UsageEnergyType';
import type { P1DongleUsage_P1DongleUsageFlowEntryResponseModel } from './P1DongleUsage_P1DongleUsageFlowEntryResponseModel';
export interface P1DongleUsage_P1DongleUsageFlowResponseModel {
  interval: DC_Domain_Models_P1_Interval;
  energyType: DC_Domain_Models_P1_UsageEnergyType;
  from: string;
  to: string;
  usageEntries?: Array<P1DongleUsage_P1DongleUsageFlowEntryResponseModel> | null;
}
