# The pipeline buildNumber $(Date:yyyyMMdd)_$(Rev:r) is overridden in Build_Preparations job

parameters:
  - name: acrHostname
    type: string
    displayName: Name of the Azure Container Registry (ACR)
    default: dxpinfrasharedacrwebp.azurecr.io
  - name: agentPool
    type: string
    displayName: Agent pool to run the pipeline on
    default: mdp-dxp-infra-cicd-p
    values:
      - Azure Pipelines
      - Self-hosted-DXP-Shared-Linux-22-04
      - mdp-dxp-infra-cicd-p
  - name: cleanDirectories
    displayName: Remove node_modules and dist directories
    type: boolean
    default: false
  - name: deployNonMainBranchBuild
    displayName: Deploy to all environments (when non-main branch)
    type: boolean
    default: false
  - name: forceContainerBuilds
    displayName: Build all frontend containers (also when not affected)
    type: boolean
    default: false
  - name: skipContainerBuilds
    displayName: Don't build (and don't deploy) new frontend containers
    type: boolean
    default: false

# Trigger for pushes
trigger:
  batch: true # Batch pipeline runs to avoid parallel deployments
  branches:
    include:
      - main

# Do not trigger on PRs
pr: none

# Run this pipeline on a schedule each Tuesday and Thursday at 10:00
# Takes 2 hours timezone diff into account, since all cron schedules are in UTC and do not support daylight saving time.
schedules:
  - cron: "0 9 * * 2,4"
    always: true
    displayName: Auto Deploy Production
    branches:
      include:
        - main

variables:
  - group: dxp-web-initial
  - name: dockerfilePath
    value: "$(Build.SourcesDirectory)/cicd/Dockerfile"
  - name: containerTag
    value: $(Build.BuildNumber)
  - name: isMain
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  - name: isPullRequest
    value: $[eq(variables['Build.Reason'], 'PullRequest')]
resources:
  repositories:
    - repository: eneco-dxp-infra # The name used to reference this repository in the checkout step
      type: github
      endpoint: eneco-online
      name: eneco-online/eneco-dxp-infra
      ref: master
    - repository: dora-metrics-collector
      type: git
      name: 668ed616-964e-4b44-a788-0c5b20b23f63/_git/722febd9-c1a7-457c-9c13-7a2abbcc6282
      # https://dev.azure.com/enecomanagedcloud/DTS%20Enabling%20Technology%20-%20System%20Team/_git/dora-metrics-collector
      ref: refs/heads/main

stages:
  - stage: Prepare
    displayName: Prepare
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    variables:
      - template: variables/development.yaml
    jobs:
      - job: Build_Preparations
        displayName: Determine Affected Projects
        steps:
          - template: templates/use-node-tools.yaml

          # The pipeline produces docker images tagged with the build number. Images are stored on an Azure Container Registry (ACR). Both the pipeline runs and ACR have a static retention duration.
          # In the case we need to recreate a running docker without having the history of both aspects, we can only do so when we know the commit hash that the running image is based on.
          # Therefor we include it in the build number so the docker image is tagged accordingly.
          #
          # The buildNumber cannot be set directly in the yaml 'name' property when it needs to contain a scripted value,
          # so we update it in the first task being executed with a logging command:
          # https://docs.microsoft.com/en-us/azure/devops/pipelines/scripts/logging-commands?view=azure-devops&tabs=bash#updatebuildnumber-override-the-automatically-generated-build-number
          - script: |
              SHORT_COMMIT_SHA=$(git rev-parse --short=7 HEAD)
              echo "Full SHA $BUILD_SOURCEVERSION"
              echo "Short SHA $SHORT_COMMIT_SHA"
              echo "##vso[build.updatebuildnumber]${BUILD_BUILDNUMBER}_${SHORT_COMMIT_SHA}"
            displayName: Update BuildNumber with short commit sha

          - script: rm -rf $(System.DefaultWorkingDirectory)/node_modules $(System.DefaultWorkingDirectory)/dist
            condition: ${{ parameters.cleanDirectories }}
            displayName: Remove node_modules and dist directories

          - script: |
              env | sort
              ls $(System.DefaultWorkingDirectory) --recursive --no-group -x --literal -1
            condition: eq(variables['System.debug'], 'true') # Tick "Enable system diagnostics" to activate
            displayName: Print debug info

          # Required for `az pipelines runs`
          - bash: |
              az config set extension.use_dynamic_install=yes_without_prompt
              az devops configure --defaults organization=$(System.TeamFoundationCollectionUri) project="$(System.TeamProject)"
            displayName: Set default Azure DevOps organization and project

          - task: Npm@1
            displayName: Install dependencies
            inputs:
              command: custom
              # Not `npm ci` intentionally (majority of pipeline runs on self-hosted agents has node_module populated)
              customCommand: "install --ignore-scripts"

          # prettier-ignore
          - script: >
              node "$(Build.SourcesDirectory)/cicd/pipelines/scripts/is-affected.js"
              --pipelineId $(System.DefinitionId)
              --app be-eneco-insights
              --app nl-eneco-main
              --app nl-eneco-selfservice
              --app nl-eneco-grootzakelijk
              --app nl-eneco-zakelijk
              --app gb-eneco-main
              --app be-eneco-main
              --app nl-eneco-mkb
              --app nl-oxxio-selfservice
              --app nl-oxxio-main
              --app nl-woonenergie-selfservice
              --app nl-woonenergie-main
              --app nl-internal-agentportal
              --lib docs,nl-internal-docs
              --lib sparky,nl-internal-sparky
              --lib eneco-flows,nl-internal-storybook
              --lib nl-internal-storybook
            name: AffectedApp
            displayName: Determine affected containers
            condition: eq('${{ parameters.forceContainerBuilds }}', 'false')
            env:
              AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)

  - stage: Build
    dependsOn: Prepare
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    variables:
      - template: variables/development.yaml
    jobs:
      - job: Publish_Statics
        displayName: Publish Statics
        steps:
          # In the FE monorepo "static" assets are stored. Published to Akamai (CDN).
          # We collect and publish these statics so they can be deployed to the target location deploy-time.
          - task: PublishPipelineArtifact@1
            displayName: Statics artifact (publish)
            inputs:
              targetPath: $(Build.SourcesDirectory)/libs/sparky/static
              artifactName: statics
              artifactType: pipeline

      # NL Eneco
      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: nl-eneco-main

      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: nl-eneco-grootzakelijk

      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: nl-eneco-zakelijk

      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: nl-eneco-mkb

      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: nl-eneco-selfservice

      # UK Eneco
      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: gb-eneco-main

        # BE Eneco
      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: be-eneco-main

      # NL Oxxio
      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: nl-oxxio-selfservice

      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: nl-oxxio-main

      # NL Woonenergie
      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: nl-woonenergie-selfservice

      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: nl-woonenergie-main

      # NL Internal
      - template: templates/build-frontend-container.yaml
        parameters:
          containerName: nl-internal-docs
          dockerfilePath: "$(Build.SourcesDirectory)/cicd/docker/docs/Dockerfile"
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}

      - template: templates/build-frontend-container.yaml
        parameters:
          containerName: nl-internal-sparky
          dockerfilePath: "$(Build.SourcesDirectory)/cicd/docker/sparky/Dockerfile"
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}

      - template: templates/build-frontend-container.yaml
        parameters:
          containerName: nl-internal-storybook
          dockerfilePath: "$(Build.SourcesDirectory)/cicd/docker/storybooks/Dockerfile"
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}

      - template: templates/build-frontend-container.yaml
        parameters:
          acrHostname: ${{ parameters.acrHostname }}
          forceContainerBuilds: ${{ parameters.forceContainerBuilds }}
          skipContainerBuilds: ${{ parameters.skipContainerBuilds }}
          containerName: nl-internal-agentportal

  # Deployments only from main branch. Use the `deployNonMainBranchBuild` parameter to deploy from another branch.
  - stage: Development
    dependsOn: Build
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    condition: and(succeeded('Build'), or(eq(variables.isMain, True), ${{ parameters.deployNonMainBranchBuild}}) )
    variables:
      - template: variables/development.yaml
      - name: tag # Referenced in Kubernetes manifest files (and token replacements)
        ${{ if not(parameters.skipContainerBuilds) }}: # use the tag from the Docker builds from build stage
          value: ${{ variables.containerTag }}
        ${{ else }}:
          value: latest
    jobs:
      - job: Deploy_Statics
        steps:
          - download: current
            artifact: statics

          - template: templates/upload-to-storage-account-blob.yaml
            parameters:
              storageAccountName: ${{ variables.storageAccountName }}
              clearFirst: false
              blobContainer: "`$web"
              sourcePath: $(Pipeline.Workspace)/statics
              targetPath: statics

      - template: templates/deploy-shared-components.yaml
        parameters:
          environment: development

      # Deploy Containers
      - template: templates/deploy-frontend-containers.yaml
        parameters:
          environment: development
          containers:
            - nl-eneco-main
            - nl-eneco-grootzakelijk
            - nl-eneco-zakelijk
            - nl-eneco-mkb
            - nl-eneco-selfservice
            - gb-eneco-main
            - be-eneco-main
            - nl-oxxio-selfservice
            - nl-oxxio-main
            - nl-woonenergie-selfservice
            - nl-woonenergie-main
            - nl-internal-agentportal

  - stage: Test
    dependsOn: Build
    condition: and(succeeded('Build'), or(eq(variables.isMain, True), ${{ parameters.deployNonMainBranchBuild }}) )
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    variables:
      - template: variables/test.yaml
      - name: tag # Referenced in Kubernetes manifest files (and token replacements)
        value: ${{ variables.containerTag }}
    jobs:
      - job: Deploy_Statics
        steps:
          - download: current
            artifact: statics

          - template: templates/upload-to-storage-account-blob.yaml
            parameters:
              azureServiceConnectionContainerRegistry: ${{ variables.azureServiceConnectionContainerRegistry }}
              storageAccountName: ${{ variables.storageAccountName }}
              clearFirst: false
              blobContainer: "`$web"
              sourcePath: $(Pipeline.Workspace)/statics
              targetPath: statics

      - template: templates/deploy-shared-components.yaml
        parameters:
          environment: test

      - template: templates/deploy-frontend-containers.yaml
        parameters:
          environment: test
          containers:
            - nl-eneco-main
            - nl-eneco-grootzakelijk
            - nl-eneco-zakelijk
            - nl-eneco-mkb
            - nl-eneco-selfservice
            - gb-eneco-main
            - be-eneco-main
            - nl-oxxio-selfservice
            - nl-oxxio-main
            - nl-woonenergie-selfservice
            - nl-woonenergie-main
            - nl-internal-agentportal

  - stage: release_npm_packages
    displayName: Release NPM packages
    dependsOn: ["Build", "Test"]
    condition: and(succeeded('Test'), or(eq(variables.isMain, True), ${{ parameters.deployNonMainBranchBuild }}) )
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    variables:
      - template: variables/production.yaml
    jobs:
      - job: release_npm_packages
        displayName: Release NPM packages
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: ${{ parameters.cleanDirectories }}
          - task: AzureKeyVault@2
            inputs:
              azureSubscription: ${{ variables.azureServiceConnectionContainerRegistry }}
              KeyVaultName: ${{ variables.keyVault }}
          - script: npx nx run-many --target=release-npm-package --parallel=false --all
            displayName: Releasing NPM packages
            env:
              GITHUB_TOKEN: $(NPM-PUBLISH-TOKEN)
              NPM_TOKEN: $(NPM-PUBLISH-TOKEN)

  - stage: Acceptance
    dependsOn: ["Build", "Test"]
    condition: and(succeeded('Test'), or(eq(variables.isMain, True), ${{ parameters.deployNonMainBranchBuild }}) )
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    variables:
      - template: variables/acceptance.yaml
      - name: tag # Referenced in Kubernetes manifest files (and token replacements)
        value: ${{ variables.containerTag }}
    jobs:
      - job: Deploy_Statics
        steps:
          - download: current
            artifact: statics

          - template: templates/upload-to-storage-account-blob.yaml
            parameters:
              azureServiceConnectionContainerRegistry: ${{ variables.azureServiceConnectionContainerRegistry }}
              storageAccountName: ${{ variables.storageAccountName }}
              clearFirst: false
              blobContainer: "`$web"
              sourcePath: $(Pipeline.Workspace)/statics
              targetPath: statics

      - template: templates/deploy-shared-components.yaml
        parameters:
          environment: acceptance

      - template: templates/deploy-frontend-containers.yaml
        parameters:
          environment: acceptance
          containers:
            - nl-eneco-main
            - nl-eneco-grootzakelijk
            - nl-eneco-zakelijk
            - nl-eneco-mkb
            - nl-eneco-selfservice
            - gb-eneco-main
            - be-eneco-main
            - nl-oxxio-selfservice
            - nl-oxxio-main
            - nl-woonenergie-selfservice
            - nl-woonenergie-main

            # Internal containers
            - nl-internal-docs
            - nl-internal-sparky
            - nl-internal-storybook
            - nl-internal-agentportal

  - stage: Production
    dependsOn: ["Build", "Acceptance"]
    condition: and(succeeded('Acceptance'), or(eq(variables.isMain, True), ${{ parameters.deployNonMainBranchBuild }}) )
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    variables:
      - template: variables/production.yaml
      - name: tag # Referenced in Kubernetes manifest files (and token replacements)
        value: ${{ variables.containerTag }}
    jobs:
      - job: Deploy_Statics
        steps:
          - download: current
            artifact: statics

          - ${{ if eq(variables['Build.CronSchedule.DisplayName'], 'Auto Deploy Production') }}:
              # Post Slack notification
              - task: PowerShell@2
                displayName: "Slack Notification"
                env:
                  # In YAML, you must explicitly map System.AccessToken into the pipeline using a variable.
                  # https://docs.microsoft.com/en-us/azure/devops/pipelines/build/variables?view=azure-devops&tabs=yaml#systemaccesstoken
                  FUNCTION_APP_KEY: $(changelog-for-web-containers-key)
                  SLACK_API_TOKEN: $(slack_api_token)
                inputs:
                  targetType: "FilePath"
                  filePath: "$(Build.SourcesDirectory)/cicd/pipelines/scripts/prod_release_start_slack_message.ps1"
                  failOnStderr: true

          - template: templates/upload-to-storage-account-blob.yaml
            parameters:
              azureServiceConnectionContainerRegistry: ${{ variables.azureServiceConnectionContainerRegistry }}
              storageAccountName: ${{ variables.storageAccountName }}
              clearFirst: false
              blobContainer: "`$web"
              sourcePath: $(Pipeline.Workspace)/statics
              targetPath: statics

      - template: templates/deploy-shared-components.yaml
        parameters:
          environment: production

      - template: templates/deploy-frontend-containers.yaml
        parameters:
          environment: production
          containers:
            - nl-eneco-main
            - nl-eneco-grootzakelijk
            - nl-eneco-zakelijk
            - nl-eneco-mkb
            - nl-eneco-selfservice
            - gb-eneco-main
            - be-eneco-main
            - nl-oxxio-selfservice
            - nl-oxxio-main
            - nl-woonenergie-selfservice
            - nl-woonenergie-main

            # Internal containers
            - nl-internal-agentportal

  - stage: ObtainDoraMetrics
    displayName: "Obtain DORA Metrics"
    dependsOn: ["Build", "Production"]
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    jobs:
      - template: dora-metrics-template.yml@dora-metrics-collector
        parameters:
          app_name: "dxp-web"
