parameters:
  - name: agentPool
    type: string
    displayName: Agent pool to run the pipeline on
    default: mdp-dxp-infra-cicd-p
    values:
      - Azure Pipelines
      - Self-hosted-DXP-Shared-Linux
      - Self-hosted-DXP-Shared-Linux-22-04
      - mdp-dxp-infra-cicd-p

# Do not trigger for pushes on branch
trigger: none

# Do not trigger on (creation of or pushes to) Pull Requests
pr: none

# Schedule this pipeline to run night at 03:00 on weekdays (Mon-Fri)
schedules:
  - cron: "0 3 * * 1-5"
    always: false
    displayName: Dependency Track Nightly (3AM) on Mon-Fri
    branches:
      include:
        - main

stages:
  - stage: dependency_track
    displayName: Dependency Track
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    jobs:
      - job: 'DependencyTrackNPM'
        displayName: 'Dependency Track NPM'
        variables:
          - group: dependency-track
        steps:
          # NPM Install
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: true
          - script:
              npm install --global @cyclonedx/cyclonedx-npm
            displayName: 'Install CycloneDX'
          - script:
              cyclonedx-npm --output-format xml --output-file bom.xml
            displayName: 'Generate CycloneDX SBOM'
          - script: |
              curl -X POST "$(DEPENDENCYTRACK_URL)/api/v1/bom" \
                -H "Content-Type: multipart/form-data" \
                -H "X-Api-Key: $(DEPENDENCYTRACK_API_KEY)" \
                -F "project=$(DEPENDENCYTRACK_FRONTEND_PROJECT_ID)" \
                -F "bom=@bom.xml"
            displayName: 'Upload BOM to Depedency Track'
