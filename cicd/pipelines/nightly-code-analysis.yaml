parameters:
  - name: agentPool
    type: string
    displayName: Agent pool to run the pipeline on
    default: mdp-dxp-infra-cicd-p
    values:
      - Azure Pipelines
      - Self-hosted-DXP-Shared-Linux-22-04
      - mdp-dxp-infra-cicd-p

# Do not trigger on commits
trigger: none

# Do not trigger on PRs
pr: none

schedules:
  - cron: "0 7 * * *"
    displayName: Nightly Frontend DXP code analysis
    branches:
      include:
        - main
    always: true

stages:
  - stage: code_analysis
    displayName: Code Analysis
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    jobs:
      - job: Unit_tests
        displayName: Unit tests
        # timeoutInMinutes: 120 - can be used to extend the default 60 min timeout
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml

          - script:
              npx nx run-many --target=test --configuration=ci --skip-nx-cache --base=origin/main
              --exclude=nl-eneco-sandbox --parallel=2
            displayName: "Run Unit Tests"

      - job: SonarCloud_analysis
        displayName: SonarCloud Analysis
        steps:
          - template: templates/prepare-sonarcloud.yaml

          - task: SonarCloudAnalyze@3
            displayName: "Run SonarCloud analysis"

          - task: SonarCloudPublish@3
            displayName: "Publish results on build summary"
            inputs:
              pollingTimeoutSec: "300"
