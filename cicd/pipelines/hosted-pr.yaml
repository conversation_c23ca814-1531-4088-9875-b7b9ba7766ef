# The pipeline buildNumber $(Date:yyyyMMdd)_$(Rev:r) is overridden in Build_Preparations job

parameters:
  - name: acrHostname
    type: string
    displayName: Name of the Azure Container Registry (ACR)
    default: dxpinfrasharedacrwebp.azurecr.io
  - name: agentPool
    type: string
    displayName: Agent pool to run the pipeline on
    default: mdp-dxp-infra-cicd-p
    values:
      - Azure Pipelines
      - Self-hosted-DXP-Shared-Linux-22-04
      - mdp-dxp-infra-cicd-p
  - name: nodeVersion
    displayName: Node version (default 22.6.0)
    type: string
    default: 22.6.0

  - name: pullRequestId
    displayName: Github Pull Request ID (also select the branch above!)
    type: string
    default: ""

  - name: container-nl-eneco-main
    displayName: Build & deploy nl-eneco-main
    type: boolean
    default: false
  - name: container-nl-eneco-grootzakelijk
    displayName: Build & deploy nl-eneco-grootzakelijk
    type: boolean
    default: false
  - name: container-nl-eneco-zakelijk
    displayName: Build & deploy nl-eneco-zakelijk
    type: boolean
    default: false
  - name: container-nl-eneco-mkb
    displayName: Build & deploy nl-eneco-mkb
    type: boolean
    default: false
  - name: container-nl-eneco-selfservice
    displayName: Build & deploy nl-eneco-selfservice
    type: boolean
    default: false
  - name: container-gb-eneco-main
    displayName: Build & deploy gb-eneco-main
    type: boolean
    default: false
  - name: container-nl-oxxio-main
    displayName: Build & deploy nl-oxxio-main
    type: boolean
    default: false
  - name: container-nl-oxxio-selfservice
    displayName: Build & deploy nl-oxxio-selfservice
    type: boolean
    default: false
  - name: container-nl-woonenergie-main
    displayName: Build & deploy nl-woonenergie-main
    type: boolean
    default: false
  - name: container-nl-woonenergie-selfservice
    displayName: Build & deploy nl-woonenergie-selfservice
    type: boolean
    default: false
  - name: container-nl-internal-agentportal
    displayName: Build & deploy nl-internal-agentportal
    type: boolean
    default: false
  - name: container-be-eneco-main
    displayName: Build & deploy be-eneco-main
    type: boolean
    default: false

# This pipeline should only run with a manual trigger
# Do not trigger on branch pushes
trigger: none
# Do not trigger on PRs
pr: none

variables:
  - group: dxp-web-initial
  - name: dockerfilePath
    value: "$(Build.SourcesDirectory)/cicd/Dockerfile"
  - name: containerTag
    value: $(Build.BuildNumber)
resources:
  repositories:
    - repository: eneco-dxp-infra # The name used to reference this repository in the checkout step
      type: github
      endpoint: eneco-online
      name: eneco-online/eneco-dxp-infra
      ref: master

stages:
  - stage: Prepare
    displayName: Prepare
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    variables:
      - template: variables/development.yaml
    jobs:
      - job: Build_Preparations
        displayName: Build Preparations
        steps:
          # The pipeline produces docker images tagged with the build number. Images are stored on an Azure Container Registry (ACR). Both the pipeline runs and ACR have a static retention duration.
          # In the case we need to recreate a running docker without having the history of both aspects, we can only do so when we know the commit hash that the running image is based on.
          # Therefor we include it in the build number so the docker image is tagged accordingly.
          #
          # The buildNumber cannot be set directly in the yaml 'name' property when it needs to contain a scripted value,
          # so we update it in the first task being executed with a logging command:
          # https://docs.microsoft.com/en-us/azure/devops/pipelines/scripts/logging-commands?view=azure-devops&tabs=bash#updatebuildnumber-override-the-automatically-generated-build-number
          - script: |
              SHORT_COMMIT_SHA=$(git rev-parse --short=7 HEAD)
              echo "Full SHA $BUILD_SOURCEVERSION"
              echo "Short SHA $SHORT_COMMIT_SHA"
              echo "##vso[build.updatebuildnumber]${BUILD_BUILDNUMBER}_${SHORT_COMMIT_SHA}"
            displayName: Update BuildNumber with short commit sha

  - stage: Build
    dependsOn: Prepare
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    variables:
      - template: variables/development.yaml
    jobs:
      - job: Publish_Statics
        displayName: Publish Statics
        steps:
          # In the FE monorepo "static" assets are stored. Published to Akamai (CDN).
          # We collect and publish these statics so they can be deployed to the target location deploy-time.
          - task: PublishPipelineArtifact@1
            displayName: Statics artifact (publish)
            inputs:
              targetPath: $(Build.SourcesDirectory)/libs/sparky/static
              artifactName: statics
              artifactType: pipeline

      - ${{ each param in parameters }}:
          - ${{ if and(startsWith(param.key, 'container-'), eq(param.value, true)) }}:
              - template: templates/build-frontend-container.yaml
                parameters:
                  containerName: ${{ replace(param.key, 'container-', '') }}
                  acrHostname: ${{ parameters.acrHostname }}
                  forceContainerBuilds: true
                  hostedPR: pr-${{ parameters.pullRequestId }}

  - stage: Deploy
    dependsOn: Build
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    condition: succeeded('Build')
    variables:
      - template: variables/development.yaml
      - name: tag # Referenced in Kubernetes manifest files (and token replacements)
        value: ${{ variables.containerTag }}
    jobs:
      - job: Deploy_Statics
        steps:
          - download: current
            artifact: statics

          - template: templates/upload-to-storage-account-blob.yaml
            parameters:
              storageAccountName: ${{ variables.storageAccountName }}
              clearFirst: false
              blobContainer: "`$web"
              sourcePath: $(Pipeline.Workspace)/statics
              targetPath: statics

      - template: templates/deploy-shared-components.yaml
        parameters:
          environment: development

      - ${{ each param in parameters }}:
          - ${{ if and(startsWith(param.key, 'container-'), eq(param.value, true)) }}:
              - template: templates/deploy-frontend-pr-container.yaml
                parameters:
                  containerName: ${{ replace(param.key, 'container-', '') }}
                  dependsOn: Deploy_Shared_Components_development
                  pullRequestId: ${{ parameters.pullRequestId }}
