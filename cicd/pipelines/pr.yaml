parameters:
  - name: agentPool
    type: string
    displayName: Agent pool to run the pipeline on
    default: mdp-dxp-infra-cicd-p
    values:
      - Azure Pipelines
      - Self-hosted-DXP-Shared-Linux-22-04
      - mdp-dxp-infra-cicd-p
  - name: cleanDirectories
    displayName: Remove dist directory
    type: boolean
    default: false
  - name: validateOnlyAffected
    displayName: Validate only the affected Nx projects
    type: boolean
    default: true

# Trigger for pushes
trigger: none

# Trigger on creation and/or updates on Pull Requests
pr:
  autoCancel: true
  drafts: true
  branches:
    include:
      - main

stages:
  - stage: Validate_Affected_Nx_Projects
    displayName: Validate Affected Nx Projects
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    variables:
      - name: command
        ${{ if eq(parameters.validateOnlyAffected, true) }}:
          value: affected
        ${{ else }}:
          value: run-many
      - name: targetBranch
        value: origin/${{ coalesce( variables['System.PullRequest.TargetBranch'], 'main') }}
      - name: excludedProjects
        value: nl-eneco-sandbox
      - name: exportableProjects
        value: be-eneco-insights

    jobs:
      - job: TypeCheck_Affected_Nx_Projects
        displayName: Type-check Affected Nx Projects
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: ${{ parameters.cleanDirectories }}
          # The Nx `tsc` target only type-checks specs and mocks (the `build` target implicitly type-checks source code)
          - script:
              npx nx $(command) --target=tsc --skip-nx-cache --base=$(targetBranch) --exclude=$(excludedProjects)
              --parallel=2
            displayName: Type-check affected projects (specs and mocks)

      - job: Lint_Affected_Nx_Projects
        displayName: Lint Affected Nx Projects
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: ${{ parameters.cleanDirectories }}
          - script:
              npx nx $(command) --target=lint --skip-nx-cache --base=$(targetBranch) --exclude=$(excludedProjects)
              --parallel=2
            displayName: Lint affected projects

      - job: Test_Affected_Nx_Projects
        displayName: Test Affected Nx Projects
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: ${{ parameters.cleanDirectories }}
          - script:
              npx nx $(command) --target=test --configuration=ci --skip-nx-cache --base=$(targetBranch)
              --exclude=$(excludedProjects) --parallel=2
            displayName: Test affected projects
      - job: SonarCloud_Analysis
        displayName: SonarCloud Analysis
        steps:
          - template: templates/prepare-sonarcloud.yaml

          - task: SonarCloudAnalyze@3
            displayName: "Run SonarCloud analysis"

          - task: SonarCloudPublish@3
            displayName: "Publish results on build summary"
            inputs:
              pollingTimeoutSec: "300"

      - job: Visually_Test_Affected_Nx_Projects
        displayName: Visually Test Affected Nx Projects
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: ${{ parameters.cleanDirectories }}
          - script:
              npx nx $(command) --target=test-visual --skip-nx-cache --base=$(targetBranch)
              --exclude=$(excludedProjects) --parallel=2
            displayName: Visually test affected projects

          - task: CopyFiles@2
            condition: failed()
            displayName: "Copying failed visual regression screenshots"
            inputs:
              contents: "e2e/**/cypress-visual-screenshots/**"
              targetFolder: "$(Build.ArtifactStagingDirectory)"

          - task: PublishBuildArtifacts@1
            condition: failed()
            displayName: Publishing failed visual regression screenshots
            inputs:
              PathtoPublish: "$(Build.ArtifactStagingDirectory)"
              ArtifactName: "VisualRegressionScreenshots"

      - job: Build_Affected_Containers
        displayName: Build Affected Nx Projects
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: ${{ parameters.cleanDirectories }}
          # The Nx `build` target runs `next build`, which implicitly type-checks source code using tsc
          - script: |
              npx nx $(command) --target=build --skip-nx-cache --configuration=production --base=$(targetBranch) --exclude=$(excludedProjects),$(exportableProjects) --parallel=2
            displayName: Build affected containers

      - job: Export_Affected_Containers
        displayName: Export Affected Nx Projects
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: ${{ parameters.cleanDirectories }}
          # The Nx `export` target also runs the `build` target
          - script: |
              npx nx $(command) --target=export --configuration=production --base=$(targetBranch) --exclude=$(excludedProjects) --parallel=2
            displayName: Export affected containers

      - job: Build_Affected_Storybooks
        displayName: Build Affected Storybooks
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: ${{ parameters.cleanDirectories }}
          - script: |
              npx nx $(command) --target=build-storybook --skip-nx-cache --configuration=production --base=$(targetBranch) --exclude=$(excludedProjects) --parallel=1
            displayName: Build affected Storybooks

      - job: Verify_Dictionaries
        displayName: Verify Dictionaries
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: ${{ parameters.cleanDirectories }}
          - script: npm run verify:dictionaries
            displayName: Verify all dictionaries

      - job: Lint_Files_Dependencies_Exports
        displayName: Find unused files
        steps:
          - template: templates/use-node-tools.yaml
          - template: templates/prepare-for-run.yaml
            parameters:
              cleanDirectories: ${{ parameters.cleanDirectories }}
          - script: npx knip -c config/knip/knip.jsonc --include files --reporter codeowners --no-exit-code
            displayName: Find unused files
