# This pipeline is triggered through Github Actions in the eneco-dxp-frontend repository
# Github Actions passes the variable pullRequestId which is used to find a ADO pipeline run which deployed a container
# with the same PR Id and then removes the deployment resources from the development k8s cluster

parameters:
  - name: agentPool
    type: string
    displayName: Agent pool to run the pipeline on
    default: mdp-dxp-infra-cicd-p
    values:
      - Azure Pipelines
      - Self-hosted-DXP-Shared-Linux-22-04
      - mdp-dxp-infra-cicd-p

  - name: frontendRepoBranch
    type: string
    displayName: Branch of the frontend repository
    default: main

# Do not trigger on branch pushes
trigger: none

# Do not trigger on PRs
pr: none

# Schedule this pipeline to run night at 04:00 on weekdays (Mon-Fri)
schedules:
  - cron: "0 4 * * 1-5"
    always: false
    displayName: Cleanup Inactive Hosted PRs
    branches:
      include:
        - main

resources:
  repositories:
    - repository: eneco-dxp-frontend # The name used to reference this repository in the checkout step
      type: github
      endpoint: eneco-online
      name: eneco-online/eneco-dxp-frontend
      ref: ${{ parameters.frontendRepoBranch }}
    - repository: eneco-dxp-infra # The name used to reference this repository in the checkout step
      type: github
      endpoint: eneco-online
      name: eneco-online/eneco-dxp-infra
      ref: master

stages:
  - stage: Cleanup
    displayName: Cleanup
    pool:
      name: ${{ parameters.agentPool }}
      vmImage: ubuntu-latest
      demands: # this is required to always run MDP on ubuntu 24.04
      - ImageOverride -equals ubuntu-24.04
    variables:
      - template: variables/development.yaml
    jobs:
      - job: Cleanup_Hosted_PR
        displayName: Cleanup closed PR
        condition: and(succeeded(), ne('$(pullRequestId)', ''))
        steps:
          - checkout: eneco-dxp-frontend
            clean: false

          - checkout: eneco-dxp-infra
            persistCredentials: true

          - task: AzureCLI@2
            displayName: Remove Hosted PR $(pullRequestId) deployment
            inputs:
              azureSubscription: ${{ variables.azureServiceConnectionTargetInfra }}
              scriptType: pscore
              scriptLocation: scriptPath
              scriptPath: "$(Agent.BuildDirectory)/s/eneco-dxp-frontend/cicd/pipelines/scripts/remove_hosted_pr_deployment.ps1"
              arguments: '-pullRequestId $(pullRequestId)'
              failOnStderr: true

      # Run this job only during the cron schedule
      - job: Cleanup_Inactive_Hosted_PR
        displayName: "Cleanup inactive hosted PRs"
        condition: eq(variables['Build.CronSchedule.DisplayName'], 'Cleanup Inactive Hosted PRs')
        steps:
          - checkout: eneco-dxp-frontend
            clean: false

          - checkout: eneco-dxp-infra
            persistCredentials: true

          - task: AzureCLI@2
            displayName: "Remove Inactive Hosted PR containers"
            env:
              GITHUB_TOKEN: $(GITHUB_TOKEN) # Set as pipeline variable
            inputs:
              azureSubscription: ${{ variables.azureServiceConnectionTargetInfra }}
              scriptType: pscore
              scriptLocation: scriptPath
              scriptPath: "$(Agent.BuildDirectory)/s/eneco-dxp-frontend/cicd/pipelines/scripts/get_inactive_hosted_pr_containers.ps1"
              failOnStderr: true


