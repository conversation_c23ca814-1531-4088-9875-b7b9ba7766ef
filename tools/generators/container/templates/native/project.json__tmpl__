{
  "name": "containers-<%= projectName %>",
  "$schema": "../../../../node_modules/nx/schemas/project-schema.json",
  "sourceRoot": "<%= target %>",
  "projectType": "application",
  "tags": ["container:next-native"],
  "targets": {
    "copy-statics": {
      "executor": "nx:run-commands",
      "options": {
        "command": "npx rsyncjs --quiet --deleteOrphaned libs/sparky/static <%= target %>/public"
      }
    },
    "build": {
      "executor": "@nx/next:build",
      "dependsOn": [
        {
          "target": "copy-statics",
          "projects": "self"
        }
      ],
      "options": {
        "root": "<%= target %>",
        "outputPath": "dist/<%= target %>",
        "fileReplacements": []
      },
      "configurations": {
        "development": {
          "outputPath": "<%= target %>"
        },
        "native": {
          "outputPath": "<%= target %>"
        },
        "production": {
          "fileReplacements": []
        }
      }
    },
    "serve": {
      "executor": "@nx/next:server",
      "dependsOn": [
        {
          "target": "copy-statics",
          "projects": "self"
        }
      ],
      "options": {
        "buildTarget": "<%= projectName %>:build",
        "port": <%= port %>,
        "dev": true
      },
      "native": {
        "buildTarget": "<%= projectName %>:build:native",
        "dev": true
      },
      "configurations": {
        "native": {
          "buildTarget": "<%= projectName %>:build:native",
          "dev": true
        },
        "production": {
          "buildTarget": "<%= projectName %>:build:production",
          "dev": false
        }
      }
    },
    "export": {
      "executor": "@nx/next:export",
      "dependsOn": [
        {
          "target": "copy-statics",
          "projects": "self"
        }
      ],
      "options": {
        "buildTarget": "<%= projectName %>:build:production",
        "outputPath": "dist/<%= target %>"
      }
    },
    "cap": {
      "executor": "nx:run-commands",
      "options": {
        "command": "npx cap --help",
        "cwd": "<%= target %>"
      }
    },
    "add": {
      "executor": "nx:run-commands",
      "options": {
        "command": "npx cap add",
        "cwd": "<%= target %>"
      },
      "configurations": {
        "ios": {
          "command": "npx cap add ios"
        },
        "android": {
          "command": "npx cap add android"
        }
      }
    },
    "run": {
      "executor": "nx:run-commands",
      "options": {
        "command": "npx cap run",
        "cwd": "<%= target %>"
      },
      "configurations": {
        "ios": {
          "command": "npx cap run ios"
        },
        "android": {
          "command": "npx cap run android"
        }
      }
    },
    "sync": {
      "executor": "nx:run-commands",
      "options": {
        "commands": [
          "npx cap sync"
        ],
        "cwd": "<%= target %>",
        "parallel": false
      },
      "configurations": {
        "ios": {
          "commands": [
            "nx export <%= projectName %>",
            "npx cap sync ios"
          ]
        },
        "android": {
          "commands": [
            "nx export <%= projectName %>",
            "npx cap sync android"
          ]
        }
      }
    },
    "update": {
      "executor": "nx:run-commands",
      "options": {
        "command": "npx cap update",
        "cwd": "<%= target %>"
      },
      "configurations": {
        "ios": {
          "command": "npx cap update ios"
        },
        "android": {
          "command": "npx cap update android"
        }
      }
    },
    "copy": {
      "executor": "nx:run-commands",
      "options": {
        "command": "npx cap copy",
        "cwd": "<%= target %>"
      },
      "configurations": {
        "ios": {
          "command": "npx cap copy ios"
        },
        "android": {
          "command": "npx cap copy android"
        }
      }
    },
    "open": {
      "executor": "nx:run-commands",
      "options": {
        "command": "npx cap open",
        "cwd": "<%= target %>"
      },
      "configurations": {
        "ios": {
          "command": "npx cap open ios"
        },
        "android": {
          "command": "npx cap open android"
        }
      }
    },
    "lint": {
      "executor": "@nx/eslint:lint",
      "options": {
        "lintFilePatterns": ["<%= target %>/**/*.{ts,tsx}"]
      }
    }
  }
}