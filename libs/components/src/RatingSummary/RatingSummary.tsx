import { FC } from 'react';

import { useCustomerPublicGetCustomerRatings } from '@dc/hooks';
import { useLinkComponent } from '@link';
import { CustomerRatingRendering } from '@sitecore/types/CustomerRating';
import { Box, TextLink, RatingStar, Stack, Text } from '@sparky';

const RatingSummary: FC<CustomerRatingRendering> = ({ fields }) => {
  const { data } = useCustomerPublicGetCustomerRatings();
  const Link = useLinkComponent();
  const rating = data?.averageScore;
  const reviewText = `${data?.ratingsAmount} ${fields?.text?.value ?? ''}`;

  if (!rating) return null;

  return (
    <Box aria-label={reviewText}>
      <Stack gap="2" alignY="start" direction="row">
        <Text size="BodyS" weight="bold" as="span">
          {`${rating.toFixed(1)}/${5 * 2}`}
        </Text>

        <RatingStar maxStars={5} score={rating} />
        {fields?.link?.value?.text && fields?.link?.value?.href ? (
          <Text size="BodyS">
            <Link href={fields?.link?.value?.href} linkType={fields.link.value.linktype}>
              <TextLink>{fields?.link?.value?.text}</TextLink>
            </Link>
          </Text>
        ) : (
          <Text size="BodyS">{reviewText}</Text>
        )}
      </Stack>
    </Box>
  );
};

export default RatingSummary;
