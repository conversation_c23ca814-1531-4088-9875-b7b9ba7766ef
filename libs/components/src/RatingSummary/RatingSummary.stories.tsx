import { Meta, StoryObj } from '@storybook/react';

import RatingSummary from './RatingSummary';

const meta: Meta<typeof RatingSummary> = {
  title: 'Sitecore Components/Building Blocks/RatingSummary',
  args: {
    fields: {
      text: {
        value: 'beoordelingen',
      },
      link: {
        value: {
          href: '/mijn-eneco/profiel/privacy/slimmer-inkopen-wijzigen/',
          text: 'Geef toestemming',
          anchor: '',
          linktype: 'internal',
          class: '',
          title: '',
          target: '',
          querystring: '',
          id: '{F3ADAB18-2F49-4616-8D53-67B16D234382}',
          url: '',
        },
      },
    },
  },
  component: RatingSummary,
};

export default meta;

type Story = StoryObj<typeof RatingSummary>;

export const BasicExample: Story = {};
