import { FC, ReactNode } from 'react';

import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { I18nProvider } from '@i18n';

import dictionary from './content/nl-NL.json';
import ShareOnSocials, { Props } from './ShareOnSocials';

window.open = jest.fn();

const POPUP_CONFIG =
  'height=400,width=550,left=237,top=184,location=no,menubar=no,resizable=no,scrollbars=yes,status=no,toolbar=no';
const NETWORK_LINKS = {
  facebook: 'https://www.facebook.com/sharer/sharer.php?u=%url%',
  linkedin: 'https://www.linkedin.com/sharing/share-offsite/?url=%url%',
  twitter: 'https://twitter.com/intent/tweet?text=%text%&url=%url%',
};

const TestWrapper: FC<{ children: ReactNode }> = ({ children }) => (
  <I18nProvider dictionary={dictionary}>{children}</I18nProvider>
);

const renderComponent = async (props?: Props) => {
  // eslint-disable-next-line testing-library/no-unnecessary-act,require-await
  await act(async () => {
    render(
      <TestWrapper>
        <ShareOnSocials {...props}></ShareOnSocials>
      </TestWrapper>,
    );
  });
};

describe('ShareOnSocials', () => {
  it('should render the given label', async () => {
    const customLabel = 'Test label';
    await renderComponent({
      label: customLabel,
    });
    expect(screen.getByText(customLabel)).toBeInTheDocument();
  });

  it('should display 3 social media icons', async () => {
    await renderComponent();
    expect(screen.getAllByRole('link')).toHaveLength(3);
  });

  it('should create the appropriate link for LinkedIn', async () => {
    const expectedUrl = 'http://localhost/';

    await renderComponent();

    const shareLink = screen.getByRole('link', {
      name: 'Delen via LinkedIn',
    });
    // The getByRole query above already returns the <a> element.
    expect((shareLink as HTMLAnchorElement)?.href).toBe(
      NETWORK_LINKS.linkedin.replace('%url%', encodeURIComponent(expectedUrl)),
    );
  });

  it('should overwrite the link for LinkedIn', async () => {
    const expectedUrl = 'https://www.eneco.nl/';

    await renderComponent({ url: expectedUrl });

    const shareLink = screen.getByRole('link', {
      name: 'Delen via LinkedIn',
    });
    expect((shareLink as HTMLAnchorElement)?.href).toBe(
      NETWORK_LINKS.linkedin.replace('%url%', encodeURIComponent(expectedUrl)),
    );
  });

  it('should open the appropriate link for LinkedIn', async () => {
    const expectedUrl = 'http://localhost/';

    await renderComponent();

    const shareLink = screen.getByRole('link', {
      name: 'Delen via LinkedIn',
    });
    await userEvent.click(shareLink as Element);

    expect(window.open).toHaveBeenCalledWith(
      NETWORK_LINKS.linkedin.replace('%url%', encodeURIComponent(expectedUrl)),
      '',
      POPUP_CONFIG,
    );
  });

  it('should create the appropriate link for Facebook', async () => {
    const expectedUrl = 'http://localhost/';

    await renderComponent();

    const shareLink = screen.getByRole('link', {
      name: 'Delen via Facebook',
    });
    expect((shareLink as HTMLAnchorElement)?.href).toBe(
      NETWORK_LINKS.facebook.replace('%url%', encodeURIComponent(expectedUrl)),
    );
  });

  it('should overwrite the link for Facebook', async () => {
    const expectedUrl = 'https://www.eneco.nl/';

    await renderComponent({ url: expectedUrl });

    const shareLink = screen.getByRole('link', {
      name: 'Delen via Facebook',
    });
    expect((shareLink as HTMLAnchorElement)?.href).toBe(
      NETWORK_LINKS.facebook.replace('%url%', encodeURIComponent(expectedUrl)),
    );
  });

  it('should open the appropriate link for Facebook', async () => {
    const expectedUrl = 'http://localhost/';

    await renderComponent();

    const shareLink = screen.getByRole('link', {
      name: 'Delen via Facebook',
    });
    await userEvent.click(shareLink as Element);

    expect(window.open).toHaveBeenCalledWith(
      NETWORK_LINKS.facebook.replace('%url%', encodeURIComponent(expectedUrl)),
      '',
      POPUP_CONFIG,
    );
  });

  it('should create the appropriate link and text for Twitter', async () => {
    const expectedText = 'Localhost';
    const expectedUrl = 'http://localhost/';

    const currentTitle = document.title;
    document.title = expectedText;

    await renderComponent();

    const shareLink = screen.getByRole('link', {
      name: 'Delen via Twitter',
    });
    expect((shareLink as HTMLAnchorElement)?.href).toBe(
      NETWORK_LINKS.twitter
        .replace('%text%', encodeURIComponent(expectedText))
        .replace('%url%', encodeURIComponent(expectedUrl)),
    );

    document.title = currentTitle;
  });

  it('should overwrite the link and text for Twitter', async () => {
    const expectedText = 'Eneco.nl';
    const expectedUrl = 'https://www.eneco.nl/';

    const currentTitle = document.title;
    document.title = expectedText;

    await renderComponent({ text: expectedText, url: expectedUrl });

    const shareLink = screen.getByRole('link', {
      name: 'Delen via Twitter',
    });
    expect((shareLink as HTMLAnchorElement)?.href).toBe(
      NETWORK_LINKS.twitter
        .replace('%text%', encodeURIComponent(expectedText))
        .replace('%url%', encodeURIComponent(expectedUrl)),
    );

    document.title = currentTitle;
  });

  it('should open the appropriate link for Twitter', async () => {
    const expectedText = 'Localhost';
    const expectedUrl = 'http://localhost/';

    const currentTitle = document.title;
    document.title = expectedText;

    await renderComponent();

    const shareLink = screen.getByRole('link', {
      name: 'Delen via Twitter',
    });
    await userEvent.click(shareLink as Element);

    expect(window.open).toHaveBeenCalledWith(
      NETWORK_LINKS.twitter
        .replace('%text%', encodeURIComponent(expectedText))
        .replace('%url%', encodeURIComponent(expectedUrl)),
      '',
      POPUP_CONFIG,
    );

    document.title = currentTitle;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
