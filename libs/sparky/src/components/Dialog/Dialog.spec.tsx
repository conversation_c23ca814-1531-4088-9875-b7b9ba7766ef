import { RenderResult, screen } from '@testing-library/react';
import userEvent, { PointerEventsCheckLevel } from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { TextLink } from '..';
import { Dialog } from './Dialog';
import { renderWithProviders } from '../../test-utils/render-with-providers';

describe('Given a Dialog', () => {
  describe('when in the default state', () => {
    let result: RenderResult;

    beforeEach(() => {
      result = renderWithProviders(
        <>
          <Dialog trigger={<TextLink emphasis="high">Trigger</TextLink>} title="title" description="description">
            Text
          </Dialog>
          <button>backdrop</button>
        </>,
      );
    });

    it('should have no accessibility violations', async () => {
      expect(await axe(result.container)).toHaveNoViolations();
    });

    it('should not show the dialog', () => {
      expect(screen.queryByText(/Text/)).not.toBeInTheDocument();
    });
  });

  describe('when in the open state', () => {
    const user = userEvent.setup({ pointerEventsCheck: PointerEventsCheckLevel.Never });
    let result: RenderResult;

    beforeEach(async () => {
      result = renderWithProviders(
        <>
          <Dialog trigger={<TextLink emphasis="high">Trigger</TextLink>} title="title" description="description">
            Text
          </Dialog>
          <button>backdrop</button>
        </>,
      );
      const trigger = await screen.findByRole('button', { name: 'Trigger' });
      await user.click(trigger);
    });

    it('should have no accessibility violations', async () => {
      expect(await axe(result.container)).toHaveNoViolations();
    });

    it('should show the dialog content', () => {
      expect(screen.getByText(/Text/)).toBeInTheDocument();
    });

    it('should not allow interactions with the backdrop while the Dialog is open', () => {
      const backdrop = screen.getByText(/backdrop/);
      backdrop.focus();
      expect(backdrop).not.toHaveFocus();
    });

    it('should close the Dialog on esc keypress', async () => {
      await user.keyboard('{Escape}');
      expect(screen.queryByText(/Text/)).not.toBeInTheDocument();
    });

    it('should close the Dialog on space keypress', async () => {
      await user.keyboard(' ');
      expect(screen.queryByText(/Text/)).not.toBeInTheDocument();
    });

    it('should close the Dialog on enter keypress', async () => {
      await user.keyboard('{Enter}');
      expect(screen.queryByText(/Text/)).not.toBeInTheDocument();
    });

    it('should close the Dialog by clicking the close button', async () => {
      const close = screen.getByRole('button', { name: /close/i });
      await user.click(close);
      expect(screen.queryByText(/Text/)).not.toBeInTheDocument();
    });
  });
});
