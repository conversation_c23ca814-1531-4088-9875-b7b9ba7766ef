import { RenderResult, screen, render } from '@testing-library/react';
import { axe } from 'jest-axe';

import { NavigationHelpButton } from './NavigationHelpButton';

describe('Given a NavigationHelpButton', () => {
  describe('when in the default state', () => {
    let result: RenderResult;

    beforeEach(() => {
      result = render(<NavigationHelpButton label="example-button"></NavigationHelpButton>);
    });

    it('should have no accessibility violations', async () => {
      expect(await axe(result.container)).toHaveNoViolations();
    });

    it('should render correctly', () => {
      expect(screen.getByRole('button', { name: /example-button/i })).toBeInTheDocument();
    });
  });
});
