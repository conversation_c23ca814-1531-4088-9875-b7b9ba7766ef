import { useEffect } from 'react';

import { useActor } from '@xstate/react';
import Cookies from 'js-cookie';

import useDC from '@dc/useDC';
import { useLayoutData } from '@sitecore/common';

import { useFlowContext } from '../utils/FlowProvider';
import { generateBasketIdCookieName, getBasketIdFromUrl } from '../utils/FlowUtils';

export const useFlowBasket = () => {
  const { flowService, basketService } = useFlowContext();
  const [flowState, sendToFlowMachine] = useActor(flowService);
  const [basketState, sendToBasketMachine] = useActor(basketService);

  const {
    context: flowContext,
    context: { basketId },
  } = flowState;

  const {
    route: { fields = {} },
  } = useLayoutData();

  const { businessUnit, label } = useDC();

  const basketCookieName = generateBasketIdCookieName(fields.flowName?.value?.toString());

  useEffect(() => {
    if (flowState.matches('IDLE')) {
      const savedBasketId = Cookies.get(basketCookieName);
      const basketIdFromUrl = getBasketIdFromUrl(basketCookieName);
      const { postalCode, houseNumber } = flowContext;
      const basketId = savedBasketId || basketIdFromUrl;

      // check if flows context is already filled up (e.g. query params postalCode && houseNumber)
      // then prioritize that over a earlier filled up basket content
      sendToBasketMachine({
        type: basketId && !postalCode && !houseNumber ? 'GET_BASKET' : 'CREATE_BASKET',
        config: {
          businessUnit,
          label,
        },
        ...(basketId && { id: basketId }),
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [flowState]);

  useEffect(() => {
    // init flow with a earlier filled up basket context
    if (basketState.matches('SUCCESS') || basketState.matches('ERROR')) {
      const { errorMessage, ...valuesFiltered } = basketState.context;

      sendToFlowMachine({ type: 'SET_ROUTE', values: valuesFiltered });
    }

    // if basket has status "CLOSED" we should set flow's state to "COMPLETED" as well
    if (basketState.matches('CLOSED') && !flowState.matches('COMPLETED')) {
      sendToFlowMachine({ type: 'COMPLETE' });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [basketState]);

  // each time flowMachines context chages we update the basket
  useEffect(() => {
    if (basketId && !flowState.matches('INIT') && !flowState.matches('IDLE')) {
      const { machines, ...valuesFiltered } = flowContext;

      sendToBasketMachine({
        type: flowState.matches('COMPLETED') ? 'CLOSE_BASKET' : 'UPDATE_BASKET',
        config: {
          businessUnit,
          label,
        },
        id: basketId,
        values: valuesFiltered,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [basketId, flowContext]);

  useEffect(() => {
    if (basketId) {
      Cookies.set(basketCookieName, basketId);
    }
  }, [basketId, basketCookieName]);
};
