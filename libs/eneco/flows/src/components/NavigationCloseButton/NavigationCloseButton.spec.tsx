import { RenderResult, screen, render } from '@testing-library/react';
import { axe } from 'jest-axe';

import { NavigationCloseButton } from './NavigationCloseButton';

describe('Given a NavigationCloseButton', () => {
  describe('when in the default state', () => {
    let result: RenderResult;

    beforeEach(() => {
      result = render(<NavigationCloseButton label="example-button"></NavigationCloseButton>);
    });

    it('should have no accessibility violations', async () => {
      expect(await axe(result.container)).toHaveNoViolations();
    });

    it('should render correctly', () => {
      expect(screen.getByRole('button', { name: /example-button/i })).toBeInTheDocument();
    });
  });
});
