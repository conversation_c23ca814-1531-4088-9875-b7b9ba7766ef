import { raise, StatesConfig } from 'xstate';

import { FlowContext, FlowRoute, FlowNavigationCategory, FlowEvent, FlowRouteCategory } from '../types';

/**
 * Creates an object with route states based on the routes config
 * - The first route has no previous route
 * - The last route will invoke the done / complete state of our machine
 * - Every other route will have the same actions that is defined in routes
 */

type State = {
  [key: string]: unknown;
};

type StateTranstion = {
  [key: string]: {
    target: string;
  };
};

// this function is used during "NEXT_PAGE" event to determine
// actual "EVENT" name to transition to a next machine state
// based on the "direction"
const getNextEventType = (context: FlowContext, routes: FlowRoute[], direction: 'next' | 'previous'): string => {
  let tempRoutes = [...routes];

  // determine routes order based on provided "direction"
  tempRoutes = direction === 'next' ? tempRoutes : tempRoutes.reverse();

  const indexCurrent = tempRoutes.findIndex(route => route.path === context.path) ?? 0;

  // reduce possible routes starting index of the current route
  tempRoutes = tempRoutes.slice(indexCurrent + 1);

  const nextRoute = tempRoutes.find(route => {
    if (!route.skippingRules?.length) {
      // should return a route when no "skippingRules" are associated
      // with that route
      return true;
    } else {
      // should return a route when every "skippingRules" that associated
      // with that route results in "false"
      return route.skippingRules.every(rule => rule(context) === false);
    }
  });

  // when no next route is found and navigation "direction" is "next"
  // we will assume that the flow reached it's final step
  return nextRoute ? `STEP_${nextRoute.id}` : direction === 'next' ? `COMPLETE` : '';
};

export const getEventTypeByContext = (routes: FlowRoute[] = [], context: FlowContext): string => {
  const nextRoute = routes.find(route => {
    if (!route.skippingFields?.length) {
      return true;
    } else {
      return !route.skippingFields?.every(field => context[field as keyof FlowContext]);
    }
  });

  return `STEP_${nextRoute?.id ?? routes[0]?.id}`;
};

export const getEventTypeByCategory = (
  routes: FlowRoute[],
  category: FlowRouteCategory,
  context: FlowContext,
): string => {
  const tempRoutes = routes.filter(route => route.category === category);

  const nextRoute = tempRoutes.find(route => {
    if (!route.skippingRules?.length) {
      // should return a route when no "skippingRules" are associated
      // with that route
      return true;
    } else {
      // should return a route when every "skippingRules" that associated
      // with that route results in "false"
      return route.skippingRules.every(rule => rule(context) === false);
    }
  });

  return `STEP_${nextRoute ? nextRoute.id : routes[0]?.id}`;
};

export const getEventTypeByPath = (routes: FlowRoute[] = [], path: string | undefined = ''): string =>
  `STEP_${routes.find(route => route.path === path)?.id ?? routes[0]?.id}`;

export const getNavigationCategories = (routes: FlowRoute[] = []): FlowNavigationCategory[] =>
  routes.reduce((acc: FlowNavigationCategory[], route: FlowRoute) => {
    const routePrevious = [...acc].pop();

    const { id, category } = route;

    if (category !== routePrevious?.category) {
      acc.push({
        id,
        category,
      });
    }

    return acc;
  }, []);

export const generateStateTransitions = (routes: FlowRoute[] = []): StateTranstion =>
  routes.reduce((acc: StateTranstion, route) => {
    acc[`STEP_${route.id}`] = {
      target: `STEP_${route.id}`,
    };

    return acc;
  }, {});

export const generateStates = (routes: FlowRoute[] = []): StatesConfig<FlowContext, never, FlowEvent, never> =>
  routes.reduce((acc: State, route, index) => {
    const hasNextPage = index !== routes.length - 1;
    const hasPreviousPage = !!routes[index - 1];

    acc[`STEP_${route.id}`] = {
      entry: raise({
        type: 'UPDATE_ROUTE',
        values: {
          path: route.path,
          pathCategory: route.category,
          hasPreviousPath: hasPreviousPage,
        },
      } as FlowEvent),
      on: {
        NEXT_PAGE: {
          ...(!hasNextPage && {
            target: 'COMPLETED',
          }),
          actions: [
            // set event values in the flow context
            'setValues',
            // perform all route assosiated actions
            ...(route.actions || []),
            // perform "next" navigation
            raise((context: FlowContext) => ({ type: getNextEventType(context, routes, 'next') })),
          ],
        },

        ...(hasPreviousPage && {
          PREVIOUS_PAGE: {
            // perform "previous" navigation
            actions: raise((context: FlowContext) => ({ type: getNextEventType(context, routes, 'previous') })),
          },
        }),
      },
    };

    return acc;
  }, {});

/**
 * Generates a basket id cookie name based on a flow name
 * @param flowName - The name of the flow
 * @param suffix - Optional suffix (default: 'basket')
 * @returns cookie name
 */
export function generateBasketIdCookieName(flowName: string, suffix: string = 'basket'): string {
  return `${flowName.toLowerCase()}_${suffix}`;
}

export const getBasketIdFromUrl = (basketCookieName: string): string => {
  if (typeof window === 'undefined') return '';

  const url = new URL(window.location.href);
  return url.searchParams.get(basketCookieName) ?? '';
};
