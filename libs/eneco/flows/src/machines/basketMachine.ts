import { assign, createMachine, EventObject, raise } from 'xstate';

import { ApiError } from '@dc/client/ApiError';
import { createBasket, getBasket, patchBasket } from '@dc/services/ShoppingBasketPublicService';
import {
  RequestDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketRequest,
  ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse,
} from '@monorepo-types/dc';

import { FlowContext, MachineConfig } from '../types';

export interface BasketMachineContext extends FlowContext {
  errorMessage?: string;
}

type BasketMachinesState = {
  value: 'IDLE' | 'CREATING' | 'FETCHING' | 'UPDATING' | 'CLOSING' | 'SUCCESS' | 'ERROR' | 'CLOSED';
  context: BasketMachineContext;
};

interface BasketMachineEvent extends EventObject {
  type: 'GET_BASKET' | 'CREATE_BASKET' | 'UPDATE_BASKET' | 'CLOSE_BASKET' | 'CLOSED';
  config: MachineConfig;

  id?: string;
  values?: Omit<FlowContext, 'machines'>;
  basketProperties?: Record<string, unknown>;
}

const createService = (
  config: MachineConfig,
): Promise<ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse> =>
  new Promise((resolve, reject) =>
    createBasket({
      businessUnit: config.businessUnit,
      label: config.label,
      requestBody: {},
    })
      .then(response => {
        resolve(response);
      })
      .catch((error: ApiError) => {
        reject({ error: error?.message });
      }),
  );

const getService = (
  config: MachineConfig,
  basketId: string,
): Promise<ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse> =>
  new Promise((resolve, reject) =>
    getBasket({
      businessUnit: config.businessUnit,
      label: config.label,
      id: basketId,
    })
      .then(response => {
        resolve(response);
      })
      .catch((error: ApiError) => {
        // pass down the "config" object onto the "onError" transition
        reject({ error: error?.message, config });
      }),
  );

const updateService = (
  config: MachineConfig,
  basketId: string,
  attributes?: Omit<FlowContext, 'machines'>,
  addtionalProperties?: Record<string, unknown>,
): Promise<ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse> =>
  new Promise((resolve, reject) => {
    const requestBody: RequestDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketRequest = {
      data: {
        ...addtionalProperties,
        attributes: { flowState: JSON.stringify(attributes) },
      },
    };

    return patchBasket({
      businessUnit: config.businessUnit,
      label: config.label,
      id: basketId,
      requestBody,
    })
      .then(response => {
        resolve(response);
      })
      .catch((error: ApiError) => {
        reject({ error: error?.message });
      });
  });

const getValuesFormAttributes = (attributes?: { [key: string]: string }): Omit<FlowContext, 'machines'> => {
  if (attributes?.flowState) {
    return JSON.parse(attributes.flowState);
  }

  return {};
};

export const basketMachine = createMachine<BasketMachineContext, BasketMachineEvent, BasketMachinesState>(
  {
    id: 'basket',
    predictableActionArguments: true,
    initial: 'IDLE',
    on: {
      CLOSED: { target: 'CLOSED' },
    },
    states: {
      IDLE: {
        on: {
          GET_BASKET: { target: 'FETCHING' },
          CREATE_BASKET: { target: 'CREATING' },
          UPDATE_BASKET: { target: 'UPDATING' },
          CLOSE_BASKET: { target: 'CLOSING' },
        },
      },
      CREATING: {
        invoke: {
          src: 'create',
          onDone: {
            target: ['SUCCESS'],
            actions: assign((_, event) => ({ basketId: event.data?.data?.id })),
          },
          onError: {
            target: ['ERROR'],
            actions: assign({ errorMessage: (_, event) => event.data?.error }),
          },
        },
      },
      FETCHING: {
        // @ts-ignore Check types for raise action
        invoke: {
          src: 'get',
          onDone: {
            target: ['SUCCESS'],
            actions: [
              raise((_, event) => ({ type: event.data?.data?.status === 'Ordered' ? 'CLOSED' : '' })),
              assign((_, event) => ({
                ...getValuesFormAttributes(event.data?.data?.attributes),
                cachedOffers: event.data?.data?.offers || [],
                basketId: event.data?.data?.id,
              })),
            ],
          },
          onError: {
            target: ['ERROR'],
            actions: raise((_, event) => ({
              type: 'CREATE_BASKET',
              config: event.data?.config,
            })),
          },
        },
      },
      UPDATING: {
        invoke: {
          src: 'update',
          onDone: {
            target: ['SUCCESS'],
          },
          onError: {
            target: ['ERROR'],
            actions: assign({ errorMessage: (_, event) => event.data?.error }),
          },
        },
      },
      CLOSING: {
        invoke: {
          src: 'close',
          onDone: {
            target: ['CLOSED'],
          },
          onError: {
            target: ['CLOSED'],
            actions: assign({ errorMessage: (_, event) => event.data?.error }),
          },
        },
      },
      SUCCESS: {
        type: 'atomic',
        entry: ['clearErrorMessage'],
        on: {
          GET_BASKET: { target: 'FETCHING' },
          CREATE_BASKET: { target: 'CREATING' },
          UPDATE_BASKET: { target: 'UPDATING' },
          CLOSE_BASKET: { target: 'CLOSING' },
        },
      },
      ERROR: {
        on: {
          CREATE_BASKET: { target: 'CREATING' },
        },
      },
      CLOSED: {
        type: 'final',
      },
    },
  },
  {
    actions: {
      clearErrorMessage: assign({ errorMessage: undefined }),
    },
    services: {
      create: (_, event) => createService(event.config),
      get: (_, event) => getService(event.config, event.id ?? ''),
      update: (_, event) => updateService(event.config, event.id ?? '', event.values, event.basketProperties),
      close: (_, event) => updateService(event.config, event.id ?? '', event.values, { status: 'Ordered' }),
    },
  },
);
