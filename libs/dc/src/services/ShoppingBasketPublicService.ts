import {
  DC_Repositories_Base_Enumerations_BusinessUnit,
  DC_Repositories_Base_Enumerations_Label,
  RequestDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketRequest,
  ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse,
} from '@monorepo-types/dc';

import { request } from '../client';
import type { ApiRequestConfig } from '../client/types';

type GetBasket = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  id: string;
};
/**
 * GetBasket
 * Gets a basket by Guid
 * @returns ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse Success
 */
export function getBasket(
  { businessUnit, label, id }: GetBasket,
  requestConfig: ApiRequestConfig = {},
): Promise<ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse> {
  return request(
    {
      method: 'GET',
      path: `/dxpweb/public/${businessUnit}/${label}/shoppingbasket/${id}`,
      errors: { 400: 'Bad Request' },
    },
    requestConfig,
  );
}

type PatchBasket = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  id: string;
  requestBody?: RequestDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketRequest;
};
/**
 * PatchBasket
 * Updates a basket
 * @returns ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse Success
 */
export function patchBasket(
  { businessUnit, label, id, requestBody }: PatchBasket,
  requestConfig: ApiRequestConfig = {},
): Promise<ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse> {
  return request(
    {
      method: 'PATCH',
      path: `/dxpweb/public/${businessUnit}/${label}/shoppingbasket/${id}`,
      body: requestBody,
      errors: { 404: 'Not Found' },
    },
    requestConfig,
  );
}

type DeleteBasket = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  id: string;
};
/**
 * DeleteBasket
 * Deletes a basket by Guid
 * @returns void
 */
export function deleteBasket(
  { businessUnit, label, id }: DeleteBasket,
  requestConfig: ApiRequestConfig = {},
): Promise<void> {
  return request(
    {
      method: 'DELETE',
      path: `/dxpweb/public/${businessUnit}/${label}/shoppingbasket/${id}`,
      errors: { 404: 'Not Found' },
    },
    requestConfig,
  );
}

type CreateBasket = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  requestBody?: RequestDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketRequest;
};
/**
 * CreateBasket
 * Creates a new basked
 * @returns ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse Success
 */
export function createBasket(
  { businessUnit, label, requestBody }: CreateBasket,
  requestConfig: ApiRequestConfig = {},
): Promise<ResponseDataDC_Domain_Models_Products_ShoppingBasket_ShoppingBasketResponse> {
  return request(
    {
      method: 'PUT',
      path: `/dxpweb/public/${businessUnit}/${label}/shoppingbasket`,
      body: requestBody,
    },
    requestConfig,
  );
}
