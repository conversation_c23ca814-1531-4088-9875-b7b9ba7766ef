import { DC_Domain_Models_P1_Interval } from '@monorepo-types/dc';
import { DC_Domain_Models_P1_UsageEnergyType } from '@monorepo-types/dc';
import { DC_Repositories_Base_Enumerations_BusinessUnit } from '@monorepo-types/dc';
import { DC_Repositories_Base_Enumerations_Label } from '@monorepo-types/dc';
import { ResponseModels_P1DongleUsage_P1DongleUsageFlowResponseModel } from '@monorepo-types/dc';
import { ResponseModels_P1DongleUsage_PowerNowResponseModel } from '@monorepo-types/dc';

import { request } from '../client';
import type { ApiRequestConfig } from '../client/types';

type GetPowerNowData = {
  dongleAgreementId: string;
  businessUnit?: DC_Repositories_Base_Enumerations_BusinessUnit;
  label?: DC_Repositories_Base_Enumerations_Label;
  customerId?: number;
};
/**
 * GetPowerNowData
 * Get realtime usage data from customer
 * @returns ResponseModels_P1DongleUsage_PowerNowResponseModel Success
 */
export function getPowerNowData(
  { dongleAgreementId, businessUnit, label, customerId }: GetPowerNowData,
  requestConfig: ApiRequestConfig = {},
): Promise<ResponseModels_P1DongleUsage_PowerNowResponseModel> {
  return request(
    {
      method: 'GET',
      path: `/powernow/data/${dongleAgreementId}`,
      query: { businessUnit, label, customerId },
      errors: { 400: 'Bad Request', 404: 'Not Found' },
    },
    requestConfig,
  );
}

type GetUsageFlow = {
  label?: DC_Repositories_Base_Enumerations_Label;
  customerId?: number;
  from?: string;
  to?: string;
  interval?: DC_Domain_Models_P1_Interval;
  energyType?: DC_Domain_Models_P1_UsageEnergyType;
};
/**
 * GetUsageFlow
 * Get usage flow of a customer from their P1 dongle
 * @returns ResponseModels_P1DongleUsage_P1DongleUsageFlowResponseModel Success
 */
export function getUsageFlow(
  { label, customerId, from, to, interval, energyType }: GetUsageFlow,
  requestConfig: ApiRequestConfig = {},
): Promise<ResponseModels_P1DongleUsage_P1DongleUsageFlowResponseModel> {
  return request(
    {
      method: 'GET',
      path: `/usageflow`,
      query: { label, customerId, from, to, interval, energyType },
      errors: { 400: 'Bad Request', 404: 'Not Found' },
    },
    requestConfig,
  );
}
