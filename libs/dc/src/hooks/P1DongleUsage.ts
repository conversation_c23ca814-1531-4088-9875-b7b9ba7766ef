import { createDCHook, collapseDataFromCall } from '../client';
import { getPowerNowData, getUsageFlow } from '../services/P1DongleUsageService';

export const useP1DongleUsageGetPowerNowData = createDCHook('getPowerNowData', collapseDataFromCall(getPowerNowData), {
  injectables: ['label', 'customerId', 'businessUnit'],
  flattenData: true,
});

export const useP1DongleUsageGetUsageFlow = createDCHook('getUsageFlow', collapseDataFromCall(getUsageFlow), {
  injectables: ['label', 'customerId'],
  flattenData: true,
});
