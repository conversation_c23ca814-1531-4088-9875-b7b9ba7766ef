/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/MyZone/AdvancePayments/ManageAdvanceAmount
 */
export interface ManageAdvanceAmountRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  advanceAmountImpact: AdvanceAmountImpact;
  advanceCard: AdvanceCard;
  commodityCard: CommodityCard;
  data: Data;
  vacancyCard: VacancyCard;
}
export interface AdvanceAmountImpact {
  eanLabel: TextField;
  title: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface AdvanceCard {
  acceptRecommendedAmountLink: GeneralLinkField;
  currencySignLabel: TextField;
  currentAmountTitle: TextField;
  eachMonthLabel: TextField;
  editAdvanceAmountLink: GeneralLinkField;
  recommendedAmountTooHighDisclaimerLabel: TextField;
  recommendedAmountTooLowDisclaimerLabel: TextField;
  recommendedAmountTitle: TextField;
  title: TextField;
}
export interface GeneralLinkField {
  editable?: string;
  value: {
    anchor: string;
    class: string;
    href: string;
    id: string;
    linktype: string;
    querystring: string;
    target: string;
    text: string;
    title: string;
    url: string;
  };
}
export interface CommodityCard {
  advanceAmountLabel: TextField;
  advanceAmountLink: GeneralLinkField;
  advanceOkNotification: NotificationField;
  advanceTooHighNotification: NotificationField;
  advanceTooLowNotification: NotificationField;
  changeAdvanceAmountNotification: NotificationField;
  changeAmountLink: GeneralLinkField;
  eanLabel: TextField;
  editLink: GeneralLinkField;
  electricityLabel: TextField;
  energyTypesList: NameValueDescriptionListField;
  gasLabel: TextField;
  monthlyActualUsageBillNotification: NotificationField;
  recommendedAdvanceAmountNotification: NotificationField;
  title: TextField;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: 'info' | 'success' | 'warning' | 'error' | 'chat';
  };
}
export interface NameValueDescriptionListField {
  editable?: string;
  value: {
    enum: {
      description: string;
      label: string;
      name: string;
      value: string;
    }[];
  };
}
export interface Data {
  addressNotDeliveredNotification: NotificationField;
  addressSelectLabel: TextField;
  advanceUnknownNotification: NotificationField;
  contractStartingSoonNotification: NotificationField;
  dunningLevelTooHighNotification: NotificationField;
  monthlyInvoicedNotification: NotificationField;
  newCustomerNotification: NotificationField;
  noActiveDeliveryNotification: NotificationField;
  recommendedAdvanceNotAvailableNotification: NotificationField;
  title: TextField;
}
export interface VacancyCard {
  activeOwnerDescription: TextField;
  activeUntilDescription: TextField;
  manageVacancyLink: GeneralLinkField;
  title: TextField;
}
