/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/MyZone/Product/MyEnecoProductDetails
 */
export interface MyEnecoProductDetailsRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  gridOperator: GridOperator;
  meterCard: MeterCard;
  productCardDetails: ProductCardDetails;
  productCardStatus: ProductCardStatus;
  productDetails: ProductDetails;
  tariffCard: TariffCard;
}
export interface GridOperator {
  helpText: TextField;
  label: TextField;
  title: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface MeterCard {
  eanFormField: CustomFormField;
  eanInfoPopoverText: TextField;
  meterNumberFormField: CustomFormField;
  meterRegimeChangeRequestPendingDescription: TextField;
  meterRegimeLabel: TextField;
  meterRegimePopoverText: TextField;
  meterRegimeTypesList: NameLabelListField;
  meterUpdateLink: GeneralLinkField;
  productTypesList: NameLabelListField;
  serviceComponentTypesList: NameLabelListField;
  serviceLabel: TextField;
  title: TextField;
  typeLabel: TextField;
  typesList: NameLabelListField;
}
export interface CustomFormField {
  editable?: string;
  value: {
    hint: string;
    label: string;
    placeholder: string;
    requiredMessage: string;
    validationMessage: string;
  };
}
export interface NameLabelListField {
  editable?: string;
  value: {
    enum: {
      label: string;
      name: string;
      value: string;
    }[];
  };
}
export interface GeneralLinkField {
  editable?: string;
  value: {
    anchor: string;
    class: string;
    href: string;
    id: string;
    linktype: string;
    querystring: string;
    target: string;
    text: string;
    title: string;
    url: string;
  };
}
export interface ProductCardDetails {
  energyTypeElectricityText: TextField;
  energyTypeGasText: TextField;
  inProgressPopoverText: TextField;
  inProgressText: TextField;
  link: GeneralLinkField;
  startsAtPopoverText: TextField;
  startsAtText: TextField;
  terminatedAtPopoverText: TextField;
  terminatedAtText: TextField;
  terminatesAtPopoverText: TextField;
  terminatesAtText: TextField;
  undeterminedTerminationPopoverText: TextField;
  undeterminedTerminationText: TextField;
  waitingForApprovalPopoverText: TextField;
  waitingForApprovalText: TextField;
}
export interface ProductCardStatus {
  productStatusesList: NameLabelListField;
}
export interface ProductDetails {
  locationFormField: CustomFormField;
}
export interface TariffCard {
  currentTariffAddendumLinkText: TextField;
  currentTariffFormField: CustomFormField;
  currentTariffLink: GeneralLinkField;
  expiredTariffCardLabel: TextField;
  nextTariffAddendumLinkText: TextField;
  nextTariffFormField: CustomFormField;
  nextTariffLink: GeneralLinkField;
  productNameFormField: CustomFormField;
  productUpdateLink: GeneralLinkField;
  promotionFormField: CustomFormField;
  title: TextField;
  // TODO: add this field to sitecore
  cancelProductSwitchErrorNotification: NotificationBox;
  cancelProductSwitchSuccessNotification: NotificationBox;
  cancelProductSwitchLink: GeneralLinkField;
}
