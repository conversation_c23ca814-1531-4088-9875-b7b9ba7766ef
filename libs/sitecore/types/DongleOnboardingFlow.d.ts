/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Project/Eneco Insights App/Dongle/DongleOnboardingFlow
 */
export interface DongleOnboardingFlowRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  bluetoothPairingOngoingLabel: TextField;
  bluetoothPairingProceedButtonLabel: TextField;
  bluetoothPairingSuccessLabel: TextField;
  bluetoothTutorialContent: TextField;
  bluetoothTutorialDialog: DialogField;
  bluetoothTutorialFallbackLink: GeneralLinkField;
  bluetoothTutorialFirstStepText: TextField;
  bluetoothTutorialFourthStepText: TextField;
  bluetoothTutorialSecondStepText: TextField;
  bluetoothTutorialThirdStepText: TextField;
  bluetoothTutorialTitle: TextField;
  connectDongleButtonLabel: TextField;
  connectDongleNavigationTitle: TextField;
  errorContent: TextField;
  errorImage: FocalPointImageField;
  errorReportProblemLink: GeneralLinkField;
  errorRetryButtonText: TextField;
  errorTitle: TextField;
  fluviusInstructionContent: TextField;
  fluviusInstructionContinueText: TextField;
  fluviusInstructionImage: FocalPointImageField;
  fluviusInstructionTitle: TextField;
  getStartedAfterDeliveryTitle: TextField;
  getStartedAfterPurchaseTitle: TextField;
  getStartedButtonText: TextField;
  getStartedContent: TextField;
  getStartedDongleImage: FocalPointImageField;
  getStartedNotification: NotificationField;
  getStartedPrimaryButtonText: TextField;
  getStartedSecondaryButtonText: TextField;
  getStartedTitle: TextField;
  headerPreviousText: TextField;
  networkConnectionConnectButtonText: TextField;
  networkConnectionOtherNetworkButtonText: TextField;
  networkConnectionPasswordFormField: CustomFormField;
  networkConnectionTitle: TextField;
  networkSelectionContent: TextField;
  networkSelectionSearchingText: TextField;
  networkSelectionTitle: TextField;
  portActivationApprovalContent: TextField;
  portActivationApprovalImage: FocalPointImageField;
  portActivationApprovalPrimaryButtonText: TextField;
  portActivationApprovalTitle: TextField;
  portActivationSuccessAfterPurchaseContent: TextField;
  portActivationSuccessAfterPurchasePrimaryButtonText: TextField;
  portActivationSuccessContent: TextField;
  portActivationSuccessImage: FocalPointImageField;
  portActivationSuccessPrimaryButtonText: TextField;
  portActivationSuccessSecondaryButtonText: TextField;
  portActivationSuccessTitle: TextField;
  progressIndicatorConnectBluetoothLabel: TextField;
  progressIndicatorConnectInternetLabel: TextField;
  successButtonLink: GeneralLinkField;
  successContent: TextField;
  successImage: FocalPointImageField;
  successTitle: TextField;
  waitingScreenPrimaryButtonText: TextField;
  waitingScreenSecondaryButtonText: TextField;
  waitingScreenStepListFiveText: TextField;
  waitingScreenStepListFourText: TextField;
  waitingScreenStepListOneText: TextField;
  waitingScreenStepListSixText: TextField;
  waitingScreenStepListThreeText: TextField;
  waitingScreenStepListTwoText: TextField;
  waitingScreenTitle: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface DialogField {
  editable?: string;
  value: {
    cancelButtonText: string | null;
    content: string;
    submitButtonText: string | null;
    title: string;
    triggerText: string;
  };
}
export interface GeneralLinkField {
  editable?: string;
  value: {
    anchor: string;
    class: string;
    href: string;
    id: string;
    linktype: string;
    querystring: string;
    target: string;
    text: string;
    title: string;
    url: string;
  };
}
export interface FocalPointImageField {
  editable?: string;
  value: {
    alt: string;
    formats: ThumbSchema[];
  };
}
export interface ThumbSchema {
  format: string;
  height: string;
  src: string;
  width: string;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: "info" | "success" | "warning" | "error" | "chat";
  };
}
export interface CustomFormField {
  editable?: string;
  value: {
    hint: string;
    label: string;
    placeholder: string;
    requiredMessage: string;
    validationMessage: string;
  };
}
