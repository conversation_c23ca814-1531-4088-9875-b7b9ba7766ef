/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Project/Eneco-be/Flows/ProductSwitch/ProductSwitch
 */
export interface ProductSwitchRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  electricityStep: ElectricityStep;
  gasStep: GasStep;
  navigation: Navigation;
  productProposal: ProductProposal;
  summaryElectricityCard: SummaryElectricityCard;
  summaryGasCard: SummaryGasCard;
  summaryStep: SummaryStep;
  items: ProductProposalItem[];
}
export interface ElectricityStep {
  cancelButtonText: TextField;
  continueButtonText: TextField;
  continueWithoutChangesButtonText: TextField;
  productSwitchDescription: TextField;
  subTitle: TextField;
  title: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface GasStep {
  cancelButtonText: TextField;
  continueButtonText: TextField;
  continueWithoutChangesButtonText: TextField;
  productSwitchDescription: TextField;
  subTitle: TextField;
  title: TextField;
}
export interface Navigation {
  electricityStepLabel: TextField;
  gasStepLabel: TextField;
  summaryStepLabel: TextField;
  cancelLink: GeneralLinkField;
  saveLink: GeneralLinkField;
  keepCurrentProductNotification: NotificationField;
  keepCurrentProductText: TextField;
  errorNotification: NotificationField;
  successNotification: NotificationField;
  cancelDialog: DialogField;
}
export interface ProductProposal {
  annualAmountLabel: TextField;
  currentProductLabel: TextField;
  rateCardLinkLabel: TextField;
}
export interface SummaryElectricityCard {
  annualAmountLabel: TextField;
  editButtonLabel: TextField;
  productNameLabel: TextField;
  rateCardLabel: TextField;
  rateCardLink: GeneralLinkField;
  eanLabel: TextField;
  rateCardLinkLabel: TextField;
  annualAmountDescription: TextField;
  title: TextField;
}
export interface SummaryGasCard {
  annualAmountLabel: TextField;
  editButtonLabel: TextField;
  productNameLabel: TextField;
  rateCardLabel: TextField;
  rateCardLink: GeneralLinkField;
  rateCardLinkLabel: TextField;
  eanLabel: TextField;
  annualAmountDescription: TextField;
  title: TextField;
}
export interface SummaryStep {
  description: TextField;
  errorNotification: NotificationField;
  successNotification: NotificationField;
  termsAndConditionsCheckboxFormField: CustomFormField;
  title: TextField;
  saveLink: GeneralLinkField;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: 'info' | 'success' | 'warning' | 'error' | 'chat';
  };
}
export interface CustomFormField {
  editable?: string;
  value: {
    hint: string;
    label: string;
    placeholder: string;
    requiredMessage: string;
    validationMessage: string;
  };
}
export interface ProductProposalItem {
  name: string;
  fields: ProductProposalItemFields;
}
export interface ProductProposalItemFields {
  data: Data;
}
export interface Data {
  productName: TextField;
  productFeaturesContent: TextField;
}
export interface GeneralLinkField {
  editable?: string;
  value: {
    anchor: string;
    class: string;
    href: string;
    id: string;
    linktype: string;
    querystring: string;
    target: string;
    text: string;
    title: string;
    url: string;
  };
}
export interface DialogField {
  value: {
    title: string;
    content: string;
    triggerText: string;
    submitButtonText: string | null;
    cancelButtonText: string | null;
  };
  editable?: string;
}
