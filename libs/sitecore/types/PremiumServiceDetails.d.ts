/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Project/Eneco Insights App/Profile/PremiumServiceDetails
 */
export interface PremiumServiceDetailsRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  descriptionText: TextField;
  detailsLabel: TextField;
  endDateLabel: TextField;
  manageSubscriptionButtonText: TextField;
  noEndDateLabel: TextField;
  partnerPortalDialog: DialogField;
  periodicityLabel: TextField;
  startDateLabel: TextField;
  statusLabelList: NameLabelListField;
  statusText: TextField;
  subscriptionIdLabel: TextField;
  featureOption: DropList;
  undoCancellationLink: GeneralLinkField;
  dongleConnectionLabel: TextField;
  dongleStatusLabelList: NameLabelListField;
  dongleConnectionLink: GeneralLinkField;
}

export interface DropList {
  editable?: string;
  value: string;
}

export interface TextField {
  editable?: string;
  value: string;
}
export interface DialogField {
  editable?: string;
  value: {
    cancelButtonText: string | null;
    content: string;
    submitButtonText: string | null;
    title: string;
    triggerText: string;
  };
}
export interface NameLabelListField {
  editable?: string;
  value: {
    enum: {
      label: string;
      name: string;
      value: string;
    }[];
  };
}
export interface GeneralLinkField {
  editable?: string;
  value: {
    anchor: string;
    class: string;
    href: string;
    id: string;
    linktype: string;
    querystring: string;
    target: string;
    text: string;
    title: string;
    url: string;
  };
}
