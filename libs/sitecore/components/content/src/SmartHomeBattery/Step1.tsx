import { FC, useEffect, useState } from 'react';

import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import { mapImage } from '@sitecore/common';
import { SmartHomeBatteryRendering } from '@sitecore/types/SmartHomeBattery';
import {
  Box,
  Dialog,
  Form,
  Grid,
  Heading,
  Image,
  InputNumber,
  InputSelect,
  RadioButton,
  RadioGroup,
  Stack,
  Text,
  TextLink,
} from '@sparky';

const Step1: FC<{
  fields: SmartHomeBatteryRendering['fields'];
  formMethods: UseFormReturn<FieldValues, undefined>;
}> = ({ fields, formMethods }) => {
  const {
    formState: { errors },
    register,
    watch,
    unregister,
    getValues,
    setValue,
    clearErrors,
  } = formMethods;

  const watchSolarPanels = watch('solarPanels');
  useEffect(() => {
    if (watchSolarPanels !== 'hasPV') {
      unregister('annualProduction');
    }
  }, [unregister, watchSolarPanels]);

  return (
    <>
      <Heading as="h2" size="M">
        {fields.step1Title.value}
      </Heading>

      <FormProvider {...formMethods}>
        <Form>
          <Heading as="h3" size="S">
            {fields.step1LivingSituationFormTitle.value}
          </Heading>
          <Stack direction="column" gap="6">
            <Stack gap="6" direction={{ initial: 'column', lg: 'row' }}>
              <Stack.Item grow>
                <InputNumber
                  hint={
                    <Text size="BodyS" color="textLowEmphasis">
                      {fields.step1LivingSituationFormFieldsZipCodeHintText.value}
                    </Text>
                  }
                  {...register('zipCode', {
                    required: fields.step1LivingSituationFormFieldsZipCodeErrorsRequiredText.value,
                    pattern: {
                      value: /^[1-9][0-9]{3}$/,
                      message: fields.step1LivingSituationFormFieldsZipCodeErrorsFormatText.value,
                    },
                    validate: {
                      flandersZipcode: v => {
                        const zipCode = parseInt(v, 10);
                        return (
                          (zipCode >= 1500 && zipCode <= 3999) ||
                          (zipCode >= 8000 && zipCode <= 9999) ||
                          fields.step1LivingSituationFormFieldsZipCodeErrorsFlandersText.value
                        );
                      },
                    },
                  })}
                  defaultValue={getValues('zipCode')}
                  formatOptions={{ useGrouping: false }}
                  error={errors?.zipCode?.message as string}
                  label={fields.step1LivingSituationFormFieldsZipCodeLabel.value}
                  autoComplete="postal-code"
                />
              </Stack.Item>
              <Stack.Item grow>
                <InputSelect
                  hint={
                    <Text size="BodyS" color="textLowEmphasis">
                      {fields.step1LivingSituationFormFieldsConstructionYearHintText.value}
                    </Text>
                  }
                  {...register('constructionYear', {
                    required: fields.step1LivingSituationFormFieldsConstructionYearErrorsRequiredText.value,
                  })}
                  placeholder={fields.step1LivingSituationFormFieldsConstructionYearPlaceholderText.value}
                  error={errors?.constructionYear?.message as string}
                  options={[
                    {
                      label: fields.step1LivingSituationFormFieldsConstructionYearOptions10YearsOlderText.value,
                      value: '>=10 jaar',
                    },
                    {
                      label: fields.step1LivingSituationFormFieldsConstructionYearOptions10YearsYoungerText.value,
                      value: '<10 jaar',
                    },
                  ]}
                  label={fields.step1LivingSituationFormFieldsConstructionYearLabel.value}
                />
              </Stack.Item>
            </Stack>
            <Stack gap="6" direction={{ initial: 'column', lg: 'row' }}>
              <Stack.Item grow>
                <InputSelect
                  hint={
                    <Text size="BodyS" color="textLowEmphasis">
                      <PopupHint fields={fields} />
                    </Text>
                  }
                  {...register('inverterType', {
                    required: fields.step1LivingSituationFormFieldsInverterTypeErrorsRequiredText.value,
                  })}
                  placeholder={fields.step1LivingSituationFormFieldsInverterTypePlaceholderText.value}
                  error={errors?.inverterType?.message as string}
                  options={[
                    {
                      label: fields.step1LivingSituationFormFieldsInverterTypeOptionsSingleText.value,
                      value: 'type 4,6',
                    },
                    {
                      label: fields.step1LivingSituationFormFieldsInverterTypeOptionsTripleText.value,
                      value: 'type 10',
                    },
                    {
                      label: fields.step1LivingSituationFormFieldsInverterTypeOptionsNoIdeaText.value,
                      value: 'singleNoIdea',
                    },
                  ]}
                  label={fields.step1LivingSituationFormFieldsInverterTypeLabel.value}
                />
              </Stack.Item>
              <Stack.Item grow>
                <InputNumber
                  hint={
                    <Text size="BodyS" color="textLowEmphasis">
                      {fields.step1LivingSituationFormFieldsAnnualConsumptionHintText.value}
                    </Text>
                  }
                  {...register('annualConsumption', {
                    min: {
                      value: fields.step1LivingSituationFormFieldsAnnualConsumptionErrorsMinValueText.value,
                      message: fields.step1LivingSituationFormFieldsAnnualConsumptionErrorsMinText.value.replace(
                        '{value}',
                        fields.step1LivingSituationFormFieldsAnnualConsumptionErrorsMinValueText.value,
                      ),
                    },
                    max: {
                      value: fields.step1LivingSituationFormFieldsAnnualConsumptionErrorsMaxValueText.value,
                      message: fields.step1LivingSituationFormFieldsAnnualConsumptionErrorsMaxText.value,
                    },
                  })}
                  defaultValue={getValues('annualConsumption')}
                  formatOptions={{ useGrouping: false }}
                  error={errors?.annualConsumption?.message as string}
                  label={fields.step1LivingSituationFormFieldsAnnualConsumptionLabel.value}
                />
              </Stack.Item>
            </Stack>

            <Heading id="solarPanelsTitle" as="h3" size="S">
              {fields.step1SolarPanelFormTitle.value}
            </Heading>
            <RadioGroup
              {...register('solarPanels', { validate: { required: v => v !== null && v !== '' } })}
              defaultValue={getValues('solarPanels')}
              onValueChange={value => {
                setValue('solarPanels', value);
                clearErrors('solarPanels');
              }}
              direction="column"
              error={
                errors?.solarPanels && (fields.step1SolarPanelFormFieldsSolarPanelsErrorsRequiredText.value as string)
              }
              aria-labelledby="solarPanelsTitle">
              <RadioButton value="hasPV">{fields.step1SolarPanelFormFieldsHasPvLabel.value}</RadioButton>
              {watchSolarPanels === 'hasPV' && (
                <Grid columns={{ initial: 1, lg: 2 }} gap={6}>
                  <InputNumber
                    {...register('annualProduction', {
                      max: {
                        value: fields.step1SolarPanelFormFieldsAnnualProductionErrorsMaxValueText.value,
                        message: fields.step1SolarPanelFormFieldsAnnualProductionErrorsMaxText.value,
                      },
                    })}
                    formatOptions={{ useGrouping: false }}
                    defaultValue={getValues('annualProduction')}
                    error={errors?.annualProduction?.message as string}
                    placeholder={fields.step1SolarPanelFormFieldsAnnualProductionPlaceholderText.value}
                    hint={
                      <Text size="BodyS" color="textLowEmphasis">
                        {fields.step1SolarPanelFormFieldsAnnualProductionHintText.value}
                      </Text>
                    }
                    label={fields.step1SolarPanelFormFieldsAnnualProductionLabel.value}
                  />
                </Grid>
              )}
              <RadioButton value="wantsPV">{fields.step1SolarPanelFormFieldsWantsPvLabel.value}</RadioButton>
              <RadioButton value="noPV">{fields.step1SolarPanelFormFieldsNoPvLabel.value}</RadioButton>
            </RadioGroup>
          </Stack>
        </Form>
      </FormProvider>
    </>
  );
};

const PopupHint: FC<{
  fields: SmartHomeBatteryRendering['fields'];
}> = ({ fields }) => {
  const [before, after] = fields.step1LivingSituationFormFieldsInverterTypeHintLabel.value.split('{link}');
  const [openModal, setOpenModal] = useState(false);
  return (
    <Dialog
      setOpen={setOpenModal}
      onClose={() => setOpenModal(false)}
      isOpen={openModal}
      title={fields.step1LivingSituationFormFieldsInverterTypePopupTitle.value}
      trigger={
        <>
          {before}
          <TextLink emphasis="low" onClick={() => setOpenModal(true)}>
            {fields.step1LivingSituationFormFieldsInverterTypeHintLinkText.value}
          </TextLink>
          {after}
        </>
      }
      width="regular">
      <Grid columns={2} gap={4}>
        {fields.step1LivingSituationFormFieldsInverterTypePopupImages.map((figure, index) => {
          const imageProps = mapImage(figure.fields.image);
          return (
            <Grid.Item key={`${figure.title}-${index}`}>
              <Heading as="h3" size="2XS">
                {figure.fields.title?.value}
              </Heading>
              <Image
                alt={imageProps.alt}
                hasLazyLoad={false}
                objectFit="cover"
                sources={imageProps.sources}
                src={imageProps.src || ''}
                width="100%"
              />
            </Grid.Item>
          );
        })}
      </Grid>
      <Box paddingY="6">
        <Text size="BodyS" color="textLowEmphasis">
          {fields.step1LivingSituationFormFieldsInverterTypePopupImagesSourceText.value}
        </Text>
      </Box>
      <RichText html={fields.step1LivingSituationFormFieldsInverterTypePopupBodyContent.value} />
    </Dialog>
  );
};

export default Step1;
