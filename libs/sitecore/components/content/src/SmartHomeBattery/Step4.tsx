import { FC, useEffect } from 'react';

import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import { useLinkComponent } from '@link';
import { SmartHomeBatteryRendering } from '@sitecore/types/SmartHomeBattery';
import {
  Bucket,
  Checkbox,
  Form,
  Heading,
  InputEmail,
  InputSelect,
  InputTelephone,
  InputText,
  Stack,
  Text,
  TextLink,
} from '@sparky';

const Step4: FC<{
  fields: SmartHomeBatteryRendering['fields'];
  formMethods: UseFormReturn<FieldValues, undefined>;
}> = ({ fields, formMethods }) => {
  const {
    formState: { errors },
    unregister,
    register,
    watch,
  } = formMethods;
  const watchIsCustomer = watch('isCustomer');

  useEffect(() => {
    if (!watchIsCustomer) {
      unregister('customernumber');
    }
  }, [unregister, watchIsCustomer]);
  return (
    <>
      <Heading as="h2" size="M">
        {fields.step4Title.value}
      </Heading>
      <Heading as="h3" size="S">
        {fields.step4Subtitle.value}
      </Heading>
      <RichText html={fields.step4BodyContent.value} />
      <FormProvider {...formMethods}>
        <Form>
          <Stack direction="column" gap="6">
            <Stack gap="6" direction={{ initial: 'column', lg: 'row' }}>
              <Stack.Item grow>
                <InputText
                  {...register('lastName', {
                    required: fields.step4ContactFormFieldsLastNameErrorsRequiredText.value,
                  })}
                  error={errors?.lastName?.message as string}
                  label={fields.step4ContactFormFieldsLastNameLabel.value}
                  autoComplete="family-name"
                />
              </Stack.Item>
              <Stack.Item grow>
                <InputText
                  {...register('firstName', {
                    required: fields.step4ContactFormFieldsFirstNameErrorsRequiredText.value,
                  })}
                  error={errors?.firstName?.message as string}
                  label={fields.step4ContactFormFieldsFirstNameLabel.value}
                  autoComplete="given-name"
                />
              </Stack.Item>
            </Stack>
            <Stack gap="6" direction={{ initial: 'column', lg: 'row' }}>
              <Stack.Item grow>
                <InputEmail
                  {...register('emailAddress', {
                    required: fields.step4ContactFormFieldsEmailAddressErrorsRequiredText.value,
                    pattern: {
                      value:
                        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                      message: fields.step4ContactFormFieldsEmailAddressErrorsFormatText.value,
                    },
                  })}
                  error={errors?.emailAddress?.message as string}
                  label={fields.step4ContactFormFieldsEmailAddressLabel.value}
                  autoComplete="email"
                  placeholder={fields.step4ContactFormFieldsEmailAddressPlaceholderText.value}
                />
              </Stack.Item>
              <Stack.Item grow>
                <InputTelephone
                  {...register('telephone', {
                    required: fields.step4ContactFormFieldsTelephoneErrorsRequiredText.value,
                    pattern: {
                      value: /^\+32[1-9][0-9]{7,8}$/,
                      message: fields.step4ContactFormFieldsTelephoneErrorsFormatText.value,
                    },
                  })}
                  error={errors.telephone?.message as string}
                  label={fields.step4ContactFormFieldsTelephoneLabel.value}
                  autoComplete="tel"
                  placeholder={fields.step4ContactFormFieldsTelephonePlaceholderText.value}
                />
              </Stack.Item>
            </Stack>
            <InputSelect
              {...register('requestType', {
                required: fields.step4ContactFormFieldsRequestTypeErrorsRequiredText.value,
              })}
              placeholder={fields.step4ContactFormFieldsRequestTypePlaceholderText.value}
              label={fields.step4ContactFormFieldsRequestTypeLabel.value}
              error={errors?.requestType?.message as string}
              options={[
                {
                  label: fields.step4ContactFormFieldsRequestTypeOptionsSoonText.value,
                  value: fields.step4ContactFormFieldsRequestTypeOptionsSoonText.value,
                },
                {
                  label: fields.step4ContactFormFieldsRequestTypeOptionsSearchText.value,
                  value: fields.step4ContactFormFieldsRequestTypeOptionsSearchText.value,
                },
                {
                  label: fields.step4ContactFormFieldsRequestTypeOptionsNotSureText.value,
                  value: fields.step4ContactFormFieldsRequestTypeOptionsNotSureText.value,
                },
              ]}
            />
            <Checkbox {...register('isCustomer')} label={fields.step4ContactFormFieldsIsCustomerLabel.value} />
            {watchIsCustomer && (
              <InputText
                {...register('customerID', {
                  required: fields.step4ContactFormFieldsCustomerIdErrorsRequiredText.value,
                  pattern: {
                    value: /^54[0-9]{8}$/,
                    message: fields.step4ContactFormFieldsCustomerIdErrorsFormatText.value,
                  },
                })}
                placeholder={fields.step4ContactFormFieldsCustomerIdPlaceholderText.value}
                error={errors?.customerID?.message as string}
                label={fields.step4ContactFormFieldsCustomerIdLabel.value}
              />
            )}
            <Checkbox
              {...register('consent', { required: fields.step4ContactFormFieldsConsentErrorsRequiredText.value })}
              error={errors?.consent?.message as string}
              label={fields.step4ContactFormFieldsConsentLabel.value}
            />
            {fields.step4ContactFormFieldsConsent2Label?.value && (
              <Checkbox {...register('consent2')} label={fields.step4ContactFormFieldsConsent2Label.value} />
            )}
          </Stack>
        </Form>
      </FormProvider>

      <Text size="BodyS" color="textLowEmphasis">
        <RichText html={fields.step4BottomContent.value} />
      </Text>
    </>
  );
};

export const ThankYouStep: FC<{
  fields: SmartHomeBatteryRendering['fields'];
}> = ({ fields }) => {
  const Link = useLinkComponent();
  return (
    <Bucket.Content>
      <Heading as="h2" size="M">
        {fields.step4ContactFormThankYouTitle.value}
      </Heading>
      <RichText html={fields.step4ContactFormThankYouBodyContent.value} />
      <Link
        href={fields.step4ContactFormThankYouLink?.value?.href}
        linkType={fields.step4ContactFormThankYouLink?.value?.linktype}>
        <TextLink emphasis="high">{fields.step4ContactFormThankYouLink?.value?.text}</TextLink>
      </Link>
    </Bucket.Content>
  );
};

export default Step4;
