import { FC, useEffect, useState } from 'react';

import { FieldValues, UseFormReturn } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import { VerticalDivider } from '@custom-components/VerticalDivider';
import { SmartEnergyOfferResponseRow } from '@dc-be/client/types.gen';
import { SmartHomeBatteryRendering } from '@sitecore/types/SmartHomeBattery';
import { Box, Divider, Expandable, Grid, Heading, InputSelect, Stack, Stretch, Text } from '@sparky';

import Battery from './Battery';

const Step3: FC<{
  fields: SmartHomeBatteryRendering['fields'];
  offers: SmartEnergyOfferResponseRow[];
  constructionYear: string;
  formMethods: UseFormReturn<FieldValues, undefined>;
}> = ({ fields, offers, constructionYear, formMethods }) => {
  const [offer, setOffer] = useState(offers[0]);
  const { register, getValues, watch } = formMethods;

  const watchStorage = watch('storage');

  useEffect(() => {
    setOffer(offers[+getValues('storage')]);
  }, [watchStorage, getValues, offers]);

  if (!offer) return null;

  return (
    <>
      <Heading as="h2" size="M">
        {fields.step3Title.value}
      </Heading>
      <Stack direction="column" gap="4">
        <Heading as="h3" size="S">
          {fields.step3Subtitle.value}
        </Heading>
        <RichText html={fields.step3BodyContent.value} />
        <Text size="BodyS">
          <RichText html={fields.step3CustomTopContent.value} />
        </Text>

        <Stack>
          <Box borderRadius="l" backgroundColor="backgroundSecondary" padding="4">
            <Grid gridTemplateColumns={{ md: '2fr 1fr' }} gap={{ initial: 6, md: 2 }}>
              <Stack gap={6} direction="row" alignX="start">
                <Stack.Item shrink={false}>
                  <Stretch height={true}>
                    <Stack gap="2" direction="column" alignX="justify">
                      <Text size={{ initial: 'BodyS', md: 'BodyM' }}>{fields.step3InfoBoxCapLabel.value}</Text>
                      <Heading as="h4" size="L">
                        {offer.savedVolume?.toString() || ''}
                        <Text display="inline" size="BodyXL" weight="bold" whiteSpace="nowrap">
                          {fields.step3CapacityUnitText.value.replace('{value}', '')}
                        </Text>
                      </Heading>
                    </Stack>
                  </Stretch>
                </Stack.Item>
                <VerticalDivider />
                <Stretch height={true}>
                  <Stack gap="2" direction="column" alignX="justify">
                    <Text size={{ initial: 'BodyS', md: 'BodyM' }}>{fields.step3InfoBoxModsLabel.value}</Text>
                    <Heading as="h4" size="L">
                      {offer.numberOfModules}
                    </Heading>
                  </Stack>
                </Stretch>
              </Stack>
              <InputSelect
                {...register('storage')}
                label={fields.step3InfoBoxStorageLabel.value}
                placeholder=""
                options={
                  offers.map((offer, index) => ({
                    value: index.toString(),
                    label: fields.step3CapacityUnitText.value.replace('{value}', offer.savedVolume?.toString() || ''),
                  })) || []
                }
              />
            </Grid>
          </Box>
        </Stack>

        <Box paddingY={2}>
          <Divider emphasis="medium" />
          <Box paddingRight="6">
            <Box padding="3">
              <Grid gridTemplateColumns="6fr 1fr" gap="2">
                <Grid.Item>
                  <Text size={{ initial: 'BodyS', md: 'BodyM' }}>{fields.step3TableCostLabel.value}</Text>
                </Grid.Item>
                <Grid.Item>
                  <Text size={{ initial: 'BodyS', md: 'BodyM' }}>
                    {fields.step3MoneyUnitText.value
                      .replace(' ', '\u00A0')
                      .replace(
                        '{value}',
                        constructionYear === '<10 jaar'
                          ? offer.investmentTwentyOnePercent?.toString() || ''
                          : offer.investmentSixPercent?.toString() || '',
                      )}
                  </Text>
                </Grid.Item>
              </Grid>
            </Box>
          </Box>
          <Divider emphasis="medium" />
          <Expandable>
            <Expandable.Trigger label="open me">
              <Box paddingLeft="3">
                <Box paddingY="3">
                  <Grid gridTemplateColumns="6fr 1fr" gap="2">
                    <Grid.Item>
                      <Text size={{ initial: 'BodyS', md: 'BodyM' }}>{fields.step3TableSavingsLabel.value}</Text>
                    </Grid.Item>
                    <Grid.Item>
                      <Text color="textHighlightVarFive" size={{ initial: 'BodyS', md: 'BodyM' }}>
                        {fields.step3MoneyUnitText.value
                          .replace(' ', '\u00A0')
                          .replace('{value}', offer.yearlySaving?.toString() || '')}
                      </Text>
                    </Grid.Item>
                  </Grid>
                </Box>
              </Box>
            </Expandable.Trigger>
            <Expandable.Content>
              <Divider emphasis="low" />
              <Box paddingRight="6">
                <Box padding="3">
                  <Grid gridTemplateColumns="6fr 1fr" gap="2">
                    <Grid.Item>
                      <Box paddingLeft="6">
                        <Text size={{ initial: 'BodyS', md: 'BodyM' }}>{fields.step3TableConsumptionLabel.value}</Text>
                      </Box>
                    </Grid.Item>
                    <Grid.Item>
                      <Text color="textHighlightVarFive" size={{ initial: 'BodyS', md: 'BodyM' }}>
                        {fields.step3MoneyUnitText.value
                          .replace(' ', '\u00A0')
                          .replace('{value}', offer.selfUsageOptimalisation?.toString() || '')}
                      </Text>
                    </Grid.Item>
                  </Grid>
                </Box>
              </Box>
              <Divider emphasis="low" />
              <Box paddingRight="6">
                <Box padding="3">
                  <Grid gridTemplateColumns="6fr 1fr" gap="2">
                    <Grid.Item>
                      <Box paddingLeft="6">
                        <Text size={{ initial: 'BodyS', md: 'BodyM' }}>{fields.step3TableReductionLabel.value}</Text>
                      </Box>
                    </Grid.Item>
                    <Grid.Item>
                      <Text color="textHighlightVarFive" size={{ initial: 'BodyS', md: 'BodyM' }}>
                        {fields.step3MoneyUnitText.value
                          .replace(' ', '\u00A0')
                          .replace('{value}', offer.capacityTarifReduction?.toString() || '')}
                      </Text>
                    </Grid.Item>
                  </Grid>
                </Box>
              </Box>
              <Divider emphasis="low" />
              <Box paddingRight="6">
                <Box padding="3">
                  <Grid gridTemplateColumns="6fr 1fr" gap="2">
                    <Grid.Item>
                      <Box paddingLeft="6">
                        <Text size={{ initial: 'BodyS', md: 'BodyM' }}>{fields.step3TableSteeringLabel.value}</Text>
                      </Box>
                    </Grid.Item>
                    <Grid.Item>
                      <Text color="textHighlightVarFive" size={{ initial: 'BodyS', md: 'BodyM' }}>
                        {fields.step3MoneyUnitText.value
                          .replace(' ', '\u00A0')
                          .replace('{value}', offer.enecoSmartSavings?.toString() || '')}
                      </Text>
                    </Grid.Item>
                  </Grid>
                </Box>
              </Box>
            </Expandable.Content>
          </Expandable>
          <Divider emphasis="medium" />
          <Box paddingRight="6">
            <Box padding="3">
              <Grid gridTemplateColumns="6fr 1fr" gap="2">
                <Grid.Item>
                  <Text size={{ initial: 'BodyS', md: 'BodyM' }} weight="bold">
                    {fields.step3TableYieldLabel.value}
                  </Text>
                </Grid.Item>
                <Grid.Item>
                  <Text size={{ initial: 'BodyS', md: 'BodyM' }} weight="bold">
                    {fields.step3YearUnitText.value
                      .replace(' ', '\u00A0')
                      .replace(
                        '{value}',
                        constructionYear === '<10 jaar'
                          ? offer.payBackPeriodTwentyOnePercent?.toString() || ''
                          : offer.payBackPeriodSixPercent?.toString() || '',
                      )}
                  </Text>
                </Grid.Item>
              </Grid>
            </Box>
          </Box>
          <Divider emphasis="medium" />
          <Box paddingRight="6">
            <Box padding="3">
              <Grid gridTemplateColumns="6fr 1fr" gap="2">
                <Grid.Item>
                  <Text size={{ initial: 'BodyS', md: 'BodyM' }} weight="bold">
                    {fields.step3TableSavings20YearLabel.value}
                  </Text>
                </Grid.Item>
                <Grid.Item>
                  <Text size={{ initial: 'BodyS', md: 'BodyM' }} weight="bold">
                    {fields.step3MoneyUnitText.value
                      .replace(' ', '\u00A0')
                      .replace(
                        '{value}',
                        constructionYear === '<10 jaar'
                          ? offer.roiTwentyOnePercent?.toString() || ''
                          : offer.roiSixPercent?.toString() || '',
                      )}
                  </Text>
                </Grid.Item>
              </Grid>
            </Box>
          </Box>
        </Box>
      </Stack>

      <Battery offer={offer} />

      <Text size="BodyS" color="textLowEmphasis">
        <RichText html={fields.step3CustomBottomContent.value} />
      </Text>
    </>
  );
};

export default Step3;
