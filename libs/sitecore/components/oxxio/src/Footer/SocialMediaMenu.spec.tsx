import React from 'react';

import { RenderResult, render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import { getSubfooterRight } from '@mocks/sitecore/components/shared/footer';

import SocialMediaMenu from './SocialMediaMenu';

describe('SocialMediaMenu', () => {
  let result: RenderResult;

  beforeEach(() => {
    result = render(<SocialMediaMenu {...getSubfooterRight()} />);
  });

  it('should have no accessibility violations', async () => {
    expect(await axe(result.container)).toHaveNoViolations();
  });

  it('should show heading', () => {
    expect(screen.getByText(/volg ons via social media/i)).toBeInTheDocument();
  });

  it('should show 5 links', () => {
    expect(screen.getByRole('link', { name: /facebook/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /instagram/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /linkedin/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /twitter/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /youtube/i })).toBeInTheDocument();

    expect(screen.queryAllByRole('link')).toHaveLength(5);
  });
});
