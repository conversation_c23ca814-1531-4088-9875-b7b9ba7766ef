import { ResponseModels_Subscriptions_CustomerInfoResponseModel } from '@monorepo-types/dc';

export const getCustomerInfoResponse: ResponseModels_Subscriptions_CustomerInfoResponseModel = {
  data: {
    chargebeeStatus: 'Active',
    customerPaymentStatus: 'AllGood',
    totalAmountDue: 0.0,
    customer: {
      billingAddress: {
        countryCode: 'BE',
        city: 'MECHELEN',
        email: '<EMAIL>',
        firstName: 'Jaime',
        lastName: 'Lopez Penalver',
        line1: 'Stephensonstraat 11201',
        line2: null,
        line3: null,
        postalCode: '2800',
        state: 'MECHELEN',
      },
      companyName: null,
      customerId: '5453505910',
      email: '<EMAIL>',
      firstName: 'Jaime',
      lastName: 'Lopez Penalver',
      phone: null,
      vatNumber: null,
      collectUrl: null,
    },
    dongle: {
      dongleStatus: 'Unknown',
      orderDate: '2025-05-22T14:34:57Z',
      purchaseStatus: 'Purchased',
      shippingAddress: {
        countryCode: 'BE',
        city: 'MECHELEN',
        email: '<EMAIL>',
        firstName: '<PERSON>',
        lastName: 'Lopez Penalver',
        line1: 'Stephensonstraat 11201',
        line2: null,
        line3: null,
        postalCode: '2800',
        state: 'MECHELEN',
      },
      shippingStatus: 'Shipped',
    },
    invoices: [
      {
        dueAmount: null,
        dueDate: '2025-05-21T22:00:00Z',
        dunningStatus: 'In_progress',
        invoiceAmount: 49.64,
        invoiceDate: '2025-05-22',
        invoiceNumber: 'INV-052025-50',
        lines: [
          {
            discountAmount: 0.0,
            itemName: 'EnergyMonitor Premium - Prorated Charges',
            quantity: 1,
            servicePeriodEnd: '2025-05-31T21:59:59Z',
            servicePeriodStart: '2025-05-21T22:00:00Z',
            subscriptionId: '198PIKUlw91i3sUO',
            totalAmount: 0.64,
          },
          {
            discountAmount: 0.0,
            itemName: 'Eneco Dongle',
            quantity: 1,
            servicePeriodEnd: '2025-05-21T22:00:00Z',
            servicePeriodStart: '2025-05-21T22:00:00Z',
            subscriptionId: '198PIKUlw91i3sUO',
            totalAmount: 49.0,
          },
        ],
        paymentFailureReasonCode: null,
        paymentFailureReasonMessage: null,
        status: 'Paid',
      },
      {
        dueAmount: null,
        dueDate: '2025-05-19T11:42:38Z',
        dunningStatus: 'In_progress',
        invoiceAmount: 30.0,
        invoiceDate: '2025-05-19',
        invoiceNumber: 'INV-052025-38',
        lines: [
          {
            discountAmount: 0.0,
            itemName: 'Eneco Dongle (losse verkoop)',
            quantity: 1,
            servicePeriodEnd: '2025-05-19T21:59:59Z',
            servicePeriodStart: '2025-05-18T22:00:00Z',
            subscriptionId: null,
            totalAmount: 30.0,
          },
        ],
        paymentFailureReasonCode: null,
        paymentFailureReasonMessage: null,
        status: 'Paid',
      },
      {
        dueAmount: null,
        dueDate: '2025-05-19T11:30:46Z',
        dunningStatus: 'In_progress',
        invoiceAmount: 30.0,
        invoiceDate: '2025-05-19',
        invoiceNumber: 'INV-052025-37',
        lines: [
          {
            discountAmount: 0.0,
            itemName: 'Eneco Dongle (losse verkoop)',
            quantity: 1,
            servicePeriodEnd: '2025-05-19T21:59:59Z',
            servicePeriodStart: '2025-05-18T22:00:00Z',
            subscriptionId: null,
            totalAmount: 30.0,
          },
        ],
        paymentFailureReasonCode: null,
        paymentFailureReasonMessage: null,
        status: 'Paid',
      },
    ],
    paymentMethod: {
      lastFourDigits: '1111',
      methodType: 'Creditcard',
      status: 'Expiring',
      expiryYear: 2025.0,
      expiryMonth: 5.0,
    },
    promotionalCreditsLeft: 0.0,
    promotionalCreditsTotal: 0.0,
    subscriptions: [
      {
        campaign: null,
        details: [],
        endDate: null,
        planDetailsUrl: null,
        planId: 'Smart-Insights',
        planName: 'EnergieMonitor Premium',
        planPrice: 1.99,
        planPriceModel: 'FixedAmount',
        quantity: 1,
        serviceType: 'smart-insights',
        startDate: '2025-05-21T22:00:00Z',
        status: 'Active',
        subscriptionId: '198PIKUlw91i3sUO',
      },
    ],
  },
};
