import { http, HttpResponse, delay } from 'msw';

import env from '@common/env';
import { ResponseModels_Documents_ContractResponseModel } from '@monorepo-types/dc';

const host = env('DC_HOST');

const successResponse: ResponseModels_Documents_ContractResponseModel = {
  data: {
    contracts: [
      {
        contractState: 'Active',
        contractId: 'contract-123',
        productTypes: ['electricity', 'gas'],
        contractDate: '2023-01-15',
      },
      {
        contractState: 'Future',
        contractId: 'contract-456',
        productTypes: ['electricity'],
        contractDate: '2023-06-01',
      },
    ],
  },
};

const noValidContractsResponse: ResponseModels_Documents_ContractResponseModel = {
  data: {
    contracts: [
      {
        contractState: 'Active',
        contractId: '', // Empty contractId
        productTypes: ['electricity'],
        contractDate: '2023-01-15',
      },
      {
        contractState: 'Future',
        contractId: '', // Empty contractId
        productTypes: ['gas'],
        contractDate: '2023-06-01',
      },
    ],
  },
};

const errorResponse = {
  errors: [
    {
      instanceId: 'a1b2c3d4-e5f6-4a5b-9c8d-1e2f3a4b5c6d',
      code: 'DCX-DW|CONTRACT_NOT_FOUND',
      type: 'FunctionalException',
      message: 'Bad Request',
    },
  ],
};

export default [
  http.get(
    `${host}/dxpweb/:businessUnit/:label/customers/:customerId/documents/contracts/latest`,
    async ({ params }) => {
      const { customerId } = params;

      await delay(300);

      if (customerId === '9999') {
        return HttpResponse.json(errorResponse, {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      // Customer with no valid contracts (empty contractId values)
      if (customerId === '12345') {
        return HttpResponse.json(noValidContractsResponse, {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      return HttpResponse.json(successResponse, {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    },
  ),
];
