import { format, subDays } from 'date-fns';

import { Customers_Profile_BECustomerProfileResponse } from '@monorepo-types/dc';

export const userWithDongleOutOfSupply: Customers_Profile_BECustomerProfileResponse = {
  customerId: **********,
  userAccount: {
    userName: '<EMAIL>',
    lastLogin: format(subDays(Date.now(), 1), 'yyyy-MM-dd'),
  },
  customerType: 'person',
  hasDongle: true,
  dongleInfoResponse: {
    status: 'OutOfSupply',
  },
  features: ['SmartChargingVehicle', 'LaunchPad', 'BEPushNotifications'],
  contact: {
    name: '<PERSON>',
    emailAddress: '<EMAIL>',
    preferences: undefined,
    phoneNumber: '+***********',
    mobilePhoneNumber: '+***********',
    address: undefined,
  },
  person: {
    firstName: 'Jan',
    gender: 'male',
    initials: null,
    name: '<PERSON>',
    salutation: 'Dhr.',
    surname: '<PERSON><PERSON><PERSON>',
    surnamePreposition: null,
    dateOfBirth: '1986-09-18',
  },
  organisation: undefined,
  accounts: [
    {
      accountId: 1,
      address: {
        street: 'Onbestaande straat',
        postalCode: '2800',
        houseNumber: '133',
        houseNumberSuffix: null,
        city: 'MECHELEN',
        bus: null,
      },
      correspondenceAddress: {
        street: 'Onbestaande straat',
        postalCode: '2800',
        houseNumber: '133',
        houseNumberSuffix: null,
        city: 'MECHELEN',
        bus: null,
      },
      active: null,
      customerProfileType: 'unknown',
      productTypes: null,
      productTypeDetails: null,
      nextChargeDate: null,
      startDate: null,
      endDate: null,
      hasRedelivery: false,
      hasRedeliveryCostTariff: null,
      hasServiceContract: false,
      meterDetails: null,
      classification: null,
      hasDynamicPricing: false,
    },
  ],
  orders: [
    {
      id: null,
      state: 'Accepted',
      startDate: '2022-11-25',
    },
    {
      id: null,
      state: 'Accepted',
      startDate: '2022-11-25',
    },
  ],
  outstandingReadings: null,
  readingInfo: {
    daysSinceLastReading: null,
  },
  relocationInfo: undefined,
};
