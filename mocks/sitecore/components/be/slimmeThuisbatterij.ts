import { HeroRendering } from '@sitecore/types/manual/Hero.types';

export const slimmeThuisbatterijFields = {
  uid: '867aff48-24ef-4855-9c41-c62bd7262cf7',
  componentName: 'SmartHomeBattery',
  dataSource: '{1012CE8B-D86A-4AEF-B83F-035519435786}',
  params: {},
  fields: {
    nextButtonText: { value: 'Volgende stap' },
    previousButtonText: { value: 'Vorige stap' },
    processIndicatorStep1: { value: 'Woonsituatie' },
    processIndicatorStep2: { value: 'Verbuikers' },
    processIndicatorStep3: { value: 'Configuratie' },
    processIndicatorStep4: { value: 'Contactgegevens' },
    step1BodyContent: { value: '' },
    step1LivingSituationFormErrorText: { value: 'Er zijn geen geldige resultaten' },
    step1LivingSituationFormErrorTitle: { value: 'Er liep iets fout' },
    step1LivingSituationFormFieldsAnnualConsumptionErrorsFormatText: {
      value: 'Afname van het net heeft niet het juiste formaat',
    },
    step1LivingSituationFormFieldsAnnualConsumptionErrorsMinValueText: { value: '1500' },
    step1LivingSituationFormFieldsAnnualConsumptionErrorsMinText: {
      value:
        'Een thuisbatterij is slechts rendabel voor jou indien je afname van het net hoger is dan {value} kWh/jaar.',
    },
    step1LivingSituationFormFieldsAnnualConsumptionErrorsMaxValueText: { value: '6000' },
    step1LivingSituationFormFieldsAnnualConsumptionErrorsMaxText: { value: 'Maximaal 6000kWh' },
    step1LivingSituationFormFieldsAnnualConsumptionErrorsRequiredText: {
      value: 'Afname van het net is een verplicht veld',
    },
    step1LivingSituationFormFieldsAnnualConsumptionHintText: {
      value: 'Een thuisbatterij is slechts rendabel voor jou indien je afname van het net hoger is dan xxx kWh/jaar.',
    },
    step1LivingSituationFormFieldsAnnualConsumptionLabel: { value: 'Afname van het net (kWh/jaar)' },
    step1LivingSituationFormFieldsConstructionYearErrorsRequiredText: {
      value: 'Leeftijd van je woning is een verplicht veld',
    },
    step1LivingSituationFormFieldsConstructionYearHintText: {
      value: 'De leeftijd van je woning bepaalt het BTW% van toepassing op de thuisbatterij.',
    },
    step1LivingSituationFormFieldsConstructionYearLabel: { value: 'Leeftijd van je woning' },
    step1LivingSituationFormFieldsConstructionYearOptions10YearsOlderText: { value: '10 jaar of ouder (6% BTW)' },
    step1LivingSituationFormFieldsConstructionYearOptions10YearsYoungerText: {
      value: 'Jonger dan 10 jaar (21% BTW)',
    },
    step1LivingSituationFormFieldsConstructionYearPlaceholderText: { value: 'Selecteer...' },
    step1LivingSituationFormFieldsInverterTypeErrorsRequiredText: {
      value: 'Huidig type aansluiting is een verplicht veld',
    },
    step1LivingSituationFormFieldsInverterTypeHintLabel: { value: '{link} als je niet zeker bent.' },
    step1LivingSituationFormFieldsInverterTypeHintLinkText: { value: 'Klik hier' },
    step1LivingSituationFormFieldsInverterTypeLabel: { value: 'Huidig type aansluiting' },
    step1LivingSituationFormFieldsInverterTypeOptionsNoIdeaText: { value: 'Ik weet het niet' },
    step1LivingSituationFormFieldsInverterTypeOptionsSingleText: { value: 'Enkelfasig' },
    step1LivingSituationFormFieldsInverterTypeOptionsTripleText: { value: 'Driefasig' },
    step1LivingSituationFormFieldsInverterTypePlaceholderText: { value: 'Selecteer...' },
    step1LivingSituationFormFieldsInverterTypePopupBodyContent: {
      value:
        '<p>Bekijk je meter en zoek het icoontje. Indien je nog een analoge meter hebt, dan is het vrij makkelijk om de afleiding te maken of je eenfasig of driefasig bent aangesloten op het net. In geval je een driefasige aansluiting hebt, dan dien je het logo terug te vinden, maar ook het schermpje te raadplegen om de juiste keuze te maken.</p><p>Kan je om een of andere reden toch die afleiding niet maken, dan kies je best 1-fasige aansluiting, gezien 80% van de huishoudens in Vlaanderen zo op het net aangesloten zijn.</p>',
    },
    step1LivingSituationFormFieldsInverterTypePopupImages: [],
    step1LivingSituationFormFieldsInverterTypePopupImagesSourceText: { value: 'Bron foto = ORES' },
    step1LivingSituationFormFieldsInverterTypePopupTitle: { value: 'Enkelfasige of driefasige meter' },
    step1LivingSituationFormFieldsZipCodeErrorsFormatText: { value: 'De postcode heeft niet het juiste formaat' },
    step1LivingSituationFormFieldsZipCodeErrorsFlandersText: {
      value: 'Momenteel is onze batterij enkel beschikbaar in Vlaanderen.',
    },
    step1LivingSituationFormFieldsZipCodeErrorsRequiredText: { value: 'Postcode is een verplicht veld' },
    step1LivingSituationFormFieldsZipCodeHintText: { value: 'Vul een postcode in regio Vlaanderen in' },
    step1LivingSituationFormFieldsZipCodeLabel: { value: 'Postcode' },
    step1LivingSituationFormPaybackTimeErrorText: {
      value:
        'Op basis van jouw inputgegevens is het momenteel niet zinvol om te investeren in onze slimme thuisbatterij.',
    },
    step1LivingSituationFormPaybackTimeErrorTitle: { value: 'Er liep iets fout' },
    step1LivingSituationFormTitle: { value: 'Jouw woonsituatie' },
    step1SolarPanelFormFieldsAnnualProductionFormatText: {
      value: 'Productie zonnepanelen heeft niet het juiste formaat',
    },
    step1SolarPanelFormFieldsAnnualProductionHintText: {
      value: 'Weet je het niet, dan gebruiken we de opwek van een gemiddeld zonnedak in Vlaanderen voor de simulatie.',
    },
    step1SolarPanelFormFieldsAnnualProductionLabel: { value: 'Productie zonnepanelen (kWh/jaar)' },
    step1SolarPanelFormFieldsAnnualProductionPlaceholderText: { value: '3500' },
    step1SolarPanelFormFieldsAnnualProductionErrorsMaxValueText: { value: '6000' },
    step1SolarPanelFormFieldsAnnualProductionErrorsMaxText: { value: 'Maximaal 6000kWh' },
    step1SolarPanelFormFieldsHasPvLabel: { value: 'Ik heb zonnepanelen' },
    step1SolarPanelFormFieldsNoPvLabel: { value: 'Ik heb geen zonnepanelen en wens er geen in de toekomst' },
    step1SolarPanelFormFieldsSolarPanelsErrorsRequiredText: { value: 'Zonnepanelen is een verplicht veld' },
    step1SolarPanelFormFieldsWantsPvLabel: {
      value: 'ik heb geen zonnepanelen en wens een simulatie voor zonnepanelen en een slimme thuisbatterij.',
    },
    step1SolarPanelFormTitle: { value: 'Zonnepanelen' },
    step1Subtitle: { value: '' },
    step1Title: { value: 'Hoeveel bespaar jij met een Slimme thuisbatterij?' },
    step2BodyContent: {
      value:
        '<p>Onderstaande verbruikers hebben een grote invloed op je verbruiksprofiel en dus in het bepalen van de ideale batterijconfiguratie.</p>',
    },
    step2FieldsHasElecHeatingHintText: { value: '' },
    step2FieldsHasElecHeatingLabel: { value: 'Elektrische verwarming' },
    step2FieldsHasEvHintText: { value: '' },
    step2FieldsHasEvLabel: { value: 'Thuislaadpunt' },
    step2FieldsHasHeatPumpHintText: { value: '' },
    step2FieldsHasHeatPumpLabel: { value: 'Warmtepomp' },
    step2FieldsHasSwimPoolHintText: {
      value: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempo.',
    },
    step2FieldsHasSwimPoolLabel: { value: 'Zwembad' },
    step2Subtitle: { value: 'Jouw grote verbruikers' },
    step2Title: { value: 'Verfijn je verbruiksprofiel' },
    step3BasicBottomContent: {
      value:
        '<p>* Onder de vorm van gegarandeerde cash-back voor klanten zonder dynamsch tarief.</p><p><strong>Let op:</strong> door deze simulaties uit te voeren zal je geen bestelling plaatsen. Deze simulatie geeft je een indicatief beeld. Er zal steeds eerst een technisch adviseur bij jou langskomen. Zo zorgen we dat je het maximale uit je investering haalt. </p>',
    },
    step3BasicToggleLabel: { value: 'Basis batterij' },
    step3BasicTopContent: {
      value:
        '<p>Ideaal als je met een kleiner budget toch reeds de voordelen van een slimme batterij wil benutten. Als deze opstelling op termijn toch te klein uitvalt, kan je makkelijk een of meerder modules bijkopen. Dit is de meest toegankelijke opstelling van 5kWh opslag met de laagste investeringskost.</p>',
    },
    step3BodyContent: {
      value: '<p>Bekijk wat een batterij op maat of een basisopstelling betekent voor jou.</p>',
    },
    step3CapacityUnitText: { value: '{value} kWh' },
    step3CustomBottomContent: {
      value:
        '<p>* Onder de vorm van gegarandeede cash-back gedurende 3 jaar, daarna wordt de besparing die Eneco realiseert door het net in balans te houden met jou gedeeld.</p><p><strong>Let op:</strong> door deze simulaties uit te voeren zal je geen bestelling plaatsen. Deze simulatie geeft je een indicatief beeld. Er zal steeds eerst een technisch adviseur bij jou langskomen. Zo zorgen we dat je het maximale uit je investering haalt. </p>',
    },
    step3CustomToggleLabel: { value: 'Batterij op maat' },
    step3CustomTopContent: {
      value: '<p>De batterij met de kortste terugverdientijd en een zo hoog mogelijk besparingspotentieel.</p>',
    },
    step3InfoBoxCapLabel: { value: 'Capaciteit' },
    step3InfoBoxModsLabel: { value: 'Modules = # batterij module(s) + 1 omvormer module' },
    step3InfoBoxStorageLabel: { value: 'Stel opslagcapaciteit in' },
    step3MoneyUnitText: { value: '€ {value}' },
    step3Subtitle: { value: 'Jouw configuratie' },
    step3TableConsumptionLabel: { value: 'Zelfverbruikoptimalisatie' },
    step3TableCostLabel: { value: 'Investering (incl. BTW)' },
    step3TableReductionLabel: { value: 'Reductie capaciteitstarief' },
    step3TableSavings20YearLabel: { value: 'Netto rendement na 20 jaar' },
    step3TableSavingsLabel: { value: 'Jaarlijks rendement' },
    step3TableSteeringLabel: { value: 'Besparing Eneco Slim Sturen *' },
    step3TableYieldLabel: { value: 'Kortste terugverdientijd' },
    step3Title: { value: 'De ideale slimme thuisbatterij voor jou!' },
    step3YearUnitText: { value: '{value} jaar' },
    step4BodyContent: {
      value:
        '<p>Onze <strong>partner Energreen</strong> contacteert je via onderstaande gegevens om een plaatsbezoek vast te leggen. Tijdens dat plaatsbezoek wordt bekeken waar de batterij het best geinstalleerd kan worden, en krijg je direct een bindende offerte. Al jouw vragen worden ter plekke beantwoord door een expert!</p>',
    },
    step4ContactFormFieldsConsentErrorsRequiredText: { value: 'Consent is een verplicht veld' },
    step4ContactFormFieldsConsentLabel: { value: 'Consent lorem ipsum*' },
    step4ContactFormFieldsConsent2Label: { value: 'Consent lorem ipsum 2' },
    step4ContactFormFieldsCustomerIdErrorsRequiredText: { value: 'Klantnummer is een verplicht veld' },
    step4ContactFormFieldsCustomerIdErrorsFormatText: { value: 'Klantnummer heeft niet het juiste formaat' },
    step4ContactFormFieldsCustomerIdPlaceholderText: { value: '54XXXXXXXX' },
    step4ContactFormFieldsCustomerIdLabel: { value: 'Klantnummer*' },
    step4ContactFormFieldsEmailAddressErrorsFormatText: { value: 'E-mailadres heeft niet het juiste formaat' },
    step4ContactFormFieldsEmailAddressErrorsRequiredText: { value: 'E-mailadres is een verplicht veld' },
    step4ContactFormFieldsEmailAddressLabel: { value: 'E-mailadres*' },
    step4ContactFormFieldsEmailAddressPlaceholderText: { value: '<EMAIL>' },
    step4ContactFormFieldsFirstNameErrorsRequiredText: { value: 'Voornaam is een verplicht veld' },
    step4ContactFormFieldsFirstNameLabel: { value: 'Voornaam*' },
    step4ContactFormFieldsIsCustomerLabel: { value: 'Ik ben klant bij Eneco' },
    step4ContactFormFieldsLastNameErrorsRequiredText: { value: 'Naam is een verplicht veld' },
    step4ContactFormFieldsLastNameLabel: { value: 'Naam*' },
    step4ContactFormFieldsRequestTypeErrorsRequiredText: { value: 'Aanvraagtype is een verplicht veld' },
    step4ContactFormFieldsRequestTypeLabel: { value: 'Aanvraagtype*' },
    step4ContactFormFieldsRequestTypeOptionsNotSureText: {
      value:
        'Ik ben niet zeker of een thuisbatterij iets voor mij is en wens dat samen met een expert bekijken wat dat kan betekenen voor me',
    },
    step4ContactFormFieldsRequestTypeOptionsSearchText: {
      value: 'Ik ga een thuisbatterij kopen, maar ben nog aan het verkennen',
    },
    step4ContactFormFieldsRequestTypeOptionsSoonText: {
      value: 'Ik ga heel binnenkort een thuisbatterij installeren',
    },
    step4ContactFormFieldsRequestTypePlaceholderText: { value: 'Selecteer...' },
    step4ContactFormFieldsTelephoneErrorsFormatText: { value: 'Telefoonnummer heeft niet het juiste formaat' },
    step4ContactFormFieldsTelephoneErrorsRequiredText: { value: 'Telefoonnummer is een verplicht veld' },
    step4ContactFormFieldsTelephoneLabel: { value: 'Telefoonnummer*' },
    step4ContactFormFieldsTelephonePlaceholderText: { value: '+32' },
    step4ContactFormThankYouBodyContent: {
      value:
        '<p>Je kan binnenkort een telefoontje verwachten van onze <strong>partner Energreen</strong> om het aanbod verder te verfijnen. We willen je alvast bedanken voor je interesse in dit project en hopen je snel van een slimme batterij te kunnen voorzien.</p>',
    },
    step4ContactFormThankYouLink: {
      value: {
        text: 'Terug naar eneco.be',
        href: '/',
        linktype: '',
        url: '',
        anchor: '',
        target: '',
        class: '',
        title: '',
        querystring: '',
        id: '',
      },
    },
    step4ContactFormThankYouTitle: { value: 'Bedankt!' },
    step4Subtitle: { value: 'Jouw contactgegevens' },
    step4Title: { value: 'Vraag een gratis plaatsbezoek aan' },
    step4BottomContent: {
      value:
        '<p>Bekijk hier ons <a href="#">Privacy beleid</a> en de <a href="#">gebruiksvoorwaarden</a> van onze sturing</p>',
    },
    submitButtonText: { value: 'Bevestigen' },
  },
  datasourceRequired: true,
};

export const slimmeThuisbatterij: HeroRendering = {
  uid: 'a122fc9a-b832-412a-8a2c-14d185cf0d11',
  componentName: 'Hero',
  dataSource: '{BED2ABAE-6F48-4E98-AD65-A06F7C716746}',
  params: {
    height: 'variable',
    width: 'variable',
  },
  fields: {
    image: {
      value: {
        alt: '',
        formats: [
          {
            format: 'default',
            src: 'https://cdn-assets-eu.frontify.com/s3/frontify-enterprise-files-eu/eyJvYXV0aCI6eyJjbGllbnRfaWQiOiJzaXRlY29yZSJ9LCJwYXRoIjoiZW5lY29cL2ZpbGVcL0V6N1N5VWlRdzF6eWhVR3ByNDdiLmpwZyJ9:eneco:mgwZvhBc6JGVqvroDiqs-r_a5zerR5sYCmc3P0idbAM?width=440&height=280',
            height: '280px',
            width: '440px',
          },
          {
            format: 'large',
            src: 'https://cdn-assets-eu.frontify.com/s3/frontify-enterprise-files-eu/eyJvYXV0aCI6eyJjbGllbnRfaWQiOiJzaXRlY29yZSJ9LCJwYXRoIjoiZW5lY29cL2ZpbGVcL0V6N1N5VWlRdzF6eWhVR3ByNDdiLmpwZyJ9:eneco:mgwZvhBc6JGVqvroDiqs-r_a5zerR5sYCmc3P0idbAM?width=1920&height=640',
            height: '640px',
            width: '1920px',
          },
          {
            format: 'small',
            src: 'https://cdn-assets-eu.frontify.com/s3/frontify-enterprise-files-eu/eyJvYXV0aCI6eyJjbGllbnRfaWQiOiJzaXRlY29yZSJ9LCJwYXRoIjoiZW5lY29cL2ZpbGVcL0V6N1N5VWlRdzF6eWhVR3ByNDdiLmpwZyJ9:eneco:mgwZvhBc6JGVqvroDiqs-r_a5zerR5sYCmc3P0idbAM?width=1024&height=460',
            height: '460px',
            width: '1024px',
          },
        ],
      },
    },
  },
  placeholders: {
    'jss-hero-bottom': [],
    'jss-hero-left': [slimmeThuisbatterijFields],
    'jss-hero-right': [],
  },
};
