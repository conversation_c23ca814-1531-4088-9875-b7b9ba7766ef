import navigationHeader from './navigation';

const mockupData = [
  navigationHeader,
  {
    uid: '91346eeb-8888-434e-82f8-0feca05bca65',
    componentName: 'OfferStep',
    dataSource: '{0A31DC53-E75F-48D9-9A82-A140A665377E}',
    params: {},
    fields: {
      content: {
        text: {
          value: '',
        },
        nextStepText: {
          value: 'Volgende',
        },
        notification: {
          value: {
            variant: 'info',
            title: '',
            content: '',
          },
        },
        contractTypeTitle: {
          value: 'Kies je type leveringstarief',
        },
        title: {
          value: 'Welke contract kies je?',
        },
        noOfferSelectedNotification: {
          value: {
            variant: 'info',
            title: 'Geen aanbod geselecteerd',
            content: 'Selecteer een aabod',
          },
        },

        moreInfoDialog: {
          value: {
            title: 'Welk contract past bij mij?',
            content:
              'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque tempus ultricies justo vel elementum. Duis at dui feugiat nulla fringilla venenatis et id diam. Duis sed ultrices purus. Sed eleifend dignissim ornare. Suspendisse a nunc enim. Praesent sollicitudin euismod ante, luctus efficitur orci consectetur dignissim. Cras rutrum augue nisi, ut placerat elit tristique dignissim. Integer id consectetur nulla. Donec pulvinar risus eu erat auctor, id dapibus ante dignissim. Mauris aliquam elit at nisi efficitur aliquam eget a mi. Suspendisse a est arcu. Donec tempus, nisl ac pellentesque faucibus, orci nisi maximus magna, in tempor neque erat a dui. In pellentesque mollis sapien nec pretium. Phasellus et nisi vitae mauris consequat tristique in a tortor. Ut suscipit sapien vel nunc convallis scelerisque.',
            triggerText: 'Welk contract past bij mij?',
            submitButtonText: '',
            cancelButtonText: '',
          },
        },
      },
      footer: {
        footerDescription: {
          value: 'Aanbod voor <strong>{address}</strong> op basis van',
        },
        redeliveryLabel: {
          value: 'Teruglevering',
        },
        gasLabel: {
          value: 'Gas',
        },
        waterLabel: {
          value: 'Water',
        },
        warmthLabel: {
          value: 'Warmte',
        },
        electricityLabel: {
          value: 'Stroom',
        },
        electricityLowLabel: {
          value: 'Dal',
        },
        electricityHighLabel: {
          value: 'Normaal',
        },
        modifyTriggerText: {
          value: 'Mijn verbruik aanpassen',
        },
      },
      genericError: {
        errorNotAvailableNotification: {
          value: {
            variant: 'error',
            title: 'Bel ons om een contract af te sluiten',
            content:
              'Op dit moment kun je bij Eneco geen contract afsluiten. Dringend stroom en/of gas nodig? Sluit dan telefonisch ons modelcontract af. Vanwege de onzekere energiemarkt wijzigt ons aanbod wekelijks. Houd voor het actuele aanbod onze website in de gaten, of lees meer over onze&nbsp;<a href="https://www.eneco.nl/duurzame-energie/energieprijzen/">energieprijzen</a>.',
          },
        },
        errorNotFoundNotification: {
          value: {
            variant: 'warning',
            title: 'We kunnen je op dit moment geen offers laten zien.',
            content:
              'We helpen je graag via onze klantenservice. We zijn op maandag tot en met vrijdag van 08.00 tot 16.00 bereikbaar. Telefoon 088-8 955 955 (gebruikelijke belkosten).',
          },
        },
        errorNotification: {
          value: {
            variant: 'error',
            title: 'We kunnen je op dit moment de looptijden niet laten zien.',
            content:
              'Er is een technisch probleem ontstaan. Laad de pagina opnieuw, of neem contact op met de&nbsp;<a rel="noopener noreferrer" href="https://www.eneco.nl/klantenservice/" target="_blank">Klantenservice</a>.',
          },
        },
        errorNotAuthenticatedNotification: {
          value: {
            variant: 'error',
            title: 'Session verlopen',
            content: 'Helaas is de sessie verlopen',
          },
        },
        loginButtonText: {
          value: 'Opnieuw inloggen',
        },
        tryAgainButtonText: {
          value: 'Laad de pagina opnieuw',
        },
      },
      productData: {
        contractTypeRadio: {
          label: 'Contracttype',
          value: {
            requiredMessage: 'Kies een type contract',
            options: [
              {
                name: 'fixed',
                label: 'Vast',
              },
              {
                name: 'dynamic',
                label: 'Dynamisch',
              },
            ],
          },
        },
        tariffsTriggerText: {
          value: 'Bekijk tarieven',
        },
        yearLabel: {
          value: 'jaar',
        },
        perMonthLabel: {
          value: 'geschat per maand',
        },
        perMonthEstimatedLabel: {
          value: 'verwacht per maand',
        },
        monthsLabel: {
          value: 'maanden',
        },
        variablePeriodLabel: {
          value: 'Dynamisch energiecontract',
        },
        productDataList: [
          {
            id: '3310795c-89e8-4070-a8a4-51d81cd7b63d',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity/',
            name: '1 Year Electricity',
            displayName: '1 Year Electricity',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '1-y-electricity',
                },
                usp3Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: 'd945f727-15fb-4b6b-94de-81dd3440ca52',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity-and-gas/',
            name: '1 Year Electricity and Gas',
            displayName: '1 Year Electricity and Gas',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom en gas',
                },
                usp2Text: {
                  value: ' Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '1-y-electricity-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: 'ade3ae39-ad22-4bfb-b0a1-18cc6f363992',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity-and-warmth/',
            name: '1 Year Electricity and Warmth',
            displayName: '1 Year Electricity and Warmth',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom en warmte',
                },
                usp2Text: {
                  value: 'Duurzaam verwarmen en altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '1-y-electricity-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '59214497-eb66-4607-adee-21b50a84da42',
            url: '/library/commodity-offer-data-library-folder/1-year-gas/',
            name: '1 Year Gas',
            displayName: '1 Year Gas',
            fields: {
              data: {
                name: {
                  value: '1 jaar gas',
                },
                usp2Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                id: {
                  value: '1-y-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '6c99217b-f3cf-42bc-bdaa-518c65e92fa3',
            url: '/library/commodity-offer-data-library-folder/1-year-warmth/',
            name: '1 Year Warmth',
            displayName: '1 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '1 jaar warmte',
                },
                usp2Text: {
                  value: 'Duurzamere manier van verwarmen en flinke CO2 reductie ten opzichte van gas ',
                },
                id: {
                  value: '1-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: '90e7d404-7b01-46cd-8fee-cdc0ccbaacaf',
            url: '/library/commodity-offer-data-library-folder/3-months-electricity/',
            name: '3 Months Electricity',
            displayName: '3 Months Electricity',
            fields: {
              data: {
                name: {
                  value: '3 maanden stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-m-electricity',
                },
                usp3Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                usp1Text: {
                  value: 'Zekerheid voor een korte periode, daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: 'b6f55e56-4385-47d8-a6bb-c511e63473f7',
            url: '/library/commodity-offer-data-library-folder/3-months-electricity-and-gas/',
            name: '3 Months Electricity and Gas',
            displayName: '3 Months Electricity and Gas',
            fields: {
              data: {
                name: {
                  value: '3 maanden stroom en gas',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-m-electricity-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: 'Zekerheid voor een korte periode, daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: 'a1fef1ba-ccdd-4500-9fbf-9ebe90bb6152',
            url: '/library/commodity-offer-data-library-folder/3-months-gas/',
            name: '3 Months Gas',
            displayName: '3 Months Gas',
            fields: {
              data: {
                name: {
                  value: '3 maanden gas',
                },
                usp2Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                id: {
                  value: '3-m-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: 'Zekerheid voor een korte periode, daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: '81aad285-6224-489d-9149-5797a89a80a1',
            url: '/library/commodity-offer-data-library-folder/3-years-electricity/',
            name: '3 Years Electricity',
            displayName: '3 Years Electricity',
            fields: {
              data: {
                name: {
                  value: '3 jaar stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-y-electricity',
                },
                usp3Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                usp1Text: {
                  value: '3 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '92717078-96b6-4f81-9432-6c181e6acb4b',
            url: '/library/commodity-offer-data-library-folder/3-years-electricity-and-gas/',
            name: '3 Years Electricity and Gas',
            displayName: '3 Years Electricity and Gas',
            fields: {
              data: {
                name: {
                  value: '3 jaar stroom en gas',
                },
                usp2Text: {
                  value: 'Voor gemak en duidelijkheid',
                },
                id: {
                  value: '3-y-electricity-gas',
                },
                usp3Text: {
                  value: 'Na 3 jaar vrij opzeggen',
                },
                usp1Text: {
                  value: 'Vaste tarieven voor 3 jaar',
                },
              },
            },
          },
          {
            id: '826a65d0-88fa-40ef-9d10-963172865f28',
            url: '/library/commodity-offer-data-library-folder/3-years-gas/',
            name: '3 Years Gas',
            displayName: '3 Years Gas',
            fields: {
              data: {
                name: {
                  value: '3 jaar gas',
                },
                usp2Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                id: {
                  value: '3-y-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '3 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '89a1bf62-5a73-43a5-ab9c-3e555ab85c64',
            url: '/library/commodity-offer-data-library-folder/3-year-electricity-and-warmth/',
            name: '3 Year Electricity and Warmth',
            displayName: '3 Year Electricity and Warmth',
            fields: {
              data: {
                name: {
                  value: '3 jaar stroom en warmte',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-y-electricity-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: '185505f0-9244-4802-9794-8d95ae589ebe',
            url: '/library/commodity-offer-data-library-folder/3-year-warmth/',
            name: '3 Year Warmth',
            displayName: '3 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '3 jaar warmte',
                },
                usp2Text: {
                  value: 'Duurzamere manier van verwarmen en flinke CO2 reductie ten opzichte van gas ',
                },
                id: {
                  value: '3-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: '27516bae-f2c9-4385-87b0-9f581cb233f4',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity/',
            name: '6 Months Electricity',
            displayName: '6 Months Electricity',
            fields: {
              data: {
                name: {
                  value: '6 maanden stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '6-m-electricity',
                },
                usp3Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                usp1Text: {
                  value: '6 maanden zeker van je tarief daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: '16393388-0665-46c8-aee2-8a92dece5c1e',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-gas/',
            name: '6 Months Electricity and Gas',
            displayName: '6 Months Electricity and Gas',
            fields: {
              data: {
                name: {
                  value: '6 maanden stroom en gas',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '6-m-electricity-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '6 maanden zeker van je tarief daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: 'ae5198c8-ac90-4ba6-839e-625bf847d91a',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-warmth/',
            name: '6 Months Electricity and Warmth',
            displayName: '6 Months Electricity and Warmth',
            fields: {
              data: {
                name: {
                  value: '6 maanden stroom en warmte',
                },
                usp2Text: {
                  value: 'Duurzaam verwarmen en altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '6-m-electricity-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: '348eab98-6db0-46fd-b96c-24fcd089ba2e',
            url: '/library/commodity-offer-data-library-folder/6-months-gas/',
            name: '6 Months Gas',
            displayName: '6 Months Gas',
            fields: {
              data: {
                name: {
                  value: '6 maanden gas',
                },
                usp2Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                id: {
                  value: '6-m-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '6 maanden zeker van je tarief daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: 'd76a98a7-1418-4928-86c0-feb6ac86255f',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity/',
            name: '0 Year Electricity',
            displayName: '0 Year Electricity',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch stroom',
                },
                usp2Text: {
                  value: 'Vind voordelige dagdelen in de app',
                },
                id: {
                  value: '0-y-electricity',
                },
                usp3Text: {
                  value: 'Flexibel opzeggen zonder boete',
                },
                usp1Text: {
                  value: 'Betaal actuele uurprijzen',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: '8cdb3c34-715d-49b5-8d3e-4186fbc582c4',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity-and-gas/',
            name: '0 Year Electricity and Gas',
            displayName: '0 Year Electricity and Gas',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch gas & stroom',
                },
                usp2Text: {
                  value: 'Vind voordelige dagdelen in de app',
                },
                id: {
                  value: '0-y-electricity-gas',
                },
                usp3Text: {
                  value: 'Flexibel opzeggen zonder boete',
                },
                usp1Text: {
                  value: 'Betaal actuele uur- en dagprijzen',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: 'baa4659b-5265-4467-8dbe-eb41281a8dc0',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity-and-warmth/',
            name: '0 Year Electricity and Warmth',
            displayName: '0 Year Electricity and Warmth',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch stroom & stadswarmte',
                },
                usp2Text: {
                  value: 'Vind voordelige dagdelen in de app',
                },
                id: {
                  value: '0-y-electricity-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betaal actuele uurprijzen voor stroom',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: '51ab57be-1043-42d1-8a61-dda847785f89',
            url: '/library/commodity-offer-data-library-folder/0-year-gas/',
            name: '0 Year Gas',
            displayName: '0 Year Gas',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch gas',
                },
                usp2Text: {
                  value: 'Voor meer vrijheid en controle',
                },
                id: {
                  value: '0-y-gas',
                },
                usp3Text: {
                  value: 'Altijd boeteloos opzeggen',
                },
                usp1Text: {
                  value: 'Actuele dagtarieven',
                },
              },
            },
          },
          {
            id: '8f827823-f398-43e0-a706-7a06d3b2259a',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity-and-1-year-gas/',
            name: '1 Year Electricity and 1 Year Gas',
            displayName: '1 Year Electricity and 1 Year Gas',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom en gas',
                },
                usp2Text: {
                  value: 'Voor zekerheid en zorgeloosheid',
                },
                id: {
                  value: '1-y-electricity-1-y-gas',
                },
                usp3Text: {
                  value: 'Na 1 jaar vrij opzeggen',
                },
                usp1Text: {
                  value: 'Vast jaartarief voor stroom en gas',
                },
              },
            },
          },
          {
            id: '08f1293d-f957-4b21-93d5-8310fcd2462a',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity-and-0-year-warmth/',
            name: '1 Year Electricity and 0 Year Warmth',
            displayName: '1 Year Electricity and 0 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom',
                },
                usp2Text: {
                  value: 'Duurzaam verwarmen en altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '1-y-electricity-0-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '2f9b8ec2-b45d-4fe8-9d8e-29af7d5404ec',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity-and-0-year-gas/',
            name: '0 Year Electricity and 0 Year Gas',
            displayName: '0 Year Electricity and 0 Year Gas',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch stroom en gas',
                },
                usp2Text: {
                  value: 'Vind voordelige dagdelen in de app',
                },
                id: {
                  value: '0-y-electricity-0-y-gas',
                },
                usp3Text: {
                  value: 'Flexibel opzeggen zonder boete',
                },
                usp1Text: {
                  value: 'Betaal actuele uur- en dagprijzen',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: '9ea79de9-eb22-45ad-8505-520ab24db671',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity-and-0-year-warmth/',
            name: '0 Year Electricity and 0 Year Warmth',
            displayName: '0 Year Electricity and 0 Year Warmth',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch stroom',
                },
                usp2Text: {
                  value: 'Vind voordelige dagdelen in de app',
                },
                id: {
                  value: '0-y-electricity-0-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betaal actuele uurprijzen voor stroom',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: '108c390e-2e28-413a-a0b5-dcec31c81ce2',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity-and-1-year-gas/',
            name: '0 Year Electricity and 1 Year Gas',
            displayName: '0 Year Electricity and 1 Year Gas',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch stroom en vast 1 jaar gas',
                },
                usp2Text: {
                  value: 'Voor mix flexibiliteit en zekerheid',
                },
                id: {
                  value: '0-y-electricity-1-y-gas',
                },
                usp3Text: {
                  value: 'Stroom zonder boete opzeggen',
                },
                usp1Text: {
                  value: 'Vast jaartarief voor gas',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: 'cdc5df2d-35cb-4986-8444-48b2e9b78f7d',
            url: '/library/commodity-offer-data-library-folder/3-months-electricity-and-0-year-warmth/',
            name: '3 Months Electricity and 0 Year Warmth',
            displayName: '3 Months Electricity and 0 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '3 maanden stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-m-electricity-0-y-warmth',
                },
                usp3Text: {
                  value: 'Hergebruik van warmte die er al is',
                },
                usp1Text: {
                  value: 'Zekerheid voor een korte periode, daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: '7373796f-eb23-4ae1-8a0d-e4abca098ff6',
            url: '/library/commodity-offer-data-library-folder/3-months-electricity-and-3-months-gas/',
            name: '3 Months Electricity and 3 Months Gas',
            displayName: '3 Months Electricity and 3 Months Gas',
            fields: {
              data: {
                name: {
                  value: '3 maanden stroom en gas',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-m-electricity-3-m-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: 'Zekerheid voor een korte periode, daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: 'e8943cb4-95bb-4e36-8db4-ad473d7b1fed',
            url: '/library/commodity-offer-data-library-folder/3-year-electricity-and-0-year-warmth/',
            name: '3 Year Electricity and 0 Year Warmth',
            displayName: '3 Year Electricity and 0 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '3 jaar stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-y-electricity-0-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: 'a00de5b0-e2cd-47af-92cb-0497a455882f',
            url: '/library/commodity-offer-data-library-folder/3-years-electricity-and-3-years-gas/',
            name: '3 Years Electricity and 3 Years Gas',
            displayName: '3 Years Electricity and 3 Years Gas',
            fields: {
              data: {
                name: {
                  value: '3 jaar stroom en gas',
                },
                usp2Text: {
                  value: 'Voor gemak en duidelijkheid',
                },
                id: {
                  value: '3-y-electricity-3-y-gas',
                },
                usp3Text: {
                  value: 'Na 3 jaar vrij opzeggen',
                },
                usp1Text: {
                  value: 'Vaste tarieven voor 3 jaar',
                },
              },
            },
          },
          {
            id: '087e786e-2a4f-49ac-8567-176a12b23f0d',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-0-year-warmth/',
            name: '6 Months Electricity and 0 Year Warmth',
            displayName: '6 Months Electricity and 0 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '6 maanden stroom',
                },
                usp2Text: {
                  value: 'Duurzaam verwarmen en altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '6-m-electricity-0-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: '55e5253a-8fed-4112-813d-f1d6815a1768',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-6-motths-gas/',
            name: '6 Months Electricity and 6 Motths Gas',
            displayName: '6 Months Electricity and 6 Motths Gas',
            fields: {
              data: {
                name: {
                  value: '6 maanden stroom en gas',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '6-m-electricity-6-m-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '6 maanden zeker van je tarief daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: '55e5253a-8fed-4112-813d-f1d6815a1768',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-6-motths-gas/',
            name: '1 Year Electricity And Gas + Eneco Voordeel-Momenten',
            displayName: '1 Year Electricity And Gas + Eneco Voordeel-Momenten',
            fields: {
              data: {
                name: {
                  value: '1 Year Electricity And Gas + Eneco Voordeel-Momenten',
                },
                usp1Text: {
                  value: 'Vast jaartarief voor stroom en gas',
                },
                id: {
                  value: 'TimeOfUse-1-y-electricity-1-y-gas',
                },
                usp3Text: {
                  value: 'Na 1 jaar vrij opzeggen',
                },
                usp2Text: {
                  value: 'Dagelijks 30% korting op stroom tussen 10:00 uur - 17:00 uur en tussen 22:00 uur - 5:00 uur',
                },
              },
            },
          },
          {
            id: '55e5253a-8fed-4112-813d-f1d6815a1769',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-6-motths-gas/',
            name: '1 Year Electricity And Warmth + Eneco Voordeel-Momenten',
            displayName: '1 Year Electricity And Warmth + Eneco Voordeel-Momenten',
            fields: {
              data: {
                name: {
                  value: '1 Year Electricity And Warmth + Eneco Voordeel-Momenten',
                },
                usp1Text: {
                  value: 'Vast jaartarief voor stroom en warmth',
                },
                id: {
                  value: 'TimeOfUse-1-y-electricity-1-y-warmth',
                },
                usp3Text: {
                  value: 'Na 1 jaar vrij opzeggen',
                },
                usp2Text: {
                  value: 'Dagelijks 30% korting op stroom tussen 10:00 uur - 17:00 uur en tussen 22:00 uur - 5:00 uur',
                },
              },
            },
          },
          {
            id: '55e5253a-8fed-4112-813d-f1d6815a1770',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-6-motths-gas/',
            name: '1 Year Electricity + Eneco Voordeel-Momenten',
            displayName: '1 Year Electricity + Eneco Voordeel-Momenten',
            fields: {
              data: {
                name: {
                  value: '1 Year Electricity + Eneco Voordeel-Momenten',
                },
                usp1Text: {
                  value: 'Vast jaartarief voor stroom',
                },
                id: {
                  value: 'TimeOfUse-1-y-electricity',
                },
                usp3Text: {
                  value: 'Na 1 jaar vrij opzeggen',
                },
                usp2Text: {
                  value: 'Dagelijks 30% korting op stroom tussen 10:00 uur - 17:00 uur en tussen 22:00 uur - 5:00 uur',
                },
              },
            },
          },
        ],
      },
      giftData: {
        giftProductDataList: [
          {
            id: 'c616daa6-d2c1-4efb-90c8-595481469c32',
            url: 'https://www.dev-dxp.eneco.nl/library/incentive-product-data-library-folder/gamma-giftcard-150/',
            name: 'Gamma Giftcard 150',
            displayName: 'Gamma Giftcard 150',
            fields: {
              content: {
                title: {
                  value: 'Gamma cadeaukaart 150',
                },
                descriptionContent: {
                  value: 'Een gamma cadeaukaart is een super <a href="/">incentive</a>',
                },
              },
              gift: {
                giftId: {
                  value: 'Gamma-Giftcard-150',
                },
              },
            },
          },
          {
            id: 'ac9e512a-3396-4506-ab1d-a43564baf52a',
            url: 'https://www.dev-dxp.eneco.nl/library/incentive-product-data-library-folder/gamma-giftcard-50/',
            name: 'Gamma Giftcard 50',
            displayName: 'Gamma Giftcard 50',
            fields: {
              content: {
                title: {
                  value: 'Gamma cadeaukaart 150',
                },
                descriptionContent: {
                  value: 'Een gamma cadeaukaart is een super <a href="/">incentive</a>',
                },
              },
              gift: {
                giftId: {
                  value: 'Gamma-Giftcard-50',
                },
              },
            },
          },
        ],
        giftExplanationTriggerText: {
          value: 'meer info',
        },
      },
      promotionTexts: {
        cashbackOnYearNoteRibbonContent: {
          value: '{cashBackOnYearNote} Cashback On Year Note',
        },
        cashbackDirectRibbonContent: {
          value: '{cashBackDirect} Cashback Direct',
        },
        cashbackDirectLabelContent: {
          value: "Eenmalige Cashback <a href='...'>(meer info)</a>",
        },
        cashbackOnYearNoteLabelContent: {
          value: "Eenmalige Cashback <a href='...'>(meer info)</a>",
        },
        cashbackDirectExplanationContent: {
          value:
            'Quasi id hic dolorem dignissimos ut possimus expedita. Aliquid mollitia cum aut et facere. Sint aperiam tenetur at aperiam.',
        },
        cashbackOnYearNoteExplanationContent: {
          value:
            'Quasi id hic dolorem dignissimos ut possimus expedita. Aliquid mollitia cum aut et facere. Sint aperiam tenetur at aperiam.',
        },
        cashbackExplanationTriggerText: {
          value: 'meer info',
        },
      },
      unitPriceLabels: {
        electricityHighLabel: {
          value: 'Stroom (normaal)',
        },
        electricityLowLabel: {
          value: 'Stroom (dal)',
        },
        electricityLabel: {
          value: 'Stroom',
        },
        redeliveryLabel: {
          value: 'Teruglevering',
        },
        gasLabel: {
          value: 'Gas',
        },
        warmthLabel: {
          value: 'Warmte',
        },
        waterColdLabel: {
          value: 'Water (koud)',
        },
        waterWarmLabel: {
          value: 'Water (warm)',
        },
      },
    },
    datasourceRequired: true,
  },
];

export default mockupData;
