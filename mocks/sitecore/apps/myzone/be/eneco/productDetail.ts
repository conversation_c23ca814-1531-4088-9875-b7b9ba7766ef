import { MyEnecoProductDetailsRendering } from '@sitecore/types/MyEnecoProductDetails';

const productDetail: MyEnecoProductDetailsRendering = {
  uid: '15e39cbc-21ac-4eb8-9282-aa600e431329',
  componentName: 'MyEnecoProductDetails',
  dataSource: '{C710B5C6-199A-4096-A4E3-79BD894161F2}',
  params: {},
  datasourceRequired: false,
  fields: {
    meterCard: {
      title: { value: 'Meter' },
      typesList: {
        value: {
          enum: [
            { name: 'Analogue', value: 'Analogue', label: 'Analoog' },
            { name: 'Digital', value: 'Digital', label: 'Digitaal' },
            { name: 'DigitalNightExclusive', value: 'DigitalNightExclusive', label: 'Digitaal exclusief nacht' },
          ],
        },
      },
      serviceComponentTypesList: {
        value: {
          enum: [
            { name: 'PureOfftake', value: 'PureOfftake', label: 'Afname' },
            { name: 'PureInjection', value: 'PureInjection', label: 'Injectie' },
            { name: 'Compensation', value: 'Compensation', label: 'Compensatie' },
            { name: 'ValorisationInjection', value: 'ValorisationInjection', label: 'Valorisatie injectie' },
            {
              name: 'CommercialisationConstraintInjection',
              value: 'CommercialisationConstraintInjection',
              label: 'CommercialisationConstraintInjection',
            },
            {
              name: 'CommercialisationInjection',
              value: 'CommercialisationInjection',
              label: 'CommercialisationInjection',
            },
          ],
        },
      },
      productTypesList: {
        value: {
          enum: [
            { name: 'Dynamic', value: 'Dynamic', label: 'Dynamic' },
            { name: 'Variable', value: 'Variable', label: 'Variable' },
            { name: 'Fixed', value: 'Fixed', label: 'Fixed' },
          ],
        },
      },
      meterRegimeTypesList: {
        value: {
          enum: [
            { name: 'NotApplicable', value: 'NotApplicable', label: 'Not Applicable' },
            { name: 'Smr1', value: 'Smr1', label: 'Smart meter 1' },
            { name: 'Smr3', value: 'Smr3', label: 'Smart meter 3' },
          ],
        },
      },
      typeLabel: { value: 'Type meter' },
      eanFormField: {
        value: {
          label: 'EAN code',
          hint: '',
          placeholder: '',
          requiredMessage: '',
          validationMessage: '',
        },
      },
      eanInfoPopoverText: { value: 'Dit is een EAN code' },
      meterNumberFormField: {
        value: {
          label: 'Meternummer',
          hint: '',
          placeholder: '',
          requiredMessage: '',
          validationMessage: '',
        },
      },
      serviceLabel: { value: 'Service' },
      meterRegimeLabel: { value: 'Meetregime' },
      meterRegimeChangeRequestPendingDescription: { value: 'in aanvraag' },
      meterRegimePopoverText: { value: 'Dit is een meetregime' },
      meterUpdateLink: {
        value: {
          href: '/nl/my-eneco/products/details/meter-regime/edit',
          text: 'Meetregime wijzigen',
          anchor: '',
          linktype: '',
          class: '',
          title: '',
          target: '',
          querystring: '',
          id: '',
          url: '',
        },
      },
    },
    tariffCard: {
      cancelProductSwitchLink: {
        value: {
          href: '/',
          text: 'Product wijzigen',
          anchor: '',
          linktype: '',
          class: '',
          title: '',
          target: '',
          querystring: '',
          id: '',
          url: '',
        },
      },
      cancelProductSwitchErrorNotification: {
        value: {
          title: 'Product switch verwijderen',
          text: 'Er is een fout opgetreden bij het verwijderen van de product switch',
          variant: 'error',
        },
      },
      cancelProductSwitchSuccessNotification: {
        value: {
          title: 'Product switch verwijderen',
          text: 'Product switch verwijderd',
          variant: 'success',
        },
      },
      title: { value: 'Tarief' },
      productNameFormField: {
        value: {
          label: 'Product',
          hint: '',
          placeholder: '',
          requiredMessage: '',
          validationMessage: '',
        },
      },
      promotionFormField: {
        value: {
          label: 'Kortingen',
          hint: '',
          placeholder: '',
          requiredMessage: '',
          validationMessage: '',
        },
      },
      currentTariffFormField: {
        value: {
          label: 'Huidige tariefkaart - sinds',
          hint: '',
          placeholder: '',
          requiredMessage: '',
          validationMessage: '',
        },
      },
      currentTariffAddendumLinkText: { value: 'Addendum' },
      expiredTariffCardLabel: { value: 'Verlopen sinds' },
      currentTariffLink: {
        value: {
          href: '/',
          text: 'Bekijk tariefkaart',
          anchor: '',
          linktype: '',
          class: '',
          title: '',
          target: '',
          querystring: '',
          id: '',
          url: '',
        },
      },
      nextTariffFormField: {
        value: {
          label: 'Je product wordt vernieuwd op ',
          hint: '',
          placeholder: '',
          requiredMessage: '',
          validationMessage: '',
        },
      },
      nextTariffAddendumLinkText: { value: 'Addendum' },
      nextTariffLink: {
        value: {
          href: '/',
          text: 'Bekijk de volgende tariefkaart',
          anchor: '',
          linktype: '',
          class: '',
          title: '',
          target: '',
          querystring: '',
          id: '',
          url: '',
        },
      },
      productUpdateLink: {
        value: {
          href: '/nl/my-eneco/products/switch/',
          text: 'Mijn product wijzigen',
          anchor: '',
          linktype: '',
          class: '',
          title: '',
          target: '',
          querystring: '',
          id: '',
          url: '',
        },
      },
    },
    productDetails: {
      locationFormField: {
        value: {
          label: 'Verbruiksadres',
          hint: '',
          placeholder: '',
          requiredMessage: '',
          validationMessage: '',
        },
      },
    },
    gridOperator: {
      title: { value: 'Fluvis' },
      helpText: { value: 'Fluvis' },
      label: { value: 'Jouw netbeheerder' },
    },
    productCardStatus: {
      productStatusesList: {
        value: {
          enum: [
            { label: 'active', name: 'Active', value: 'Active' },
            { label: 'InProgress', name: 'InProgress', value: 'InProgress' },
            { label: 'RequestedTermination', name: 'RequestedTermination', value: 'RequestedTermination' },
            { label: 'WaitingForApproval', name: 'WaitingForApproval', value: 'WaitingForApproval' },
            { label: 'WaitingForDelivery', name: 'WaitingForDelivery', value: 'WaitingForDelivery' },
            { label: 'Terminated', name: 'None', value: 'None' },
          ],
        },
      },
    },
    productCardDetails: {
      inProgressPopoverText: { value: '' },
      inProgressText: { value: 'in progress' },
      waitingForApprovalPopoverText: { value: 'in afwachting voor goedkeuring' },
      waitingForApprovalText: { value: 'in afwachting voor goedkeuring' },
      undeterminedTerminationText: { value: 'Product van onbepaalde tijd' },
      undeterminedTerminationPopoverText: { value: 'Product van onbepaalde tijd' },
      terminatesAtText: { value: 'Je product gaat stoppen op {date}' },
      terminatedAtText: { value: 'Je product is beëindigd op {date}' },
      terminatesAtPopoverText: { value: 'Je product is beëindigd op {date}' },
      terminatedAtPopoverText: { value: 'Je product is beëindigd op {date}' },
      startsAtText: { value: 'Product start op {date}' },
      startsAtPopoverText: { value: 'Je product start op {date}' },
      energyTypeElectricityText: { value: 'Elektriciteit' },
      energyTypeGasText: { value: 'Gas' },
      link: {
        value: {
          href: '/my-eneco/products/details',
          text: 'Bekijk details',
          anchor: '',
          linktype: '',
          class: '',
          title: '',
          target: '',
          querystring: '',
          id: '',
          url: '',
        },
      },
    },
  },
};
export default productDetail;
