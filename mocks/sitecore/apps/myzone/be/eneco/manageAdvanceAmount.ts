import { ManageAdvanceAmountRendering } from '@sitecore/types/ManageAdvanceAmount';

const manageAdvanceAmount: ManageAdvanceAmountRendering = {
  componentName: 'ManageAdvanceAmount',
  dataSource: '{5CD09EAB-1A2F-47A2-9175-9EEFAFE00DA9}',
  datasourceRequired: false,
  fields: {
    advanceCard: {
      acceptRecommendedAmountLink: {
        value: {
          anchor: '',
          class: '',
          href: '',
          id: '',
          linktype: '',
          querystring: '',
          target: '',
          text: 'Advies overnemen',
          title: '',
          url: '/',
        },
      },
      currencySignLabel: { value: '€' },
      currentAmountTitle: { value: 'Je betaalt momenteel' },
      eachMonthLabel: { value: 'per maand' },
      editAdvanceAmountLink: {
        value: {
          anchor: '',
          class: '',
          href: '/my-eneco/voorschotbedrag/edit',
          id: '',
          linktype: '',
          querystring: '',
          target: '',
          text: 'Wijzigen',
          title: '',
          url: '',
        },
      },
      recommendedAmountTooHighDisclaimerLabel: {
        value: 'Zonder het advies te volgen, verwachten we dat je {amount} moet bij betalen bij je jaarafrekening(en).',
      },
      recommendedAmountTooLowDisclaimerLabel: {
        value: 'Zonder het advies te volgen, verwachten we dat je {amount} terugkrijgt bij je jaarafrekening(en).',
      },
      recommendedAmountTitle: { value: 'Ons voorstel' },
      title: { value: 'Huidig totaal voorschotbedrag' },
    },
    advanceAmountImpact: {
      eanLabel: { value: 'EAN-code' },
      title: { value: 'Impact op voorschotbedrag' },
    },
    commodityCard: {
      advanceAmountLabel: { value: 'Voorschotbedrag' },
      advanceAmountLink: {
        value: {
          anchor: '',
          class: '',
          href: '',
          id: '',
          linktype: '',
          querystring: '',
          target: '',
          text: 'Bekijk bedrag',
          title: 'Bekijk huidige voorschotbedrag',
          url: '/',
        },
      },
      advanceOkNotification: {
        value: {
          content: 'Je huidige voorschotbedrag is in lijn met je verwacht verbruik.',
          title: 'Voorschotbedrag is passend',
          variant: 'success',
        },
      },
      advanceTooHighNotification: {
        value: {
          content: 'Zonder het advies te volgen, verwachten we dat je {amount} terugkrijgt bij je jaarafrekening.',
          title: 'Bedrag te hoog',
          variant: 'warning',
        },
      },
      advanceTooLowNotification: {
        value: {
          content: 'Zonder het advies te volgen, verwachten we dat je {amount} moet betalen bij je jaarafrekening.',
          title: 'Bedrag te laag',
          variant: 'warning',
        },
      },
      changeAdvanceAmountNotification: {
        value: {
          content:
            'Aanbevolen voorschotbedrag overgenomen. Zodra deze verwerkt is, vind je deze terug op je eerstvolgende voorschotfactuur.',
          title: 'Voorschotbedrag aangepast',
          variant: 'success',
        },
      },
      changeAmountLink: {
        value: {
          anchor: '',
          class: '',
          href: '',
          id: '',
          linktype: '',
          querystring: '',
          target: '',
          text: 'Bedrag wijzigen',
          title: 'Pas je voorschot aan',
          url: '/',
        },
      },
      eanLabel: { value: 'EAN-code' },
      editLink: {
        value: {
          anchor: '',
          class: '',
          href: '/',
          id: '',
          linktype: '',
          querystring: '',
          target: '',
          text: 'Aanpassen',
          title: '',
          url: '/',
        },
      },
      electricityLabel: { value: 'Elektriciteit' },
      energyTypesList: {
        value: {
          enum: [
            {
              label: 'Elektriciteit',
              name: 'electricity',
              value: 'electricity',
              description: 'Verbruik van elektriciteit',
            },
            {
              label: 'Gas',
              name: 'gas',
              value: 'gas',
              description: 'Verbruik van gas',
            },
          ],
        },
      },
      gasLabel: { value: 'Gas' },
      monthlyActualUsageBillNotification: {
        value: {
          content: 'We baseren ons voorstel op je maandelijkse verbruik.',
          title: 'Actueel verbruik',
          variant: 'info',
        },
      },
      recommendedAdvanceAmountNotification: {
        value: {
          content: '',
          title: 'Aanbevolen voorschotbedrag overgenomen',
          variant: 'success',
        },
      },
      title: { value: 'Voorschot per energiebron' },
    },
    data: {
      addressNotDeliveredNotification: {
        value: {
          content: 'We hebben momenteel geen leveringsinformatie voor dit adres.',
          title: 'Adres niet geleverd',
          variant: 'info',
        },
      },
      addressSelectLabel: { value: 'Verbruiksadres' },
      advanceUnknownNotification: {
        value: {
          content: 'We kunnen je voorschotbedrag momenteel niet berekenen.',
          title: 'Het voorschotbedrag en aanbevolen voorschotbedrag is nog niet gekend.',
          variant: 'info',
        },
      },
      contractStartingSoonNotification: {
        value: {
          content:
            'Wanneer je belevering van Eneco start, zullen we je aanbevolen voorschotbedrag berekenen op basis van de verbruiksinformatie die wij ontvangen van je netbeheerder.',
          title: 'Je aanbevolen voorschotbedrag kan binnenkort wijzigen',
          variant: 'info',
        },
      },
      dunningLevelTooHighNotification: {
        value: {
          content: 'Je betalingsniveau is te hoog. Neem contact op met onze klantendienst.',
          title: 'Betalingsachterstand',
          variant: 'warning',
        },
      },
      monthlyInvoicedNotification: {
        value: {
          content: 'Het is daarom niet mogelijk om het voorschotbedrag in te stellen.',
          title: 'Je ontvangt maandelijks een afrekening op basis van je reëel verbruik',
          variant: 'info',
        },
      },
      newCustomerNotification: {
        value: {
          content:
            'Nadat je Eneco-klant werd kan het door ons voorgestelde voorschotbedrag afwijken van het voorschotbedrag in je offerte.',
          title: 'Een ander voorschotbedrag?',
          variant: 'info',
        },
      },
      noActiveDeliveryNotification: {
        value: {
          content: 'Er is momenteel geen actieve levering op dit adres.',
          title: 'Geen actieve levering',
          variant: 'info',
        },
      },
      recommendedAdvanceNotAvailableNotification: {
        value: {
          content: 'Je zal binnenkort je jaarlijkse afrekening ontvangen.',
          title: 'Aanbevolen voorschotbedrag momenteel niet beschikbaar',
          variant: 'info',
        },
      },
      title: { value: 'Voorschotbedrag' },
    },
    vacancyCard: {
      activeOwnerDescription: {
        value:
          '<p>Met een leegstandscontract verlagen we je maandelijkse voorschot naar € 10 per meter. Dit zorgt ervoor dat je huis nog steeds energie heeft om werkmannen te voorzien van energie. Moest je voor een lange periode niet in het huis wonen vermijd je zo ook schimmel of schade aan de leidingen.</p><p>Je huidige contract wordt gewoon behouden. </p>',
      },
      activeUntilDescription: { value: 'Dit leegstandscontract is actief tot 01-12-2025.' },
      manageVacancyLink: {
        value: {
          anchor: '',
          class: '',
          href: '',
          id: '',
          linktype: '',
          querystring: '',
          target: '',
          text: 'Leegstandscontract beheren',
          title: '',
          url: '/',
        },
      },
      title: { value: 'Leegstand' },
    },
  },
  params: {},
  uid: '',
};

export default manageAdvanceAmount;
