import { PremiumServiceDetailsRendering } from '@sitecore/types/PremiumServiceDetails';

const premiumServiceDetails: PremiumServiceDetailsRendering = {
  componentName: 'PremiumServiceDetails',
  dataSource: '',
  datasourceRequired: false,
  params: {},
  uid: '',
  fields: {
    dongleConnectionLabel: { value: 'Verbinding' },
    dongleStatusLabelList: {
      value: {
        enum: [
          {
            name: 'Error',
            value: 'Error',
            label: 'Connectieprobleem',
          },
          {
            name: 'InSupply',
            value: 'InSupply',
            label: 'Actief',
          },
          {
            name: 'OutOfSupply',
            value: 'OutOfSupply',
            label: 'Nog niet verbonden',
          },
          {
            name: 'Pairing',
            value: 'Pairing',
            label: 'Nog niet verbonden',
          },
          {
            name: 'ReadyForSupply',
            value: 'ReadyForSupply',
            label: 'Nog niet verbonden',
          },
          {
            name: 'Unknown',
            value: 'Unknown',
            label: 'Nog niet verbonden',
          },
        ],
      },
    },
    dongleConnectionLink: {
      value: {
        href: '/?item=%2Fprofile%2Fconnections',
        text: 'Eneco dongle',
        anchor: '',
        linktype: 'internal',
        class: '',
        title: '',
        querystring: '',
        id: '',
        target: '',
        url: '',
      },
    },
    featureOption: { value: 'smart-insights' },
    descriptionText: {
      value: 'Helpt je altijd je verbruik live op te volgen. Zo weet je exact wat je verbruikt',
    },
    periodicityLabel: { value: ' /maand' },
    detailsLabel: { value: 'Details' },
    statusText: { value: 'Status' },
    statusLabelList: {
      value: {
        enum: [
          {
            name: 'active',
            value: 'active',
            label: 'Actief',
          },
          {
            name: 'nonrenewing',
            value: 'nonrenewing',
            label: 'Eindigt binnenkort',
          },
          {
            name: 'cancelled',
            value: 'cancelled',
            label: 'Opgezegd',
          },
          {
            name: 'unknown',
            value: 'unknown',
            label: 'Onbekend',
          },
        ],
      },
    },
    startDateLabel: { value: 'Startdatum' },
    endDateLabel: { value: 'Einddatum' },
    noEndDateLabel: { value: 'Geen' },
    subscriptionIdLabel: { value: 'Abonnement ID' },
    manageSubscriptionButtonText: { value: 'Abonnement beheren' },
    undoCancellationLink: {
      value: {
        href: `/?item=%2Flaunchpad%2Fdongle`,
        text: 'Annulatie ongedaan maken',
        target: '',
        url: '',
        anchor: '',
        linktype: 'internal',
        class: '',
        title: '',
        querystring: '',
        id: '{1D891232-DD57-4323-8367-789877C16AE9}',
      },
    },
    partnerPortalDialog: {
      value: {
        title: 'Beheer je premium diensten',
        content: 'Je wordt doorgestuurd naar ons partnerportaal om je abonnement te beheren',
        cancelButtonText: 'Annuleren',
        submitButtonText: 'Naar het partnerportaal',
        triggerText: '',
      },
    },
  },
};

export default premiumServiceDetails;
