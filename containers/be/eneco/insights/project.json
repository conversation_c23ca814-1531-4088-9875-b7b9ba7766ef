{"name": "be-eneco-insights", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "containers/be/eneco/insights", "projectType": "application", "tags": ["container:next-native"], "implicitDependencies": ["mocks"], "targets": {"copy-statics": {"executor": "nx:run-commands", "options": {"command": "npx rsyncjs --quiet --deleteOrphaned libs/sparky/static containers/be/eneco/insights/public"}}, "build": {"dependsOn": [{"target": "copy-statics"}], "executor": "@nx/next:build", "options": {"outputPath": "dist/containers/be/eneco/insights"}}, "serve-build": {"dependsOn": [{"target": "copy-statics"}], "executor": "@nx/next:build", "options": {"outputPath": "dist/containers/be/eneco/insights"}, "configurations": {"development": {"outputPath": "containers/be/eneco/insights", "fileReplacements": [{"replace": "libs/dc/src/client/request-browser.ts", "with": "libs/dc/src/client/request-native.ts"}, {"replace": "libs/sitecore/client/src/browser.ts", "with": "libs/sitecore/client/src/native.ts"}, {"replace": "mocks/worker/index.tsx", "with": "mocks/worker/index.dev.tsx"}, {"replace": "containers/be/eneco/insights/pages/mobile.tsx", "with": "containers/be/eneco/insights/src/pages/mobile-web.tsx"}]}}}, "serve": {"executor": "@nx/next:server", "dependsOn": [{"target": "copy-statics"}], "options": {"buildTarget": "be-eneco-insights:serve-build:development", "port": 4304, "dev": true}}, "export": {"dependsOn": [{"target": "copy-statics"}], "executor": "@nx/next:build", "options": {"root": "containers/be/eneco/insights", "outputPath": "dist/containers/be/eneco/insights", "fileReplacements": [{"replace": "libs/dc/src/client/request-browser.ts", "with": "libs/dc/src/client/request-native.ts"}, {"replace": "libs/sitecore/client/src/browser.ts", "with": "libs/sitecore/client/src/native.ts"}]}}, "export-web": {"dependsOn": [{"target": "copy-statics"}], "executor": "@nx/next:build", "options": {"root": "containers/be/eneco/insights", "outputPath": "dist/containers/be/eneco/insights", "fileReplacements": [{"replace": "libs/dc/src/client/request-browser.ts", "with": "libs/dc/src/client/request-native.ts"}, {"replace": "containers/be/eneco/insights/pages/mobile.tsx", "with": "containers/be/eneco/insights/src/pages/mobile-web.tsx"}, {"replace": "libs/sitecore/client/src/browser.ts", "with": "libs/sitecore/client/src/native.ts"}]}}, "cap": {"executor": "nx:run-commands", "options": {"command": "npx cap --help", "cwd": "containers/be/eneco/insights"}}, "add": {"executor": "nx:run-commands", "options": {"command": "npx cap add", "cwd": "containers/be/eneco/insights"}, "configurations": {"ios": {"command": "npx cap add ios"}, "android": {"command": "npx cap add android"}}}, "run": {"executor": "nx:run-commands", "options": {"command": "npx cap run", "cwd": "containers/be/eneco/insights"}, "configurations": {"ios": {"command": "npx cap run ios"}, "android": {"command": "npx cap run android"}}}, "sync": {"executor": "nx:run-commands", "options": {"commands": ["npx cap sync"], "cwd": "containers/be/eneco/insights", "parallel": false}, "configurations": {"ios": {"commands": ["npx cap sync ios"]}, "android": {"commands": ["npx cap sync android"]}}}, "update": {"executor": "nx:run-commands", "options": {"command": "npx cap update", "cwd": "containers/be/eneco/insights"}, "configurations": {"ios": {"command": "npx cap update ios"}, "android": {"command": "npx cap update android"}}}, "copy": {"executor": "nx:run-commands", "options": {"command": "npx cap copy", "cwd": "containers/be/eneco/insights"}, "configurations": {"ios": {"command": "npx cap copy ios"}, "android": {"command": "npx cap copy android"}}}, "open": {"executor": "nx:run-commands", "options": {"command": "npx cap open", "cwd": "containers/be/eneco/insights"}, "configurations": {"ios": {"command": "npx cap open ios"}, "android": {"command": "npx cap open android"}}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["containers/be/eneco/insights/**/*.{ts,tsx}"]}}}}