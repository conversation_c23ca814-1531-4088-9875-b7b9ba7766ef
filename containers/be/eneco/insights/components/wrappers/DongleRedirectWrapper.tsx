import { FC, PropsWithChildren, useEffect } from 'react';

import { useRouter } from '@dxp-next';
import { InsightsNativePaths } from '@native-components/constants/paths';

const { P1_DONGLE_ONBOARDING_PATH } = InsightsNativePaths;

const DongleRedirectWrapper: FC<PropsWithChildren> = ({ children }) => {
  const router = useRouter();

  useEffect(() => {
    router.push(P1_DONGLE_ONBOARDING_PATH);
  }, [router]);

  return children;
};

export default DongleRedirectWrapper;
