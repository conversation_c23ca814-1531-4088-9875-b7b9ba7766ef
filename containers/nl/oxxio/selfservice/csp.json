{"directives": {"default-src": ["self"], "media-src": ["self"], "script-src": ["self", "unsafe-inline", "unsafe-eval"], "connect-src": ["self"], "img-src": ["self"], "font-src": ["self"], "style-src": ["self", "unsafe-inline"], "frame-src": ["self"], "child-src": ["self"], "object-src": ["none"]}, "protocols": {"connect-src": ["wss"], "img-src": ["data"], "child-src": ["blob"]}, "sources": [{"name": "Adcalls nl", "category": "GTM", "connect-src": "api.adcalls.nl", "script-src": "script.adcalls.nl/e907d5da-14dc-4967-b180-03e37a3022be.js"}, {"name": "Application Insights", "category": "Application Insights", "connect-src": ["*.in.applicationinsights.azure.com", "js.monitor.azure.com/scripts/b/ai.config.1.cfg.json"]}, {"name": "AppNexus", "category": "GTM", "script-src": "acdn.adnxs.com/dmp/up/pixie.js", "img-src": "ib.adnxs.com", "connect-src": "self"}, {"name": "AzureEdgeNet", "category": "GTM", "script-src": "mktdplp102cdn.azureedge.net"}, {"name": "Bing ads", "category": "GTM", "script-src": ["bat.bing.com", "www.clarity.ms/tag/uet/23001835"], "img-src": "bat.bing.com", "connect-src": ["bat.bing.com", "bat.bing.net"]}, {"name": "CDN", "category": "CDN", "script-src": {"production": "cdn-dxp.enecogroup.com", "acceptance": "cdn-dxp.acc.enecogroup.com", "test": "cdn-dxp.test.enecogroup.com", "development": "*.enecogroup.com"}, "img-src": {"production": "cdn-dxp.enecogroup.com", "acceptance": "cdn-dxp.acc.enecogroup.com", "test": "cdn-dxp.test.enecogroup.com", "development": "*.enecogroup.com"}, "font-src": {"production": "cdn-dxp.enecogroup.com", "acceptance": "cdn-dxp.acc.enecogroup.com", "test": "cdn-dxp.test.enecogroup.com", "development": "*.enecogroup.com"}}, {"name": "CDN Frontify", "category": "CDN", "img-src": "*.frontify.com"}, {"name": "Conversationals", "category": "Conversationals", "script-src": {"production": "cdn.conversationalsdevelopment.nl", "acceptance": "cdn-test.conversationalsdevelopment.nl", "test": "cdn-test.conversationalsdevelopment.nl", "development": "*.conversationalsdevelopment.nl"}, "media-src": ["cdn.conversationalsdevelopment.nl/oxxio/client/v3/sounds/beep.mp3"]}, {"name": "Conversationals", "category": "Conversationals", "script-src": "api.seamly-app.com", "connect-src": {"production": "api.seamly-app.com", "acceptance": "api.uat.seamly-app.com", "test": "api.uat.seamly-app.com"}}, {"name": "Daisycon", "category": "GTM", "img-src": ["lt45.net", "www.lt45.net", "www.rkn3.net", "rkn3.net", "ds1.nl", "www.ds1.nl"]}, {"name": "DC", "category": "DC", "connect-src": {"production": "api-digital.enecogroup.com", "acceptance": "acc.api-digital.enecogroup.com", "test": "acc.api-digital.enecogroup.com", "development": "*.enecogroup.com"}}, {"name": "DoubleClick", "category": "GTM", "img-src": ["*.fls.doubleclick.net", "ad.doubleclick.net", "*.g.doubleclick.net"], "frame-src": ["*.fls.doubleclick.net", "bid.g.doubleclick.net", "td.doubleclick.net"], "script-src": "*.g.doubleclick.net", "connect-src": ["ad.doubleclick.net", "stats.g.doubleclick.net"]}, {"name": "Dynamics", "category": "GTM", "script-src": "svc.dynamics.com"}, {"name": "Dynatrace", "category": "Dynatrace", "connect-src": "bf53370xjr.bf.dynatrace.com"}, {"name": "Facebook ads", "category": "GTM", "script-src": "connect.facebook.net", "img-src": "www.facebook.com"}, {"name": "GA", "category": "GTM", "docs": "https://developers.google.com/tag-platform/tag-manager/web/csp", "script-src": "www.google-analytics.com", "connect-src": ["*.google-analytics.com", "*.analytics.google.com"], "img-src": ["*.google-analytics.com", "*.analytics.google.com"]}, {"name": "Google ads", "category": "GTM", "script-src": ["www.googleadservices.com", "www.google.nl", "pagead2.googlesyndication.com"], "img-src": ["googleads.g.doubleclick.net", "www.google.com", "www.google.nl", "ade.googlesyndication.com", "adservice.google.com", "*.adservice.google.com", "*.googleadservices.com"], "connect-src": ["pagead2.googlesyndication.com", "www.google.com"]}, {"name": "Google Recaptcha", "category": "Google Recaptcha", "script-src": ["www.google.com/recaptcha/api.js", "www.gstatic.com"], "frame-src": "www.google.com"}, {"name": "Google STM Oxxio", "category": "GTM", "connect-src": {"production": "stm.oxxio.nl", "acceptance": "stm.acc.oxxio.nl"}}, {"name": "GTM", "category": "GTM", "docs": "https://developers.google.com/tag-platform/tag-manager/web/csp", "script-src": "*.googletagmanager.com", "connect-src": "www.googletagmanager.com", "font-src": "fonts.gstatic.com", "img-src": ["www.googletagmanager.com", "ssl.gstatic.com", "www.gstatic.com"]}, {"name": "<PERSON><PERSON>", "category": "GTM", "docs": "https://help.hotjar.com/hc/en-us/articles/115011640307-Content-Security-Policies", "script-src": "*.hotjar.com", "connect-src": ["*.hotjar.com", "vc.hotjar.io", "content.hotjar.io"], "img-src": "*.hotjar.com", "font-src": "*.hotjar.com", "style-src": "*.hotjar.com", "frame-src": "vars.hotjar.com"}, {"name": "LemonPi", "category": "GTM", "script-src": "pixels.lemonpi.io", "connect-src": "d.lemon<PERSON>.io", "img-src": "d.lemon<PERSON>.io"}, {"name": "Linkedin Ads", "category": "GTM", "script-src": ["snap.licdn.com/li.lms-analytics/insight.min.js", "snap.licdn.com/li.lms-analytics/insight.beta.min.js", "snap.licdn.com/li.lms-analytics/insight.old.min.js"], "connect-src": ["px.ads.linkedin.com"], "img-src": ["px.ads.linkedin.com", "www.linkedin.com", "px4.ads.linkedin.com"]}, {"name": "Pinterest", "category": "GTM", "script-src": ["s.pinimg.com", "ct.pinterest.com"], "connect-src": "ct.pinterest.com", "img-src": "ct.pinterest.com", "frame-src": "ct.pinterest.com"}, {"name": "Reddit", "category": "GTM", "script-src": ["www.reddit.com", "ads.reddit.com", "www.redditstatic.com"], "connect-src": ["*.reddit.com", "www.redditstatic.com", "conversions-config.reddit.com"], "img-src": ["www.reddit.com", "ads.reddit.com", "alb.reddit.com"]}, {"name": "Salesfeed", "category": "GTM", "script-src": "api.salesfeed.com"}, {"name": "SegmentStream", "category": "GTM", "script-src": "*.segmentstream.com", "connect-src": "track.segmentstream.com"}, {"name": "Twitter", "category": "GTM", "script-src": "static.ads-twitter.com", "connect-src": ["analytics.twitter.com", "t.co", "ads-twitter.com", "ads-api.twitter.com"], "img-src": ["analytics.twitter.com", "t.co", "ads-twitter.com", "ads-api.twitter.com"]}, {"name": "<PERSON><PERSON><PERSON>", "category": "GTM", "script-src": ["w.usabilla.com", "api.usabilla.com", "d6tizftlrpuof.cloudfront.net"], "connect-src": "api.usabilla.com", "img-src": ["w.usabilla.com", "d6tizftlrpuof.cloudfront.net"], "font-src": "d6tizftlrpuof.cloudfront.net", "style-src": "d6tizftlrpuof.cloudfront.net", "frame-src": "d6tizftlrpuof.cloudfront.net"}, {"name": "Youtube", "category": "Sitecore", "frame-src": "*.youtube.com", "img-src": ["img.youtube.com"]}]}