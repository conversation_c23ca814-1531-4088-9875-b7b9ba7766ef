{"name": "eneco-frontend-2021", "version": "0.0.0", "private": true, "repository": {"type": "git", "url": "https://github.com/eneco-online/eneco-dxp-frontend"}, "license": "MIT", "scripts": {"postinstall": "node ./scripts/copy-example-env.mjs && lefthook install && patch-package", "build": "nx run-many --target=build --all --exclude=nl-eneco-sandbox", "test": "nx run-many --target=test --all", "lint": "nx run-many --target=lint --all --exclude=nl-eneco-sandbox", "tsc": "nx run-many --target=tsc --all --exclude=nl-eneco-sandbox", "generate:types:sitecore": "tsx scripts/generate-types-sitecore", "download:types:dc": "dotenv -- tsx scripts/download-types-dc", "generate:types:dc": "dotenv -- tsx scripts/generate-types-dc", "generate:types:dc-be": "dotenv -- tsx scripts/generate-types-dc-be", "generate:types:dc:agentportal": "tsx containers/nl/internal/agentportal/util/generate-types-dc-agentportal.ts", "verify:dictionaries": "tsx scripts/verify-dictionaries", "azure": "dotenv -- tsx scripts/azure.ts", "uuid": "nanoid --size 6", "switch-env": "node ./scripts/switch-env.mjs"}, "sideEffects": ["e2e/**/commands.ts", "libs/common/src/application-insights/index.ts", "mocks/worker/index.dev.tsx"], "browserslist": ["Chrome >= 87", "Edge >= 87", "Safari >= 12.1", "Samsung >= 13", "Firefox >= 84"], "dependencies": {"@awesome-cordova-plugins/core": "^6.8.0", "@awesome-cordova-plugins/in-app-browser": "^6.8.0", "@azure/msal-browser": "^3.21.0", "@azure/msal-react": "^2.0.22", "@capacitor/android": "^6.0.0", "@capacitor/app": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/geolocation": "^6.0.0", "@capacitor/ios": "^6.0.0", "@capacitor/network": "^6.0.3", "@capacitor/preferences": "^6.0.0", "@capacitor/splash-screen": "^6.0.0", "@capacitor/status-bar": "^6.0.0", "@carbon/react": "^1.77.0", "@cypress/react": "^9.0.1", "@eneco-packages/xstate-custom": "file:packages/xstate-v5", "@evilmartians/lefthook": "^1.11.12", "@growthbook/growthbook-react": "^1.3.0", "@hey-api/client-fetch": "^0.2.4", "@hey-api/openapi-ts": "^0.52.6", "@hookform/resolvers": "^2.9.11", "@ionic/core": "^6.3.5", "@ionic/react": "^6.7.5", "@microsoft/applicationinsights-common": "^3.2.2", "@microsoft/applicationinsights-react-js": "^17.3.0", "@microsoft/applicationinsights-web": "^3.3.0", "@next/bundle-analyzer": "15.3.3", "@nx/devkit": "21.1.2", "@nx/next": "21.1.2", "@nx/react": "21.1.2", "@nx/webpack": "21.1.2", "@openid/appauth": "^1.3.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@sitecore-jss/sitecore-jss-nextjs": "^22.5.4", "@sitecore-jss/sitecore-jss-react": "^22.5.4", "@snyk/github-codeowners": "1.1.0", "@stitches/react": "^1.2.8", "@storybook/addon-a11y": "8.6.12", "@storybook/addon-designs": "^8.2.0", "@storybook/addon-essentials": "8.6.12", "@storybook/addon-links": "8.6.12", "@storybook/preview-api": "^8.6.7", "@storybook/react": "8.6.12", "@storybook/theming": "8.6.12", "@swc/core": "1.7.23", "@swc/jest": "0.2.36", "@testing-library/cypress": "^10.0.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "29.5.14", "@types/jest-axe": "^3.5.9", "@types/node": "^20.14.13", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@types/react-is": "19.0.0", "@types/webpack": "^5.28.5", "@typescript-eslint/parser": "^8.26.1", "@ua/capacitor-airship": "^3.0.1", "@ultraq/icu-message-formatter": "^0.14.3", "@visx/annotation": "3.12.0", "@visx/axis": "^3.12.0", "@visx/glyph": "3.12.0", "@visx/grid": "3.12.0", "@visx/group": "3.12.0", "@visx/legend": "^3.12.0", "@visx/marker": "3.12.0", "@visx/pattern": "3.12.0", "@visx/responsive": "^3.12.0", "@visx/scale": "3.12.0", "@visx/shape": "3.12.0", "@visx/text": "3.12.0", "@visx/tooltip": "3.12.0", "@vitejs/plugin-react": "4.3.4", "@xstate/react": "^3.2.2", "ace-builds": "^1.35.4", "applicationinsights": "^2.9.5", "ariakit": "^2.0.0-next.41", "babel-jest": "29.7.0", "capacitor-native-settings": "^6.0.2", "capacitor-plugin-app-tracking-transparency": "^2.0.5", "chalk": "^4.1.2", "cordova-plugin-inappbrowser": "^6.0.0", "cross-fetch": "^4.0.0", "cypress-image-diff-js": "^2.4.0", "d3-scale": "^4.0.2", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "deepmerge": "^4.3.1", "dotenv-cli": "^7.4.2", "enode-capacitor": "^1.0.7", "focus-trap-react": "^10.2.3", "got": "^11.8.6", "html-react-parser": "^5.2.3", "ibantools": "^4.5.1", "ionic-appauth": "^2.1.0", "jest": "29.7.0", "jest-axe": "^10.0.0", "js-cookie": "^3.0.5", "jsx-ast-utils": "^3.3.5", "jwt-decode": "^4.0.0", "knip": "1.0.0-beta.1", "markdown-rambler": "0.0.23", "msw": "2.3.4", "msw-storybook-addon": "^2.0.3", "nanoid": "^5.1.4", "next": "15.3.3", "next-auth": "^4.24.11", "next-router-mock": "^1.0.2", "next-transpile-modules": "10.0.1", "node-fetch": "^3.3.2", "nx": "21.1.2", "patch-package": "^8.0.0", "path-to-regexp": "^8.0.0", "prettier": "^3.5.3", "react": "19.1.0", "react-ace": "^10.1.0", "react-day-picker": "^9.6.4", "react-dom": "19.1.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.54.2", "react-is": "19.1.0", "react-router-dom": "^7.5.2", "react-to-text": "^2.0.1", "react-use": "^17.6.0", "rehype-urls": "^1.2.0", "rsyncjs": "^0.0.12", "rxjs": "^7.8.1", "semantic-release": "^24.2.3", "semantic-release-commit-filter": "^1.0.2", "smartmeterdongle": "^0.1.5", "swc-loader": "^0.2.6", "swr": "^2.2.5", "ts-morph": "16.0.0", "ts-morph-helpers": "0.3.0", "ts-toolbelt": "^9.6.0", "tsconfig-paths-webpack-plugin": "^4.2.0", "tslib": "^2.6.3", "tsx": "4.8.1", "typescript": "5.2.2", "ua-parser-js": "^2.0.3", "use-deep-compare": "^1.3.0", "vite": "6.3.4", "vite-plugin-dts": "4.5.3", "vite-tsconfig-paths": "^4.3.2", "webpack": "5.98.0", "xstate": "^4.38.3", "yup": "^1.6.1"}, "devDependencies": {"@azure/identity": "^4.4.1", "@azure/keyvault-secrets": "^4.8.0", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@capacitor/cli": "^6.0.0", "@eslint/js": "^9.22.0", "@faker-js/faker": "^8.4.1", "@nx/cypress": "21.1.2", "@nx/eslint": "21.1.2", "@nx/eslint-plugin": "21.1.2", "@nx/jest": "21.1.2", "@nx/js": "21.1.2", "@nx/plugin": "21.1.2", "@nx/storybook": "21.1.2", "@nx/vite": "21.1.2", "@nx/workspace": "21.1.2", "@storybook/addon-docs": "8.6.12", "@storybook/blocks": "8.6.12", "@storybook/core-server": "8.6.12", "@storybook/nextjs": "8.6.12", "@types/d3-scale": "^4.0.8", "@types/react-google-recaptcha": "^2.1.9", "@types/ua-parser-js": "^0.7.39", "@vanilla-extract/dynamic": "^2.1.2", "@vanilla-extract/jest-transform": "^1.1.14", "@vanilla-extract/next-plugin": "^2.4.10", "@vanilla-extract/recipes": "^0.5.5", "@vanilla-extract/vite-plugin": "^5.0.1", "@vanilla-extract/webpack-plugin": "^2.3.18", "commitlint": "^19.8.0", "conventional-changelog-conventionalcommits": "^8.0.0", "cypress": "^14.3.2", "eslint": "^9.22.0", "eslint-config-prettier": "10.1.2", "eslint-import-resolver-typescript": "^4.2.2", "eslint-plugin-cypress": "^4.3.0", "eslint-plugin-dxp-rules": "file:config/eslint/plugins/dxp-rules", "eslint-plugin-import": "2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-storybook": "^0.11.6", "eslint-plugin-testing-library": "^7.1.1", "globby": "^14.0.2", "inquirer": "^12.5.0", "jest-fetch-mock": "^3.0.3", "jest-fixed-jsdom": "^0.0.9", "jiti": "2.4.2", "json-schema": "0.4.0", "json-schema-to-typescript": "^15.0.4", "jsonc-eslint-parser": "^2.4.0", "mini-css-extract-plugin": "^2.9.0", "openapi-types": "^12.1.3", "parse-openapi": "^0.0.1", "remark-gfm": "^4.0.1", "storybook": "^8.6.7", "ts-jest": "^29.2.6", "ts-node": "10.9.1", "typescript-eslint": "^8.27.0", "undici": "^6.21.2"}, "optionalDependencies": {"@nx/nx-linux-x64-gnu": "20.6.0", "@rollup/rollup-linux-x64-gnu": "^4.38.0", "@swc/core-linux-x64-gnu": "^1.11.13", "esbuild-linux-64": "^0.15.18"}, "overrides": {"@sitecore-jss/sitecore-jss-nextjs": {"next": "$next", "react": "$react", "react-dom": "$react-dom"}, "@sitecore-jss/sitecore-jss-react": {"react": "$react", "react-dom": "$react-dom"}, "react": "19.1.0", "react-dom": "19.1.0", "next": "$next", "@nx/eslint-plugin": {"eslint-config-prettier": "$eslint-config-prettier"}}, "engines": {"node": ">=22.6.0", "npm": "^10 <11"}, "msw": {"workerDirectory": "libs/sparky/static"}}